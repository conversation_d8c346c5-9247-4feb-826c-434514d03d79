2025-08-02 19:57:41,313 INFO [20250802_195736_49b43b1f]                 Book.java:562  | 1 sheet in C:\Users\<USER>\Desktop\melody-forge\temp\20250802_195736_49b43b1f.png
2025-08-02 19:57:41,439 INFO []                 Book.java:2531 | Stored /book.xml
2025-08-02 19:57:41,439 INFO []                 Book.java:2491 | Book stored as C:\Users\<USER>\Desktop\melody-forge\output\20250802_195736_49b43b1f.omr
2025-08-02 19:57:41,444 INFO []                 Book.java:1933 | Book reaching PAGE on sheets:[#1]
2025-08-02 19:57:41,445 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | LOAD
2025-08-02 19:57:41,524 INFO [20250802_195736_49b43b1f]            SheetStub.java:1268 | Loaded image #1 1440x1920 from C:\Users\<USER>\Desktop\melody-forge\temp\20250802_195736_49b43b1f.png
2025-08-02 19:57:41,528 INFO [20250802_195736_49b43b1f]            ImageUtil.java:124  | Converting max RGB to gray
2025-08-02 19:57:41,585 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | BINARY
2025-08-02 19:57:41,727 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | SCALE
2025-08-02 19:57:41,740 INFO [20250802_195736_49b43b1f]         ScaleBuilder.java:241  | Beam  guessed height:  6 -- 0.50 of 13 interline
2025-08-02 19:57:41,741 INFO [20250802_195736_49b43b1f]         ScaleBuilder.java:264  | Beam measured height:  7 -- 0.33 of [4..13] range at 369% of needed quorum
2025-08-02 19:57:41,742 INFO [20250802_195736_49b43b1f]            ScaleStep.java:65   | Scale{ interline(12,13,14) line(2,2,3) beam(7)}
2025-08-02 19:57:41,742 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | GRID
2025-08-02 19:57:42,053 INFO [20250802_195736_49b43b1f]       LinesRetriever.java:1500 | Global slope: 0.00000
2025-08-02 19:57:42,093 INFO [20250802_195736_49b43b1f]    ClustersRetriever.java:328  | Retrieved raw line clusters: 10 of sizes [5] with interline(12,13,14)
2025-08-02 19:57:42,213 INFO [20250802_195736_49b43b1f]            PeakGraph.java:310  | Systems: #1[1, 2] #2[3, 4] #3[5, 6] #4[7, 8] #5[9, 10]
2025-08-02 19:57:42,419 INFO [20250802_195736_49b43b1f]        SystemManager.java:134  | Indentation detected for system #1
2025-08-02 19:57:42,421 INFO [20250802_195736_49b43b1f]        SystemManager.java:779  | 1 part along 5 systems
2025-08-02 19:57:42,422 INFO [20250802_195736_49b43b1f]                 Book.java:545  | Created scores: [{Score 1}]
2025-08-02 19:57:42,424 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | HEADERS
2025-08-02 19:57:42,608 INFO [20250802_195736_49b43b1f]      BasicClassifier.java:277  | Classifier loaded XML norms.
2025-08-02 19:57:42,612 INFO [20250802_195736_49b43b1f]   AbstractClassifier.java:396  | Classifier data loaded from default uri jar:file:/C:/Program%20Files/Audiveris/app/audiveris.jar!/res/basic-classifier.zip
2025-08-02 19:57:43,081 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | STEM_SEEDS
2025-08-02 19:57:43,107 INFO [20250802_195736_49b43b1f]        StemSeedsStep.java:86   | stem(2 max:3)
2025-08-02 19:57:43,142 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | BEAMS
2025-08-02 19:57:43,725 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | LEDGERS
2025-08-02 19:57:43,865 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | HEADS
2025-08-02 19:57:45,011 INFO [20250802_195736_49b43b1f]        HeadSeedTally.java:235  | Scale information: HeadSeeds{NOTEHEAD_VOID[L:-0.2,R:-0.1] NOTEHEAD_BLACK[L:-0.2,R:-0.2]}
2025-08-02 19:57:45,012 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | STEMS
2025-08-02 19:57:45,430 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | REDUCTION
2025-08-02 19:57:45,562 INFO [20250802_195736_49b43b1f]        ReductionStep.java:105  | Stems free length median value: 47 pixels, 3.6 interlines
2025-08-02 19:57:45,567 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | CUE_BEAMS
2025-08-02 19:57:45,568 INFO [20250802_195736_49b43b1f]         CueBeamsStep.java:81   | Step CUE_BEAMS is skipped because small heads switch is off
2025-08-02 19:57:45,568 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | TEXTS
2025-08-02 19:57:47,316 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | MEASURES
2025-08-02 19:57:47,324 INFO [20250802_195736_49b43b1f]                 Page.java:307  | 17 raw measures: [4 in system#1, 3 in system#2, 3 in system#3, 3 in system#4, 4 in system#5]
2025-08-02 19:57:47,324 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | CHORDS
2025-08-02 19:57:47,336 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | CURVES
2025-08-02 19:57:47,676 INFO [20250802_195736_49b43b1f]         SlursBuilder.java:240  | Slurs: 14
2025-08-02 19:57:47,686 INFO [20250802_195736_49b43b1f]      SegmentsBuilder.java:155  | Segments: 15
2025-08-02 19:57:47,696 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | SYMBOLS
2025-08-02 19:57:47,923 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | LINKS
2025-08-02 19:57:47,955 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | RHYTHMS
2025-08-02 19:57:47,985 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#6} No timeOffset for HeadChordInter#6123{(0.965/0.965) staff:4 slot#10 dur:1/8}
2025-08-02 19:57:47,986 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#6} No timeOffset for HeadChordInter#6124{(0.945/0.945) staff:4 slot#12 dur:1/8}
2025-08-02 19:57:47,986 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#6} No timeOffset for HeadChordInter#6108{(0.937/0.937) staff:3 slot#11 dur:1/4}
2025-08-02 19:57:47,986 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#6} No timeOffset for HeadChordInter#6124{(0.945/0.945) staff:4 slot#12 dur:1/8}
2025-08-02 19:57:47,986 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#6} No timeOffset for HeadChordInter#6108{(0.937/0.937) staff:3 slot#11 dur:1/4}
2025-08-02 19:57:47,987 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#6} Voice{#2 excess:3/16} too long
2025-08-02 19:57:47,987 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#6} Voice{#6 excess:1/8} too long
2025-08-02 19:57:47,988 INFO [20250802_195736_49b43b1f]          StackRhythm.java:224  | S2 MeasureStack#6 no correct rhythm
2025-08-02 19:57:47,990 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#7} No timeOffset for HeadChordInter#6130{(0.959/0.959) staff:4 slot#8 dur:1/8}
2025-08-02 19:57:47,991 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#7} No timeOffset for HeadChordInter#6131{(0.953/0.953) staff:4 slot#9 dur:1/8}
2025-08-02 19:57:47,991 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#7} No timeOffset for RestChordInter#6896{(0.800/0.800) staff:3 slot#9 dur:1/16}
2025-08-02 19:57:47,992 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#7} No timeOffset for HeadChordInter#6131{(0.953/0.953) staff:4 slot#9 dur:1/8}
2025-08-02 19:57:47,992 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#7} No timeOffset for RestChordInter#6896{(0.800/0.800) staff:3 slot#9 dur:1/16}
2025-08-02 19:57:47,993 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#7} Voice{#5 excess:1/8} too long
2025-08-02 19:57:47,993 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#7} Voice{#2 excess:5/16} too long
2025-08-02 19:57:47,994 INFO [20250802_195736_49b43b1f]          StackRhythm.java:224  | S2 MeasureStack#7 no correct rhythm
2025-08-02 19:57:47,996 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#8} No timeOffset for HeadChordInter#6161{(0.943/0.943) staff:6 slot#8 dur:1/4}
2025-08-02 19:57:47,996 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#8} No timeOffset for HeadChordInter#6161{(0.943/0.943) staff:6 slot#8 dur:1/4}
2025-08-02 19:57:47,996 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#8} No timeOffset for HeadChordInter#6162{(0.939/0.939) staff:6 slot#9 dur:1/4}
2025-08-02 19:57:47,997 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#8} No timeOffset for RestChordInter#6931{(0.792/0.792) staff:5 slot#10 dur:1/8}
2025-08-02 19:57:47,998 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#8} No timeOffset for HeadChordInter#6164{(0.949/0.949) staff:6 slot#11 dur:1/8}
2025-08-02 19:57:47,998 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#8} No timeOffset for RestChordInter#6931{(0.792/0.792) staff:5 slot#10 dur:1/8}
2025-08-02 19:57:47,998 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#8} Voice{#1 excess:1/8} too long
2025-08-02 19:57:47,999 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#8} Voice{#2 excess:3/8} too long
2025-08-02 19:57:47,999 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#8} Voice{#6 excess:3/16} too long
2025-08-02 19:57:47,999 INFO [20250802_195736_49b43b1f]          StackRhythm.java:224  | S3 MeasureStack#8 no correct rhythm
2025-08-02 19:57:48,002 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#10} No timeOffset for HeadChordInter#6175{(0.968/0.968) staff:6 slot#10 dur:1/8}
2025-08-02 19:57:48,003 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#10} No timeOffset for HeadChordInter#6176{(0.944/0.944) staff:6 slot#12 dur:1/8}
2025-08-02 19:57:48,003 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#10} No timeOffset for HeadChordInter#6156{(0.953/0.953) staff:5 slot#11 dur:1/8}
2025-08-02 19:57:48,004 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#10} Voice{#2 excess:1/8} too long
2025-08-02 19:57:48,004 INFO [20250802_195736_49b43b1f]          StackRhythm.java:224  | S3 MeasureStack#10 no correct rhythm
2025-08-02 19:57:48,006 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#11} No timeOffset for HeadChordInter#6201{(0.946/0.946) staff:8 slot#4 dur:1/4}
2025-08-02 19:57:48,007 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#11} No timeOffset for HeadChordInter#6201{(0.946/0.946) staff:8 slot#4 dur:1/4}
2025-08-02 19:57:48,007 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#11} No timeOffset for HeadChordInter#6178{(0.951/0.951) staff:7 slot#5 dur:1/16}
2025-08-02 19:57:48,007 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#11} No timeOffset for RestChordInter#6973{(0.800/0.800) staff:8 slot#6 dur:1/16}
2025-08-02 19:57:48,008 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#11} No timeOffset for HeadChordInter#6179{(0.942/0.942) staff:7 slot#6 dur:3/8}
2025-08-02 19:57:48,008 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#11} No timeOffset for RestChordInter#6973{(0.800/0.800) staff:8 slot#6 dur:1/16}
2025-08-02 19:57:48,008 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#11} No timeOffset for HeadChordInter#6179{(0.942/0.942) staff:7 slot#6 dur:3/8}
2025-08-02 19:57:48,009 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#11} No timeOffset for HeadChordInter#6203{(0.961/0.961) staff:8 slot#8 dur:1/8}
2025-08-02 19:57:48,009 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#11} No timeOffset for HeadChordInter#6179{(0.942/0.942) staff:7 slot#6 dur:3/8}
2025-08-02 19:57:48,010 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#11} No timeOffset for HeadChordInter#6205{(0.952/0.952) staff:8 slot#11 dur:1/8}
2025-08-02 19:57:48,010 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#11} No timeOffset for RestChordInter#6974{(0.799/0.799) staff:7 slot#10 dur:1/16}
2025-08-02 19:57:48,011 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#11} No timeOffset for HeadChordInter#6205{(0.952/0.952) staff:8 slot#11 dur:1/8}
2025-08-02 19:57:48,011 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#11} No timeOffset for HeadChordInter#6180{(0.922/0.922) staff:7 slot#11 dur:1/4}
2025-08-02 19:57:48,012 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#11} No timeOffset for HeadChordInter#6205{(0.952/0.952) staff:8 slot#11 dur:1/8}
2025-08-02 19:57:48,012 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#11} Voice{#5 excess:1/8} too long
2025-08-02 19:57:48,012 INFO [20250802_195736_49b43b1f]          StackRhythm.java:224  | S4 MeasureStack#11 no correct rhythm
2025-08-02 19:57:48,014 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#12} No timeOffset for HeadChordInter#6188{(0.958/0.958) staff:7 slot#9 dur:1/16}
2025-08-02 19:57:48,015 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#12} No timeOffset for HeadChordInter#6211{(0.956/0.956) staff:8 slot#9 dur:1/8}
2025-08-02 19:57:48,015 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#12} Voice{#5 excess:1/16} too long
2025-08-02 19:57:48,015 INFO [20250802_195736_49b43b1f]          StackRhythm.java:224  | S4 MeasureStack#12 no correct rhythm
2025-08-02 19:57:48,016 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#13} No timeOffset for HeadChordInter#6216{(0.974/0.974) staff:8 slot#6 dur:3/16}
2025-08-02 19:57:48,016 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#13} No timeOffset for HeadChordInter#6217{(0.951/0.951) staff:8 slot#8 dur:1/8}
2025-08-02 19:57:48,018 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#13} No timeOffset for HeadChordInter#6217{(0.951/0.951) staff:8 slot#8 dur:1/8}
2025-08-02 19:57:48,018 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#13} No timeOffset for HeadChordInter#6198{(0.951/0.951) staff:7 slot#10 dur:1/8}
2025-08-02 19:57:48,019 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#13} Voice{#1 excess:1/16} too long
2025-08-02 19:57:48,019 INFO [20250802_195736_49b43b1f]          StackRhythm.java:224  | S4 MeasureStack#13 no correct rhythm
2025-08-02 19:57:48,020 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#14} No timeOffset for HeadChordInter#6221{(0.962/0.962) staff:9 slot#7 dur:1/8}
2025-08-02 19:57:48,021 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#14} No timeOffset for HeadChordInter#6244{(0.960/0.960) staff:10 slot#7 dur:1/8}
2025-08-02 19:57:48,021 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#14} Voice{#5 excess:1/8} too long
2025-08-02 19:57:48,021 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#14} Voice{#2 excess:1/8} too long
2025-08-02 19:57:48,021 INFO [20250802_195736_49b43b1f]          StackRhythm.java:224  | S5 MeasureStack#14 no correct rhythm
2025-08-02 19:57:48,022 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#15} No timeOffset for HeadChordInter#6250{(0.970/0.970) staff:10 slot#6 dur:3/16}
2025-08-02 19:57:48,023 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#15} No timeOffset for HeadChordInter#6250{(0.970/0.970) staff:10 slot#6 dur:3/16}
2025-08-02 19:57:48,023 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#15} No timeOffset for HeadChordInter#6250{(0.970/0.970) staff:10 slot#6 dur:3/16}
2025-08-02 19:57:48,023 INFO [20250802_195736_49b43b1f]   AbstractChordInter.java:627  | Measure{#15} No timeOffset for HeadChordInter#6252{(0.954/0.954) staff:10 slot#10 dur:1/8}
2025-08-02 19:57:48,024 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#15} Voice{#1 excess:3/16} too long
2025-08-02 19:57:48,024 INFO [20250802_195736_49b43b1f]          StackRhythm.java:224  | S5 MeasureStack#15 no correct rhythm
2025-08-02 19:57:48,027 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#16} No timeOffset for HeadChordInter#6233{(0.964/0.964) staff:9 slot#7 dur:1/8}
2025-08-02 19:57:48,028 INFO [20250802_195736_49b43b1f]        MeasureRhythm.java:1045 | Measure{#16} No timeOffset for HeadChordInter#6259{(0.958/0.958) staff:10 slot#7 dur:1/8}
2025-08-02 19:57:48,028 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#16} Voice{#5 excess:1/8} too long
2025-08-02 19:57:48,028 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#16} Voice{#2 excess:1/8} too long
2025-08-02 19:57:48,029 INFO [20250802_195736_49b43b1f]          StackRhythm.java:224  | S5 MeasureStack#16 no correct rhythm
2025-08-02 19:57:48,032 INFO [20250802_195736_49b43b1f]       StepMonitoring.java:98   | PAGE
2025-08-02 19:57:48,043 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#6} Voice{#2 excess:3/16} too long
2025-08-02 19:57:48,043 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#6} Voice{#6 excess:1/8} too long
2025-08-02 19:57:48,043 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#7} Voice{#2 excess:5/16} too long
2025-08-02 19:57:48,043 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#7} Voice{#5 excess:1/8} too long
2025-08-02 19:57:48,044 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#8} Voice{#1 excess:1/8} too long
2025-08-02 19:57:48,045 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#8} Voice{#2 excess:3/8} too long
2025-08-02 19:57:48,046 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#8} Voice{#6 excess:3/16} too long
2025-08-02 19:57:48,046 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#10} Voice{#2 excess:1/8} too long
2025-08-02 19:57:48,047 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#11} Voice{#5 excess:1/8} too long
2025-08-02 19:57:48,048 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#12} Voice{#5 excess:1/16} too long
2025-08-02 19:57:48,048 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#13} Voice{#2 excess:1/16} too long
2025-08-02 19:57:48,049 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#14} Voice{#2 excess:1/8} too long
2025-08-02 19:57:48,050 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#14} Voice{#5 excess:1/8} too long
2025-08-02 19:57:48,051 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#15} Voice{#3 excess:3/16} too long
2025-08-02 19:57:48,052 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#16} Voice{#2 excess:1/8} too long
2025-08-02 19:57:48,053 INFO [20250802_195736_49b43b1f]                Voice.java:257  | Measure{#16} Voice{#5 excess:1/8} too long
2025-08-02 19:57:48,054 INFO [20250802_195736_49b43b1f]                 Book.java:2550 | Book{20250802_195736_49b43b1f} storing
2025-08-02 19:57:48,086 INFO [20250802_195736_49b43b1f]                 Book.java:2531 | Stored /book.xml
2025-08-02 19:57:48,123 INFO [20250802_195736_49b43b1f]           DataHolder.java:348  | Stored /sheet#1/BINARY.png
2025-08-02 19:57:48,558 INFO [20250802_195736_49b43b1f]                Sheet.java:1562 | Stored /sheet#1/sheet#1.xml
2025-08-02 19:57:48,559 INFO [20250802_195736_49b43b1f]                 Book.java:2491 | Book stored as C:\Users\<USER>\Desktop\melody-forge\output\20250802_195736_49b43b1f.omr
2025-08-02 19:57:48,562 INFO [20250802_195736_49b43b1f]            SheetStub.java:1644 | Disposed sheet
2025-08-02 19:57:48,595 INFO [20250802_195736_49b43b1f]                 Book.java:2018 | End of Stub#1 memory: 31,196,576
2025-08-02 19:57:48,595 INFO [20250802_195736_49b43b1f]                 Book.java:2025 | Book processed.
2025-08-02 19:57:48,865 INFO [20250802_195736_49b43b1f]            SheetStub.java:995  | Loaded /sheet#1/sheet#1.xml
2025-08-02 19:57:49,172 INFO [20250802_195736_49b43b1f]      PartwiseBuilder.java:2661 | Exporting sheet(s): [#1]
2025-08-02 19:57:49,263 INFO [20250802_195736_49b43b1f]        ScoreExporter.java:164  | Score 20250802_195736_49b43b1f exported to C:\Users\<USER>\Desktop\melody-forge\output\20250802_195736_49b43b1f.mxl
2025-08-02 19:57:49,287 INFO [20250802_195736_49b43b1f]                Score.java:181  | Closing {Score 1}
