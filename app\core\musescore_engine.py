"""
MuseScore渲染引擎封装
用于将带指法的MusicXML渲染为高质量图片
"""
import os
import subprocess
import logging
import time
from pathlib import Path
from flask import current_app

logger = logging.getLogger(__name__)

class MuseScoreEngine:
    """MuseScore渲染引擎"""
    
    def __init__(self, config=None):
        if config:
            self.musescore_path = config.get('MUSESCORE_PATH')
            self.output_folder = config.get('OUTPUT_FOLDER', 'output')
        else:
            self.musescore_path = current_app.config.get('MUSESCORE_PATH')
            self.output_folder = current_app.config.get('OUTPUT_FOLDER')
        
    def render_to_image(self, xml_path, output_name=None, format='png', dpi=300):
        """
        将MusicXML渲染为图片
        
        Args:
            xml_path (str): 输入MusicXML文件路径
            output_name (str): 输出文件名（不含扩展名）
            format (str): 输出格式（png, pdf, svg）
            dpi (int): 输出分辨率
            
        Returns:
            dict: 渲染结果
        """
        try:
            # 转换为绝对路径
            abs_xml_path = os.path.abspath(xml_path)
            abs_output_folder = os.path.abspath(self.output_folder)

            if not os.path.exists(abs_xml_path):
                raise FileNotFoundError(f"输入文件不存在: {abs_xml_path}")

            if not os.path.exists(self.musescore_path):
                logger.error(f"MuseScore程序路径不存在: {self.musescore_path}")
                logger.error(f"请检查.env文件中的MUSESCORE_PATH配置")
                raise FileNotFoundError(f"MuseScore程序不存在: {self.musescore_path}")

            logger.info(f"MuseScore程序路径: {self.musescore_path}")
            logger.info(f"MuseScore程序存在: {os.path.exists(self.musescore_path)}")

            # 测试MuseScore是否可以正常执行
            try:
                logger.info("开始MuseScore版本检查...")
                version_result = subprocess.run(
                    [self.musescore_path, '--version'],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                logger.info(f"MuseScore版本检查返回码: {version_result.returncode}")
                if version_result.returncode == 0:
                    logger.info(f"MuseScore版本信息: {version_result.stdout.strip()}")
                elif version_result.returncode == 1320:
                    logger.error("MuseScore被Windows组策略或安全软件阻止执行")
                    logger.error("请尝试以管理员身份运行应用，或检查防病毒软件设置")
                else:
                    logger.warning(f"MuseScore版本检查失败，返回码: {version_result.returncode}")
                    logger.warning(f"错误信息: {version_result.stderr}")
            except subprocess.TimeoutExpired:
                logger.warning("MuseScore版本检查超时")
            except Exception as e:
                logger.warning(f"无法获取MuseScore版本信息: {str(e)}")

            # 确保输出目录存在
            os.makedirs(abs_output_folder, exist_ok=True)

            # 生成输出文件名
            if output_name is None:
                output_name = Path(abs_xml_path).stem

            output_path = os.path.join(abs_output_folder, f"{output_name}.{format}")
            abs_output_path = os.path.abspath(output_path)

            # 构建命令行参数（MuseScore 4格式）
            cmd = [
                self.musescore_path,
                '-o', abs_output_path,
                abs_xml_path
            ]

            # 添加格式特定参数
            if format == 'png':
                cmd.extend(['--image-resolution', str(dpi)])
            elif format == 'pdf':
                cmd.extend(['--image-resolution', str(dpi)])

            logger.info(f"执行MuseScore命令: {' '.join(cmd)}")
            logger.info(f"输入文件: {abs_xml_path}")
            logger.info(f"输出文件: {abs_output_path}")

            # 执行MuseScore（使用项目根目录作为工作目录）
            logger.info("开始执行MuseScore命令...")
            start_time = time.time()

            try:
                # 尝试不同的执行方式来避免被阻止
                creation_flags = 0
                if os.name == 'nt':  # Windows
                    creation_flags = subprocess.CREATE_NO_WINDOW

                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=60,
                    cwd=os.getcwd(),
                    creationflags=creation_flags
                )

                end_time = time.time()
                logger.info(f"MuseScore命令执行完成，耗时: {end_time - start_time:.2f}秒")

            except subprocess.TimeoutExpired:
                logger.error("MuseScore命令执行超时（60秒）")
                return {
                    'success': False,
                    'error': 'MuseScore渲染超时，请检查文件是否过于复杂'
                }
            
            # 记录详细的执行结果
            logger.info(f"MuseScore返回码: {result.returncode}")
            logger.info(f"MuseScore stdout: {result.stdout}")
            logger.info(f"MuseScore stderr: {result.stderr}")

            if result.returncode != 0:
                error_msg = result.stderr.strip() if result.stderr.strip() else "未知错误"
                logger.error(f"MuseScore执行失败，返回码: {result.returncode}")
                logger.error(f"错误信息: {error_msg}")
                logger.error(f"标准输出: {result.stdout}")

                # 提供更有用的错误信息
                if result.returncode == 1:
                    error_msg = f"MuseScore执行失败，可能是文件格式问题或路径错误。返回码: {result.returncode}"
                elif result.returncode == 127:
                    error_msg = f"找不到MuseScore程序，请检查路径配置: {self.musescore_path}"
                elif result.returncode == 1320:
                    error_msg = (
                        "MuseScore被Windows安全策略阻止执行。解决方案：\n"
                        "1. 以管理员身份运行此应用\n"
                        "2. 检查防病毒软件是否阻止了MuseScore\n"
                        "3. 检查Windows组策略设置\n"
                        "4. 尝试重新安装MuseScore"
                    )
                else:
                    error_msg = f"MuseScore执行失败，返回码: {result.returncode}，错误: {result.stderr}"

                return {
                    'success': False,
                    'error': error_msg,
                    'stdout': result.stdout,
                    'stderr': result.stderr,
                    'returncode': result.returncode
                }
            
            # 检查输出文件是否生成
            # MuseScore可能会生成带数字后缀的文件（如 filename-1.png）
            actual_output_path = output_path

            if not os.path.exists(output_path):
                # 尝试查找带数字后缀的文件
                base_name = os.path.splitext(output_path)[0]
                extension = os.path.splitext(output_path)[1]

                # 检查可能的文件名变体
                for i in range(1, 10):  # 检查 -1 到 -9
                    variant_path = f"{base_name}-{i}{extension}"
                    if os.path.exists(variant_path):
                        actual_output_path = variant_path
                        logger.info(f"找到MuseScore生成的文件: {variant_path}")
                        break
                else:
                    # 仍然没有找到文件
                    logger.error(f"输出文件未生成: {output_path}")
                    logger.error(f"也未找到变体文件: {base_name}-[1-9]{extension}")

                    # 列出输出目录中的所有文件用于调试
                    output_dir = os.path.dirname(output_path)
                    if os.path.exists(output_dir):
                        files_in_dir = os.listdir(output_dir)
                        logger.error(f"输出目录中的文件: {files_in_dir}")

                    return {
                        'success': False,
                        'error': f'输出文件未生成: {output_path}',
                        'stdout': result.stdout,
                        'stderr': result.stderr
                    }
            
            logger.info(f"MuseScore渲染成功: {actual_output_path}")

            return {
                'success': True,
                'output_path': actual_output_path,  # 返回实际生成的文件路径
                'format': format,
                'dpi': dpi,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
        except subprocess.TimeoutExpired:
            logger.error("MuseScore渲染超时")
            return {
                'success': False,
                'error': 'MuseScore渲染超时（超过2分钟）'
            }
        except Exception as e:
            logger.error(f"MuseScore渲染异常: {str(e)}")
            return {
                'success': False,
                'error': f'渲染异常: {str(e)}'
            }
    
    def render_to_pdf(self, xml_path, output_name=None):
        """渲染为PDF格式"""
        return self.render_to_image(xml_path, output_name, format='pdf')
    
    def render_to_png(self, xml_path, output_name=None, dpi=300):
        """渲染为PNG格式"""
        return self.render_to_image(xml_path, output_name, format='png', dpi=dpi)
    
    def is_available(self):
        """检查MuseScore是否可用"""
        return os.path.exists(self.musescore_path)
    
    def get_version(self):
        """获取MuseScore版本信息"""
        try:
            result = subprocess.run(
                [self.musescore_path, '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.stdout.strip() if result.returncode == 0 else None
        except:
            return None
    
    def apply_fingering_style(self, xml_path):
        """
        应用指法显示样式
        修改MusicXML文件以优化指法显示效果
        """
        try:
            import xml.etree.ElementTree as ET
            
            # 解析XML文件
            tree = ET.parse(xml_path)
            root = tree.getroot()
            
            # 查找所有指法标记
            for fingering in root.iter('fingering'):
                # 设置指法样式
                fingering.set('font-size', '10')
                fingering.set('font-weight', 'bold')
                fingering.set('color', '#000000')
                
                # 设置位置 - 使用更兼容的方式查找父元素
                try:
                    # 在整个文档中查找包含此指法的音符
                    for note in root.iter('note'):
                        if fingering in list(note.iter()):
                            # 根据音符位置调整指法位置
                            if 'staff' in note.attrib:
                                staff_num = int(note.attrib['staff'])
                                if staff_num == 1:  # 右手
                                    fingering.set('placement', 'above')
                                else:  # 左手
                                    fingering.set('placement', 'below')
                            break
                except Exception as pos_error:
                    logger.warning(f"设置指法位置失败: {pos_error}")
                    # 使用默认位置
                    fingering.set('placement', 'above')
            
            # 保存修改后的文件
            styled_path = xml_path.replace('.xml', '_styled.xml')
            tree.write(styled_path, encoding='utf-8', xml_declaration=True)
            
            return {
                'success': True,
                'styled_path': styled_path
            }
            
        except Exception as e:
            logger.error(f"应用指法样式失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
