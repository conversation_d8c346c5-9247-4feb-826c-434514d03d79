"""
MXL文件解压工具
用于从MXL压缩文件中提取XML内容
"""
import zipfile
import os
import logging
import tempfile
from pathlib import Path

logger = logging.getLogger(__name__)

class MXLExtractor:
    """MXL文件解压工具"""
    
    def extract_mxl_to_xml(self, mxl_path, output_dir=None):
        """
        从MXL文件中提取XML内容
        
        Args:
            mxl_path (str): MXL文件路径
            output_dir (str): 输出目录，如果为None则使用临时目录
            
        Returns:
            dict: 提取结果
        """
        try:
            if not os.path.exists(mxl_path):
                return {
                    'success': False,
                    'error': f'MXL文件不存在: {mxl_path}'
                }
            
            # 确定输出目录
            if output_dir is None:
                output_dir = os.path.dirname(mxl_path)
            
            # 生成输出文件名
            base_name = Path(mxl_path).stem
            xml_output_path = os.path.join(output_dir, f"{base_name}.xml")
            
            # 解压MXL文件
            with zipfile.ZipFile(mxl_path, 'r') as zip_file:
                # 查找主XML文件
                xml_files = [f for f in zip_file.namelist() if f.endswith('.xml') and not f.startswith('META-INF/')]
                
                if not xml_files:
                    return {
                        'success': False,
                        'error': 'MXL文件中未找到XML内容'
                    }
                
                # 通常主XML文件与MXL文件同名
                main_xml = None
                for xml_file in xml_files:
                    if Path(xml_file).stem == base_name:
                        main_xml = xml_file
                        break
                
                # 如果没找到同名文件，使用第一个XML文件
                if main_xml is None:
                    main_xml = xml_files[0]
                
                logger.info(f"从MXL中提取XML文件: {main_xml}")
                
                # 提取XML内容
                with zip_file.open(main_xml) as xml_content:
                    with open(xml_output_path, 'wb') as output_file:
                        output_file.write(xml_content.read())
            
            logger.info(f"MXL解压完成: {xml_output_path}")
            
            return {
                'success': True,
                'xml_path': xml_output_path,
                'extracted_file': main_xml
            }
            
        except zipfile.BadZipFile:
            return {
                'success': False,
                'error': 'MXL文件格式无效或已损坏'
            }
        except Exception as e:
            logger.error(f"MXL解压失败: {str(e)}")
            return {
                'success': False,
                'error': f'MXL解压失败: {str(e)}'
            }
    
    def is_mxl_file(self, file_path):
        """
        检查文件是否为MXL格式
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            bool: 是否为MXL文件
        """
        if not file_path.lower().endswith('.mxl'):
            return False
            
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                # 检查是否包含XML文件
                xml_files = [f for f in zip_file.namelist() if f.endswith('.xml')]
                return len(xml_files) > 0
        except:
            return False
