{% extends "base.html" %}

{% block title %}简化可视化编辑器{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">
                <i class="fas fa-edit me-2"></i>可视化乐谱编辑
            </h2>
        </div>
    </div>
    
    <div class="row">
        <!-- 左侧：图片对照区 -->
        <div class="col-lg-6">
            <!-- 原始图片 -->
            <div class="card shadow-sm mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-image me-2"></i>原始图片
                    </h6>
                </div>
                <div class="card-body text-center p-2">
                    <img id="original-image" src="" class="img-fluid rounded" alt="原始乐谱" style="max-height: 300px;">
                    <div id="image-error" class="alert alert-warning d-none mt-2">
                        <i class="fas fa-exclamation-triangle"></i>
                        图片加载失败
                    </div>
                </div>
            </div>
            
            <!-- 识别预览 -->
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-music me-2"></i>识别预览
                    </h6>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary" id="refresh-preview">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="switch-to-xml">
                            <i class="fas fa-code"></i> XML编辑
                        </button>
                    </div>
                </div>
                <div class="card-body text-center p-2">
                    <div id="preview-loading" class="d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">生成预览中...</span>
                        </div>
                        <p class="mt-2">正在生成预览...</p>
                    </div>
                    <img id="recognition-preview" src="" class="img-fluid rounded d-none" alt="识别预览" 
                         style="max-height: 300px; cursor: pointer;" 
                         title="点击图片可查看位置信息">
                    <div id="preview-error" class="alert alert-warning d-none">
                        <i class="fas fa-exclamation-triangle"></i>
                        预览生成失败，但您仍可以继续编辑
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧：音符编辑区 -->
        <div class="col-lg-6">
            <div class="card shadow-sm" style="height: 650px; overflow-y: auto;">
                <div class="card-header sticky-top bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-tools me-2"></i>音符编辑
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>对照编辑：</strong>参考左侧原图，在右侧编辑音符。
                    </div>
                    
                    <!-- 基本信息 -->
                    <div class="mb-3">
                        <h6>基本信息</h6>
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">小节数</small>
                                <div class="h6" id="measure-count">-</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">音符数</small>
                                <div class="h6" id="note-count">-</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 音符列表 -->
                    <div id="notes-container">
                        <div class="text-center text-muted">
                            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                            正在加载音符列表...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="row mt-3 mb-4">
        <div class="col-12 text-center">
            <button type="button" class="btn btn-secondary me-3" id="back-to-upload">
                <i class="fas fa-arrow-left me-2"></i>返回上传
            </button>
            <button type="button" class="btn btn-info me-3" id="preview-changes">
                <i class="fas fa-eye me-2"></i>预览修改
            </button>
            <button type="button" class="btn btn-primary" id="proceed-to-fingering">
                <i class="fas fa-hand-paper me-2"></i>确认并生成指法
            </button>
        </div>
    </div>
</div>

<script>
// 简化的JavaScript实现
document.addEventListener('DOMContentLoaded', function() {
    console.log('简化可视化编辑器开始初始化');
    
    // 获取任务ID
    const urlParams = new URLSearchParams(window.location.search);
    const taskId = urlParams.get('task_id');
    
    if (!taskId) {
        alert('缺少任务ID');
        window.location.href = '/upload';
        return;
    }
    
    console.log('任务ID:', taskId);
    
    // 加载图片
    loadImages(taskId);
    
    // 加载音符数据
    loadNotes(taskId);
    
    // 绑定事件
    document.getElementById('switch-to-xml').addEventListener('click', function() {
        window.location.href = `/xml-editor?task_id=${taskId}`;
    });
    
    document.getElementById('back-to-upload').addEventListener('click', function() {
        if (confirm('确定要返回上传页面吗？')) {
            window.location.href = '/upload';
        }
    });
});

function loadImages(taskId) {
    console.log('开始加载图片');
    
    // 加载原始图片
    const originalImg = document.getElementById('original-image');
    originalImg.src = `/api/file/${taskId}/preprocessed_image`;
    originalImg.onerror = function() {
        console.error('原始图片加载失败');
        document.getElementById('image-error').classList.remove('d-none');
    };
    originalImg.onload = function() {
        console.log('原始图片加载成功');
    };
    
    // 尝试加载预览图片
    const previewImg = document.getElementById('recognition-preview');
    previewImg.src = `/api/file/${taskId}/preview`;
    previewImg.onerror = function() {
        console.error('预览图片加载失败');
        document.getElementById('preview-error').classList.remove('d-none');
    };
    previewImg.onload = function() {
        console.log('预览图片加载成功');
        previewImg.classList.remove('d-none');
    };
}

function loadNotes(taskId) {
    console.log('开始加载音符数据');
    
    fetch(`/api/status/${taskId}`)
        .then(response => response.json())
        .then(data => {
            console.log('任务状态:', data);
            
            if (data.state === 'SUCCESS' && data.result && data.result.files) {
                // 加载XML并解析音符
                return fetch(`/api/file/${taskId}/extracted_xml`);
            } else {
                throw new Error('任务未完成或数据不完整');
            }
        })
        .then(response => response.text())
        .then(xmlContent => {
            console.log('XML内容长度:', xmlContent.length);
            parseAndDisplayNotes(xmlContent);
        })
        .catch(error => {
            console.error('加载音符数据失败:', error);
            const container = document.getElementById('notes-container');
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    加载失败: ${error.message}
                </div>
            `;
        });
}

function parseAndDisplayNotes(xmlContent) {
    try {
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlContent, 'application/xml');
        
        // 统计信息
        const measures = xmlDoc.querySelectorAll('measure').length;
        const notes = xmlDoc.querySelectorAll('note').length;
        
        document.getElementById('measure-count').textContent = measures;
        document.getElementById('note-count').textContent = notes;
        
        // 显示简单的音符列表
        const container = document.getElementById('notes-container');
        container.innerHTML = `
            <div class="alert alert-success">
                <i class="fas fa-check me-2"></i>
                成功加载 ${measures} 个小节，${notes} 个音符
            </div>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                详细的音符编辑功能正在开发中，请使用XML编辑器进行精确修改。
            </div>
            <div class="text-center">
                <button class="btn btn-primary" onclick="window.location.href='/xml-editor?task_id=${new URLSearchParams(window.location.search).get('task_id')}'">
                    <i class="fas fa-code me-2"></i>切换到XML编辑器
                </button>
            </div>
        `;
        
    } catch (error) {
        console.error('解析XML失败:', error);
        const container = document.getElementById('notes-container');
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                XML解析失败: ${error.message}
            </div>
        `;
    }
}
</script>
{% endblock %}
