/**
 * 可视化乐谱编辑器JavaScript
 */

class VisualEditor {
    constructor() {
        console.log('🔧 VisualEditor构造函数开始 - XML单一数据源架构');
        this.taskId = null;

        // 🔧 核心架构重构：XML为单一数据源
        this.originalXML = '';
        this.currentXML = '';  // 唯一的数据源
        this.musicData = null; // 从XML解析的只读视图数据，每次操作后重新生成

        this.currentMode = 'preview'; // 'preview' 或 'editor'
        this.editMode = 'visual'; // 'visual' 或 'xml'
        this.hasUnsavedChanges = false; // 是否有未保存的修改

        // 初始状态备份（用于撤销所有修改）
        this.initialState = null;

        // 连线编辑状态
        this.tieMode = false; // 是否处于连音线编辑模式
        this.slurMode = false; // 是否处于连奏线编辑模式
        this.beamMode = false; // 是否处于连音符编辑模式
        this.selectedNotesForTie = []; // 选中的音符用于连音线
        this.selectedNotesForSlur = []; // 选中的音符用于连奏线
        this.selectedNotesForBeam = []; // 选中的音符用于连音符

        // 连线状态数据结构
        this.connectionData = {
            ties: new Map(), // noteId -> {partnerId, type: 'start'|'stop'}
            slurs: new Map(), // slurId -> {noteIds: [], number}
            beams: new Map(), // beamId -> {noteIds: [], number}
            noteConnections: new Map() // noteId -> {ties: [], slurs: [], beams: []}
        };

        // 🔧 XML操作事务系统
        this.xmlOperationLock = false; // XML操作锁，防止并发修改
        this.workingXmlDoc = null; // 统一的工作XML文档
        this.xmlTransactionStack = []; // XML操作事务栈，支持回滚
        this.maxTransactionStackSize = 50; // 最大事务栈大小

        // 🔧 全局音符ID管理器
        this.globalNoteIdCounter = 1;

        // 待处理的音符数据（用于编辑模态框）
        this.pendingSingleNoteData = null; // 存储待处理的单音符数据
        this.pendingChordData = null; // 存储待处理的和弦数据
        this.currentEditingChord = []; // 当前正在编辑的和弦音符列表

        console.log('🔧 开始调用init方法');
        this.init();
        console.log('🔧 VisualEditor构造函数完成 - 新架构初始化完成');
    }

    init() {
        console.log('可视化编辑器初始化开始');

        // 从URL获取任务ID
        const urlParams = new URLSearchParams(window.location.search);
        this.taskId = urlParams.get('task_id');

        console.log('获取到的任务ID:', this.taskId);

        if (!this.taskId) {
            console.error('缺少任务ID');
            this.showMessage('缺少任务ID，请重新上传文件', 'danger');
            setTimeout(() => {
                window.location.href = '/upload';
            }, 3000);
            return;
        }

        console.log('开始绑定事件');
        this.bindEvents();

        console.log('开始加载任务数据');
        this.loadTaskData();
    }

    bindEvents() {
        console.log('开始绑定事件');

        // 安全绑定事件的辅助函数
        const safeBindEvent = (id, event, handler) => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener(event, handler);
                console.log(`成功绑定事件: ${id}`);
            } else {
                console.warn(`元素不存在，跳过绑定: ${id}`);
            }
        };

        // 按钮事件
        safeBindEvent('show-preview', 'click', () => this.showPreview());
        safeBindEvent('show-editor', 'click', () => this.showEditor());
        safeBindEvent('visual-edit-mode', 'click', () => this.showVisualEdit());
        safeBindEvent('xml-edit-mode', 'click', () => this.showXMLEdit());
        safeBindEvent('back-to-upload', 'click', () => this.backToUpload());
        safeBindEvent('proceed-to-fingering', 'click', () => this.proceedToFingering());

        // 标题信息编辑事件
        safeBindEvent('main-title', 'input', () => this.updateTitleInfo());
        safeBindEvent('sub-title', 'input', () => this.updateTitleInfo());
        safeBindEvent('composer', 'input', () => this.updateTitleInfo());
        safeBindEvent('original-artist', 'input', () => this.updateTitleInfo());
        safeBindEvent('arranger', 'input', () => this.updateTitleInfo());
        safeBindEvent('copyright', 'input', () => this.updateTitleInfo());

        // 编辑控件事件
        safeBindEvent('key-signature', 'change', () => this.updateKeySignature());
        safeBindEvent('time-signature', 'change', () => this.updateTimeSignature());
        safeBindEvent('tempo', 'change', () => this.updateTempo());

        // 音符编辑按钮事件
        safeBindEvent('add-note', 'click', () => this.addNote());
        safeBindEvent('auto-fix', 'click', () => this.autoFix());
        safeBindEvent('save-visual-changes', 'click', () => this.saveVisualChanges());
        safeBindEvent('undo-all-changes', 'click', () => this.undoAllChanges());

        // 连线编辑按钮事件
        safeBindEvent('add-tie-mode', 'click', () => this.toggleTieMode());
        safeBindEvent('add-slur-mode', 'click', () => this.toggleSlurMode());
        safeBindEvent('finish-slur', 'click', () => this.finishSlurCreation());

        // 连音符编辑按钮事件
        safeBindEvent('add-beam-mode', 'click', () => this.toggleBeamMode());
        safeBindEvent('finish-beam', 'click', () => this.finishBeamCreation());

        // XML编辑按钮事件
        safeBindEvent('format-xml', 'click', () => this.formatXML());
        safeBindEvent('validate-xml', 'click', () => this.validateXML());
        safeBindEvent('save-xml', 'click', () => this.saveXMLChanges());

        // 预览图点击事件（高级功能）
        document.addEventListener('click', (e) => {
            if (e.target.id === 'recognition-preview') {
                this.handlePreviewClick(e);
            }
        });

        console.log('事件绑定完成');
    }

    async loadTaskData() {
        try {
            console.log('开始加载任务数据，任务ID:', this.taskId);
            const response = await fetch(`/api/status/${this.taskId}`);
            const data = await response.json();

            console.log('任务状态数据:', data);

            if (data.state === 'SUCCESS' && data.result && data.result.files) {
                console.log('文件列表:', data.result.files);

                // 加载原始图片
                if (data.result.files.preprocessed_image) {
                    const imageUrl = `/api/file/${this.taskId}/preprocessed_image`;
                    console.log('加载原始图片:', imageUrl);
                    const originalImg = document.getElementById('original-image');
                    originalImg.src = imageUrl;
                    originalImg.onerror = () => {
                        console.error('原始图片加载失败');
                        this.showMessage('原始图片加载失败', 'warning');
                    };
                    originalImg.onload = () => {
                        console.log('原始图片加载成功');
                    };
                }

                // 加载XML内容并解析 - 优先使用增强版XML（包含文字集成信息）
                if (data.result.files.enhanced_xml || data.result.files.extracted_xml) {
                    console.log('开始加载XML内容');
                    await this.loadAndParseXML(data.result.files);
                }

                // 生成初始预览
                console.log('开始生成预览');
                this.generatePreview();
            } else {
                throw new Error('任务数据不完整');
            }
        } catch (error) {
            console.error('加载任务数据失败:', error);
            this.showMessage('加载数据失败: ' + error.message, 'danger');
        }
    }

    async loadAndParseXML(files) {
        try {
            console.log('开始加载XML内容');

            // 优先使用增强版XML（包含文字集成信息），如果不存在则使用原始XML
            let xmlFileType = 'extracted_xml';
            const hasEnhancedXml = files && files.enhanced_xml && typeof files.enhanced_xml === 'string' && files.enhanced_xml.length > 0;

            if (hasEnhancedXml) {
                xmlFileType = 'enhanced_xml';
                console.log('Visual Editor: ✅ 使用增强版XML文件（包含文字集成信息）');
            } else {
                console.log('Visual Editor: ⚠️ 使用原始XML文件');
            }

            const response = await fetch(`/api/file/${this.taskId}/${xmlFileType}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const xmlContent = await response.text();
            console.log('XML内容长度:', xmlContent.length);

            this.originalXML = xmlContent;
            this.currentXML = xmlContent;

            // 解析XML获取音乐信息
            console.log('开始解析XML数据');
            this.musicData = this.parseXMLData(xmlContent);
            console.log('解析后的音乐数据:', this.musicData);

            this.updateUI();

            // 加载音符列表
            console.log('开始加载音符列表');
            this.loadNotesList();

            // 保存初始状态（用于撤销所有修改）
            this.saveInitialState();

        } catch (error) {
            console.error('加载XML内容失败:', error);
            this.showMessage('加载XML内容失败: ' + error.message, 'danger');

            // 显示错误信息在音符容器中
            const container = document.getElementById('notes-container');
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    加载XML失败: ${error.message}
                </div>
            `;
        }
    }

    // 🔧 核心方法：生成全局唯一的音符ID
    generateNoteId() {
        return this.globalNoteIdCounter++;
    }

    // 🔧 核心方法：重新解析XML并生成notesList（单向数据流）
    refreshMusicDataFromXML() {
        console.log('🔄 从XML重新生成musicData...');
        try {
            // 🔧 修复：保存当前音符的修改状态
            const previousModifiedStates = new Map();
            if (this.musicData && this.musicData.notesList) {
                this.musicData.notesList.forEach(note => {
                    if (note.isModified) {
                        previousModifiedStates.set(note.id.toString(), true);
                    }
                });
                console.log(`🔧 保存了 ${previousModifiedStates.size} 个音符的修改状态`);
            }

            // 重新解析XML
            this.musicData = this.parseXMLData(this.currentXML);

            // 🔧 修复：恢复音符的修改状态
            if (previousModifiedStates.size > 0) {
                this.musicData.notesList.forEach(note => {
                    if (previousModifiedStates.has(note.id.toString())) {
                        note.isModified = true;
                        console.log(`🔧 恢复音符 ${note.id} 的修改状态`);
                    }
                });
            }

            console.log('✅ musicData重新生成完成');
            return true;
        } catch (error) {
            console.error('❌ 从XML重新生成musicData失败:', error);
            return false;
        }
    }

    // 🔧 核心方法：执行XML操作并自动刷新视图
    async executeXMLOperation(operationName, xmlOperation) {
        console.log(`🔧 执行XML操作: ${operationName}`);

        // 保存当前状态到事务栈
        this.pushTransaction(operationName, this.currentXML);

        try {
            // 🔧 修复：在执行XML操作前，先同步所有内存中的音符到XML
            console.log('🔄 执行XML操作前，先同步内存中的音符到XML...');
            await this.updateXMLWithNoteChangesInternal();

            // 🔧 修复：验证同步后的XML中是否包含所有音符
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(this.currentXML, 'text/xml');
            const xmlNoteCount = xmlDoc.querySelectorAll('note').length;
            const notesListCount = this.musicData.notesList.length;

            if (xmlNoteCount !== notesListCount) {
                console.warn(`⚠️ 同步后音符数量不匹配: XML=${xmlNoteCount}, notesList=${notesListCount}`);
                // 尝试修复：重新添加缺失的音符到XML
                await this.ensureAllNotesInXML();
            }

            // 1. 执行XML操作
            const result = await xmlOperation();

            // 2. 重新解析XML生成musicData
            if (!this.refreshMusicDataFromXML()) {
                throw new Error('XML操作后数据重新解析失败');
            }

            // 3. 刷新UI显示
            this.loadNotesList();
            this.updateUI();

            // 4. 标记有未保存的修改
            this.hasUnsavedChanges = true;
            this.updateSaveButtonState();

            console.log(`✅ XML操作完成: ${operationName}`);
            return result;

        } catch (error) {
            console.error(`❌ XML操作失败: ${operationName}`, error);
            // 自动回滚到操作前的状态
            this.rollbackLastTransaction();
            throw error;
        }
    }

    // 🔧 事务系统：推入新事务
    pushTransaction(operationName, xmlState) {
        const transaction = {
            id: Date.now(),
            operationName: operationName,
            xmlState: xmlState,
            timestamp: new Date().toISOString()
        };

        this.xmlTransactionStack.push(transaction);

        // 限制事务栈大小
        if (this.xmlTransactionStack.length > this.maxTransactionStackSize) {
            this.xmlTransactionStack.shift(); // 移除最旧的事务
        }

        console.log(`📝 事务已保存: ${operationName} (栈大小: ${this.xmlTransactionStack.length})`);
    }

    // 🔧 事务系统：回滚最后一个事务
    rollbackLastTransaction() {
        if (this.xmlTransactionStack.length === 0) {
            console.warn('⚠️ 没有可回滚的事务');
            return false;
        }

        const lastTransaction = this.xmlTransactionStack.pop();
        console.log(`🔄 回滚事务: ${lastTransaction.operationName}`);

        // 恢复XML状态
        this.currentXML = lastTransaction.xmlState;

        // 重新解析并刷新UI
        if (this.refreshMusicDataFromXML()) {
            this.loadNotesList();
            this.updateUI();
            console.log(`✅ 事务回滚成功: ${lastTransaction.operationName}`);
            return true;
        } else {
            console.error(`❌ 事务回滚失败: ${lastTransaction.operationName}`);
            return false;
        }
    }

    // 🔧 事务系统：回滚到指定事务
    rollbackToTransaction(transactionId) {
        const transactionIndex = this.xmlTransactionStack.findIndex(t => t.id === transactionId);
        if (transactionIndex === -1) {
            console.warn(`⚠️ 未找到事务ID: ${transactionId}`);
            return false;
        }

        const targetTransaction = this.xmlTransactionStack[transactionIndex];
        console.log(`🔄 回滚到事务: ${targetTransaction.operationName}`);

        // 恢复XML状态
        this.currentXML = targetTransaction.xmlState;

        // 移除该事务之后的所有事务
        this.xmlTransactionStack = this.xmlTransactionStack.slice(0, transactionIndex + 1);

        // 重新解析并刷新UI
        if (this.refreshMusicDataFromXML()) {
            this.loadNotesList();
            this.updateUI();
            console.log(`✅ 回滚到事务成功: ${targetTransaction.operationName}`);
            return true;
        } else {
            console.error(`❌ 回滚到事务失败: ${targetTransaction.operationName}`);
            return false;
        }
    }

    // 🔧 事务系统：清空事务栈
    clearTransactionStack() {
        this.xmlTransactionStack = [];
        console.log('🗑️ 事务栈已清空');
    }

    // 🔧 事务系统：获取事务历史
    getTransactionHistory() {
        return this.xmlTransactionStack.map(t => ({
            id: t.id,
            operationName: t.operationName,
            timestamp: t.timestamp
        }));
    }

    // 🔧 测试验证：验证XML单一数据源架构的完整性
    validateXMLArchitecture() {
        console.log('🧪 开始验证XML单一数据源架构...');

        const validationResults = {
            xmlConsistency: true,
            noteIdConsistency: true,
            chordStructureConsistency: true,
            dataFlowConsistency: true,
            errors: []
        };

        try {
            // 1. 验证XML格式正确性
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(this.currentXML, 'application/xml');
            const parseError = xmlDoc.querySelector('parsererror');
            if (parseError) {
                validationResults.xmlConsistency = false;
                validationResults.errors.push('XML格式错误: ' + parseError.textContent);
            }

            // 2. 验证音符ID一致性
            const noteElements = xmlDoc.querySelectorAll('note[data-note-id]');
            const xmlNoteIds = Array.from(noteElements).map(el => parseInt(el.getAttribute('data-note-id')));
            const musicDataNoteIds = this.musicData?.notesList?.map(n => n.id) || [];

            const missingInXML = musicDataNoteIds.filter(id => !xmlNoteIds.includes(id));
            const missingInMusicData = xmlNoteIds.filter(id => !musicDataNoteIds.includes(id));

            if (missingInXML.length > 0 || missingInMusicData.length > 0) {
                validationResults.noteIdConsistency = false;
                if (missingInXML.length > 0) {
                    validationResults.errors.push(`XML中缺失音符ID: ${missingInXML.join(', ')}`);
                }
                if (missingInMusicData.length > 0) {
                    validationResults.errors.push(`musicData中缺失音符ID: ${missingInMusicData.join(', ')}`);
                }
            }

            // 3. 验证和弦结构一致性
            const chordGroups = {};
            this.musicData?.notesList?.forEach(note => {
                if (note.isChord && note.chordGroup) {
                    if (!chordGroups[note.chordGroup]) {
                        chordGroups[note.chordGroup] = [];
                    }
                    chordGroups[note.chordGroup].push(note);
                }
            });

            Object.entries(chordGroups).forEach(([chordGroup, notes]) => {
                // 检查和弦中是否有主音符（isChord=false）
                const mainNotes = notes.filter(n => !n.isChord);
                if (mainNotes.length !== 1) {
                    validationResults.chordStructureConsistency = false;
                    validationResults.errors.push(`和弦${chordGroup}主音符数量错误: ${mainNotes.length}`);
                }
            });

            // 4. 验证数据流一致性（XML -> musicData -> UI）
            const testXML = this.currentXML;
            const testMusicData = this.parseXMLData(testXML);
            if (!testMusicData) {
                validationResults.dataFlowConsistency = false;
                validationResults.errors.push('XML解析失败，数据流中断');
            }

            console.log('🧪 架构验证完成:', validationResults);
            return validationResults;

        } catch (error) {
            console.error('🧪 架构验证失败:', error);
            validationResults.xmlConsistency = false;
            validationResults.errors.push(`验证过程异常: ${error.message}`);
            return validationResults;
        }
    }

    // 🔧 测试验证：运行完整的系统测试
    async runSystemTests() {
        console.log('🧪 开始运行系统测试...');

        const testResults = {
            architectureValidation: null,
            operationTests: [],
            transactionTests: [],
            overallSuccess: true
        };

        try {
            // 1. 架构验证测试
            testResults.architectureValidation = this.validateXMLArchitecture();
            if (testResults.architectureValidation.errors.length > 0) {
                testResults.overallSuccess = false;
            }

            // 2. 基本操作测试
            console.log('🧪 测试基本操作...');

            // 保存当前状态
            const originalXML = this.currentXML;
            const originalTransactionStackSize = this.xmlTransactionStack.length;

            // 测试事务系统
            console.log('🧪 测试事务系统...');
            const testTransaction = {
                name: '事务回滚测试',
                success: false,
                error: null
            };

            try {
                // 执行一个测试操作
                this.pushTransaction('测试操作', this.currentXML);

                // 模拟操作失败并回滚
                const rollbackSuccess = this.rollbackLastTransaction();

                if (rollbackSuccess && this.currentXML === originalXML) {
                    testTransaction.success = true;
                    console.log('✅ 事务回滚测试通过');
                } else {
                    testTransaction.error = '事务回滚后状态不一致';
                    testResults.overallSuccess = false;
                }
            } catch (error) {
                testTransaction.error = error.message;
                testResults.overallSuccess = false;
            }

            testResults.transactionTests.push(testTransaction);

            // 恢复原始状态
            this.currentXML = originalXML;
            this.xmlTransactionStack = this.xmlTransactionStack.slice(0, originalTransactionStackSize);

            console.log('🧪 系统测试完成:', testResults);
            return testResults;

        } catch (error) {
            console.error('🧪 系统测试失败:', error);
            testResults.overallSuccess = false;
            testResults.operationTests.push({
                name: '系统测试异常',
                success: false,
                error: error.message
            });
            return testResults;
        }
    }

    // 🔧 增强的音符查找方法：在XML中查找指定的音符元素
    findNoteElementInXML(xmlDoc, noteData) {
        console.log(`🔍 查找音符元素: ID=${noteData.id}, ${noteData.pitch}${noteData.octave}, 小节${noteData.measure}, 谱表${noteData.staff}, defaultX=${noteData.defaultX}`);

        // 方法1: 通过data-note-id查找（如果存在）
        let noteElement = null;
        try {
            noteElement = xmlDoc.querySelector(`note[data-note-id="${noteData.id}"]`);
            if (noteElement) {
                console.log(`🔍 通过data-note-id找到音符元素: ${noteData.id}`);
                return noteElement;
            }
        } catch (e) {
            console.warn(`⚠️ querySelector失败:`, e.message);
        }

        // 方法2: 遍历查找匹配的音符（更精确的匹配）
        console.log(`🔍 开始遍历查找音符: ${noteData.pitch}${noteData.octave} (ID=${noteData.id})`);

        const currentNotes = xmlDoc.querySelectorAll('note');
        const targetPitch = noteData.pitch + noteData.octave;

        // 收集所有匹配的候选音符
        const candidates = [];

        for (let i = 0; i < currentNotes.length; i++) {
            const el = currentNotes[i];

            // 首先检查是否有data-note-id属性匹配
            const elementId = el.getAttribute('data-note-id');
            if (elementId && elementId === noteData.id.toString()) {
                console.log(`🔍 通过遍历找到精确ID匹配的音符: ID=${noteData.id}`);
                return el;
            }

            const pitchElement = el.querySelector('pitch');
            const isRest = el.querySelector('rest') !== null;

            if (!isRest && pitchElement) {
                const step = pitchElement.querySelector('step')?.textContent;
                const alter = pitchElement.querySelector('alter')?.textContent;
                const octave = pitchElement.querySelector('octave')?.textContent;

                if (step && octave) {
                    // 处理升降号
                    let candidatePitch = step;
                    if (alter) {
                        const alterValue = parseInt(alter);
                        if (alterValue > 0) {
                            candidatePitch += '#'.repeat(alterValue);
                        } else if (alterValue < 0) {
                            candidatePitch += 'b'.repeat(Math.abs(alterValue));
                        }
                    }
                    candidatePitch += octave;

                    if (candidatePitch === targetPitch) {
                        // 检查小节和谱表是否匹配
                        const measureElement = el.closest('measure');
                        const measureNumber = measureElement?.getAttribute('number');
                        const staffElement = el.querySelector('staff');
                        const staffNumber = staffElement ? parseInt(staffElement.textContent) : null;

                        // 检查是否在同一小节和谱表
                        if (parseInt(measureNumber) === noteData.measure && staffNumber === noteData.staff) {
                            const candidateDefaultX = parseFloat(el.getAttribute('default-x') || '0');
                            candidates.push({
                                element: el,
                                index: i,
                                hasId: !!elementId,
                                id: elementId,
                                defaultX: candidateDefaultX
                            });
                        }
                    }
                }
            }
        }

        // 如果有候选音符，选择最合适的
        if (candidates.length > 0) {
            let bestCandidate = null;

            if (candidates.length === 1) {
                bestCandidate = candidates[0];
            } else {
                // 多个候选音符时，使用defaultX进行匹配
                console.log(`🔍 多个候选音符，使用defaultX进行精确匹配，目标defaultX: ${noteData.defaultX}`);

                for (const candidate of candidates) {
                    console.log(`🔍 候选音符defaultX: ${candidate.defaultX}`);

                    // 如果defaultX匹配（误差小于5），则选择这个候选音符
                    if (Math.abs(candidate.defaultX - noteData.defaultX) < 5) {
                        bestCandidate = candidate;
                        console.log(`🔍 找到defaultX匹配的候选音符: ${candidate.defaultX} ≈ ${noteData.defaultX}`);
                        break;
                    }
                }

                // 如果没有defaultX匹配，则选择有ID的音符，或者第一个
                if (!bestCandidate) {
                    bestCandidate = candidates.find(c => c.hasId) || candidates[0];
                    console.log(`🔍 没有defaultX匹配，使用备选方案`);
                }
            }

            if (bestCandidate) {
                console.log(`🔍 从${candidates.length}个候选音符中选择: 索引${bestCandidate.index}, ID=${bestCandidate.id || '无'}`);
                return bestCandidate.element;
            }
        }

        console.warn(`⚠️ 未找到匹配的音符元素: ${noteData.pitch}${noteData.octave} (ID=${noteData.id})`);
        return null;
    }

    // 🔧 核心方法：创建音符XML元素
    createNoteXMLElement(xmlDoc, noteData) {
        const noteElement = xmlDoc.createElement('note');
        noteElement.setAttribute('data-note-id', noteData.id.toString());

        // 🔧 重要修复：设置default-x属性
        if (noteData.defaultX !== undefined) {
            noteElement.setAttribute('default-x', noteData.defaultX.toString());
            console.log(`🔧 设置default-x: ${noteData.defaultX}`);
        } else {
            console.warn(`⚠️ 音符${noteData.pitch}${noteData.octave}没有defaultX值`);
        }

        if (noteData.isRest) {
            // 创建休止符
            const restElement = xmlDoc.createElement('rest');
            noteElement.appendChild(restElement);
        } else {
            // 创建音符
            const pitchElement = xmlDoc.createElement('pitch');

            const stepElement = xmlDoc.createElement('step');
            stepElement.textContent = noteData.pitch;
            pitchElement.appendChild(stepElement);

            const octaveElement = xmlDoc.createElement('octave');
            octaveElement.textContent = noteData.octave.toString();
            pitchElement.appendChild(octaveElement);

            noteElement.appendChild(pitchElement);
        }

        // 添加时值
        const durationElement = xmlDoc.createElement('duration');
        durationElement.textContent = noteData.duration.toString();
        noteElement.appendChild(durationElement);

        // 添加声部
        if (noteData.voice) {
            const voiceElement = xmlDoc.createElement('voice');
            voiceElement.textContent = noteData.voice.toString();
            noteElement.appendChild(voiceElement);
        }

        // 添加音符类型
        const typeElement = xmlDoc.createElement('type');
        // 🔧 完整的MusicXML type映射（基于MusicXML 4.1官方文档）
        const typeMap = {
            // 基于duration值映射到MusicXML type值
            0.25: '1024th',    // 1024分音符
            0.5: '512th',      // 512分音符
            1: '256th',        // 256分音符
            2: '128th',        // 128分音符
            4: '64th',         // 64分音符
            8: '32nd',         // 32分音符
            16: '16th',        // 16分音符
            32: 'eighth',      // 八分音符
            64: 'quarter',     // 四分音符
            128: 'half',       // 二分音符
            256: 'whole',      // 全音符
            512: 'breve',      // 二全音符
            1024: 'long',      // 长音符
            2048: 'maxima'     // 最长音符
        };

        // 🔧 修复：根据实际的duration值映射
        // 我们的duration值表示每小节的分割数，需要转换为标准的音符时值
        let mappedType;
        if (noteData.duration === 1) {
            mappedType = 'whole';      // 全音符
        } else if (noteData.duration === 2) {
            mappedType = 'half';       // 二分音符
        } else if (noteData.duration === 4) {
            mappedType = 'quarter';    // 四分音符
        } else if (noteData.duration === 8) {
            mappedType = 'eighth';     // 八分音符
        } else if (noteData.duration === 16) {
            mappedType = '16th';       // 十六分音符
        } else if (noteData.duration === 32) {
            mappedType = '32nd';       // 三十二分音符
        } else if (noteData.duration === 64) {
            mappedType = '64th';       // 六十四分音符
        } else if (noteData.duration === 128) {
            mappedType = '128th';      // 一百二十八分音符
        } else if (noteData.duration === 256) {
            mappedType = '256th';      // 二百五十六分音符
        } else {
            mappedType = 'quarter';    // 默认四分音符
        }

        typeElement.textContent = mappedType;
        noteElement.appendChild(typeElement);

        // 添加附点
        if (noteData.dotted) {
            const dotElement = xmlDoc.createElement('dot');
            noteElement.appendChild(dotElement);
        }

        // 🔧 添加stem元素（如果不是休止符）
        if (!noteData.isRest) {
            const stemElement = xmlDoc.createElement('stem');
            // 根据音高决定stem方向，这里使用简单的规则
            const octave = parseInt(noteData.octave);
            if (octave >= 4) {
                stemElement.textContent = 'down';
                stemElement.setAttribute('default-y', '-35');
            } else {
                stemElement.textContent = 'up';
                stemElement.setAttribute('default-y', '15');
            }
            noteElement.appendChild(stemElement);
        }

        // 🔧 添加staff元素
        if (noteData.staff) {
            const staffElement = xmlDoc.createElement('staff');
            staffElement.textContent = noteData.staff.toString();
            noteElement.appendChild(staffElement);
        }

        return noteElement;
    }

    // 🔧 核心方法：更新音符XML元素
    updateNoteXMLElement(noteElement, noteData) {
        // 更新音高信息
        if (noteData.isRest) {
            // 转换为休止符
            const existingPitch = noteElement.querySelector('pitch');
            if (existingPitch) {
                noteElement.removeChild(existingPitch);
            }

            let restElement = noteElement.querySelector('rest');
            if (!restElement) {
                restElement = noteElement.ownerDocument.createElement('rest');
                noteElement.insertBefore(restElement, noteElement.firstChild);
            }
        } else {
            // 转换为音符
            const existingRest = noteElement.querySelector('rest');
            if (existingRest) {
                noteElement.removeChild(existingRest);
            }

            let pitchElement = noteElement.querySelector('pitch');
            if (!pitchElement) {
                pitchElement = noteElement.ownerDocument.createElement('pitch');
                noteElement.insertBefore(pitchElement, noteElement.firstChild);
            }

            // 更新音高
            let stepElement = pitchElement.querySelector('step');
            if (!stepElement) {
                stepElement = noteElement.ownerDocument.createElement('step');
                pitchElement.appendChild(stepElement);
            }
            stepElement.textContent = noteData.pitch;

            // 更新八度
            let octaveElement = pitchElement.querySelector('octave');
            if (!octaveElement) {
                octaveElement = noteElement.ownerDocument.createElement('octave');
                pitchElement.appendChild(octaveElement);
            }
            octaveElement.textContent = noteData.octave.toString();
        }

        // 更新时值
        const durationElement = noteElement.querySelector('duration');
        if (durationElement) {
            durationElement.textContent = noteData.duration.toString();
        }

        // 更新音符类型
        const typeElement = noteElement.querySelector('type');
        if (typeElement) {
            // 🔧 使用正确的MusicXML type值
            let mappedType;
            if (noteData.duration === 1) {
                mappedType = 'whole';
            } else if (noteData.duration === 2) {
                mappedType = 'half';
            } else if (noteData.duration === 4) {
                mappedType = 'quarter';
            } else if (noteData.duration === 8) {
                mappedType = 'eighth';
            } else if (noteData.duration === 16) {
                mappedType = '16th';       // 🔧 修复：使用正确的MusicXML值
            } else if (noteData.duration === 32) {
                mappedType = '32nd';
            } else if (noteData.duration === 64) {
                mappedType = '64th';
            } else if (noteData.duration === 128) {
                mappedType = '128th';
            } else {
                mappedType = 'quarter';
            }
            typeElement.textContent = mappedType;
        }

        // 更新附点
        const existingDot = noteElement.querySelector('dot');
        if (noteData.dotted && !existingDot) {
            const dotElement = noteElement.ownerDocument.createElement('dot');
            noteElement.appendChild(dotElement);
        } else if (!noteData.dotted && existingDot) {
            noteElement.removeChild(existingDot);
        }
    }

    parseXMLData(xmlContent) {
        try {
            console.log('🔧 开始解析XML数据...');
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xmlContent, 'application/xml');

            // 检查XML解析是否成功
            const parseError = xmlDoc.getElementsByTagName('parsererror');
            if (parseError.length > 0) {
                console.error('XML解析错误:', parseError[0].textContent);
                throw new Error('XML格式错误');
            }

            // 标记所有原有音符（没有data-added-by-editor属性的音符）
            const allNotes = xmlDoc.querySelectorAll('note');
            allNotes.forEach(note => {
                if (!note.hasAttribute('data-added-by-editor')) {
                    note.setAttribute('data-original-note', 'true');
                }
            });

            // 更新XML内容（包含新的标记）
            const serializer = new XMLSerializer();
            this.currentXML = serializer.serializeToString(xmlDoc);

            // 解析基本信息
            const data = {
                keySignature: 0,
                timeSignature: '4/4',
                tempo: '',
                measures: 0,
                notes: 0,
                staffs: 0,
                notesList: [],
                // 标题信息
                mainTitle: '',
                subTitle: '',
                composer: '',
                originalArtist: '',
                arranger: '',
                copyright: ''
            };

            // 解析标题信息
            const workTitle = xmlDoc.querySelector('work work-title');
            if (workTitle) {
                data.mainTitle = workTitle.textContent.trim();
            }

            const movementTitle = xmlDoc.querySelector('movement-title');
            if (movementTitle) {
                data.subTitle = movementTitle.textContent.trim();
            }

            // 解析创作者信息 - 添加调试日志
            const creators = xmlDoc.querySelectorAll('creator');
            console.log('找到的creator元素数量:', creators.length);

            creators.forEach((creator, index) => {
                const type = creator.getAttribute('type');
                const content = creator.textContent.trim();
                console.log(`Creator ${index}: type="${type}", content="${content}"`);

                // 正确分配创作者信息
                if (type === 'composer' && !data.composer) {
                    data.composer = content;
                    console.log('设置作曲为:', content);
                } else if (type === 'lyricist' && !data.originalArtist) {
                    data.originalArtist = content;
                    console.log('设置原唱为:', content);
                } else if (type === 'arranger' && !data.arranger) {
                    data.arranger = content;
                    console.log('🔥 设置编曲为:', content);
                } else if (type === 'arranger' && data.arranger) {
                    console.log('🔥 编曲信息已存在，跳过:', content, '现有:', data.arranger);
                }
            });

            // 解析版权信息
            const rights = xmlDoc.querySelector('rights');
            if (rights) {
                data.copyright = rights.textContent.trim();
            }

            // 解析调号
            const keyElement = xmlDoc.querySelector('key fifths');
            if (keyElement) {
                data.keySignature = parseInt(keyElement.textContent) || 0;
            }

            // 解析拍号
            const timeElements = xmlDoc.querySelectorAll('time');
            if (timeElements.length > 0) {
                const beats = xmlDoc.querySelector('time beats');
                const beatType = xmlDoc.querySelector('time beat-type');
                if (beats && beatType) {
                    data.timeSignature = `${beats.textContent}/${beatType.textContent}`;
                }
            }

            // 统计小节数（包括隐式小节0）
            const allMeasures = xmlDoc.querySelectorAll('measure');
            data.measures = allMeasures.length;

            // 检查是否有隐式小节0
            const implicitMeasure = xmlDoc.querySelector('measure[number="0"][implicit="yes"]');
            if (implicitMeasure) {
                console.log('发现隐式小节0，总小节数:', data.measures);
            }

            // 解析音符列表
            const notes = xmlDoc.querySelectorAll('note');
            data.notes = notes.length;

            console.log(`🎵 开始解析音符，总共找到 ${notes.length} 个note元素`);

            notes.forEach((note, index) => {
                // 添加详细的音符解析调试信息
                const measureElement = note.closest('measure');
                const measureNumber = measureElement ? measureElement.getAttribute('number') : 'unknown';
                const pitchElement = note.querySelector('pitch');
                const stepElement = pitchElement ? pitchElement.querySelector('step') : null;
                const octaveElement = pitchElement ? pitchElement.querySelector('octave') : null;
                const isRest = note.querySelector('rest') !== null;

                console.log(`🔍 解析第${index}个音符: measure=${measureNumber}, ${isRest ? 'Rest' : (stepElement?.textContent || 'Unknown') + (octaveElement?.textContent || '')}`);

                const noteData = this.parseNoteData(note, index);
                if (noteData) {
                    console.log(`✅ 音符解析成功: ID=${noteData.id}, ${noteData.pitch}${noteData.octave}, measure=${noteData.measure}`);
                    data.notesList.push(noteData);
                } else {
                    console.warn(`❌ 音符解析失败: index=${index}`);
                }
            });

            // 统计声部数
            data.staffs = xmlDoc.querySelectorAll('part').length;

            // 保存XML文档引用
            data.xmlDoc = xmlDoc;

            // 解析连线信息
            this.parseConnectionsFromXML(xmlDoc, data.notesList);

            // 🔧 重要：按音乐时间顺序排序notesList
            // 这确保groupNotesWithChords方法能正确分组和弦
            data.notesList.sort((a, b) => {
                // 首先按小节排序
                if (a.measure !== b.measure) {
                    return a.measure - b.measure;
                }
                // 然后按谱表排序
                if (a.staff !== b.staff) {
                    return a.staff - b.staff;
                }
                // 然后按default-x位置排序（时间顺序）
                if (a.defaultX !== b.defaultX) {
                    return a.defaultX - b.defaultX;
                }
                // 然后按声部排序
                if (a.voice !== b.voice) {
                    return a.voice - b.voice;
                }
                // 最后按XML索引排序
                return (a.xmlIndex || 0) - (b.xmlIndex || 0);
            });

            console.log('🔧 notesList已按音乐时间顺序排序');

            // 输出最终解析结果
            console.log('XML解析完成，最终数据:', {
                mainTitle: data.mainTitle,
                subTitle: data.subTitle,
                originalArtist: data.originalArtist,
                arranger: data.arranger,
                copyright: data.copyright
            });

            return data;

        } catch (error) {
            console.error('XML解析失败:', error);
            return {
                keySignature: 0,
                timeSignature: '4/4',
                tempo: '',
                measures: 0,
                notes: 0,
                staffs: 0,
                notesList: []
            };
        }
    }

    parseNoteData(noteElement, index) {
        try {
            // 🔧 重构：使用全局ID管理器
            const savedNoteId = noteElement.getAttribute('data-note-id');
            let noteId;

            if (savedNoteId) {
                // 如果XML中已有ID，使用它并更新计数器
                noteId = parseInt(savedNoteId);
                this.globalNoteIdCounter = Math.max(this.globalNoteIdCounter, noteId + 1);
            } else {
                // 如果没有ID，生成新的全局唯一ID并保存到XML
                noteId = this.generateNoteId();
                noteElement.setAttribute('data-note-id', noteId.toString());
            }

            // 添加详细的音符解析调试
            const measureElement = noteElement.closest('measure');
            const measureNumber = measureElement ? measureElement.getAttribute('number') : 'unknown';
            console.log(`🔍 parseNoteData: index=${index}, noteId=${noteId}, measureNumber=${measureNumber}`);

            const noteData = {
                id: noteId,
                isRest: false,
                pitch: '',
                octave: 4,
                duration: 4,
                measure: 1,
                voice: 1,
                staff: 1,  // 声部：1=高音谱表，2=低音谱表
                clef: 'treble',  // 谱号类型
                isChord: false,  // 是否是和弦的一部分
                chordGroup: null,  // 和弦组ID
                defaultX: 0,  // XML中的default-x位置，用于时间排序
                xmlIndex: index  // 在XML中的原始索引
            };

            // 检查是否是和弦的一部分
            if (noteElement.querySelector('chord')) {
                noteData.isChord = true;
                // 和弦组ID基于前一个音符的位置
                noteData.chordGroup = `chord_${index - 1}`;
            }

            // 检查是否是休止符
            if (noteElement.querySelector('rest')) {
                noteData.isRest = true;
                noteData.pitch = 'Rest';
            } else {
                // 解析音高
                const pitchElement = noteElement.querySelector('pitch');
                if (pitchElement) {
                    const step = pitchElement.querySelector('step');
                    const octave = pitchElement.querySelector('octave');
                    const alter = pitchElement.querySelector('alter');

                    if (step) noteData.pitch = step.textContent;
                    if (octave) noteData.octave = parseInt(octave.textContent);
                    if (alter) {
                        const alterValue = parseInt(alter.textContent);
                        if (alterValue > 0) noteData.pitch += '#'.repeat(alterValue);
                        if (alterValue < 0) noteData.pitch += 'b'.repeat(Math.abs(alterValue));
                    }
                }
            }

            // 解析时值 - 使用type标签而不是duration值
            const typeElement = noteElement.querySelector('type');
            const durationElement = noteElement.querySelector('duration');

            if (typeElement) {
                const typeText = typeElement.textContent;
                const typeMapping = {
                    'whole': 1,
                    'half': 2,
                    'quarter': 4,
                    'eighth': 8,
                    'sixteenth': 16,  // 🔧 修复：支持标准的MusicXML类型名称
                    '16th': 16,       // 🔧 兼容：同时支持简写形式
                    '32nd': 32,
                    '64th': 64
                };
                noteData.duration = typeMapping[typeText] || 4;
            } else if (durationElement) {
                // 如果没有type标签，使用duration值（备用方案）
                noteData.duration = parseInt(durationElement.textContent) || 4;
            }

            // 解析附点
            const dotElement = noteElement.querySelector('dot');
            noteData.dotted = !!dotElement; // 转换为布尔值
            if (noteData.dotted) {
                console.log(`🔍 发现附点音符: ${noteData.pitch}${noteData.octave}`);
            }

            // 解析小节号
            const measure = noteElement.closest('measure');
            if (measure) {
                const measureNumber = measure.getAttribute('number');
                noteData.measure = parseInt(measureNumber);

                // 处理小节0（隐式小节）
                if (measureNumber === '0') {
                    noteData.isImplicitMeasure = true;
                }

                // 调试：确保小节编号正确
                console.log(`🔍 小节编号解析: XML=${measureNumber} -> noteData.measure=${noteData.measure}`);
            }

            // 解析声部
            const voice = noteElement.querySelector('voice');
            if (voice) {
                noteData.voice = parseInt(voice.textContent) || 1;
            }

            // 解析谱表信息
            const staff = noteElement.querySelector('staff');
            if (staff) {
                noteData.staff = parseInt(staff.textContent) || 1;
                // 根据谱表判断谱号
                noteData.clef = noteData.staff === 1 ? 'treble' : 'bass';
            } else {
                // 如果没有明确的谱表信息，根据音高判断
                if (!noteData.isRest && noteData.octave <= 3) {
                    noteData.staff = 2;
                    noteData.clef = 'bass';
                } else {
                    noteData.staff = 1;
                    noteData.clef = 'treble';
                }
            }

            // 解析位置信息（用于时间排序）
            const defaultX = noteElement.getAttribute('default-x');
            if (defaultX) {
                noteData.defaultX = parseFloat(defaultX);
            }

            return noteData;
        } catch (error) {
            console.error('解析音符数据失败:', error);
            return null;
        }
    }

    updateUI() {
        if (!this.musicData) return;

        console.log('开始更新UI');

        // 安全更新元素的辅助函数
        const safeUpdateElement = (id, value, property = 'textContent') => {
            const element = document.getElementById(id);
            if (element) {
                element[property] = value;
                console.log(`更新元素成功: ${id} = ${value}`);
            } else {
                console.warn(`元素不存在，跳过更新: ${id}`);
            }
        };

        // 更新标题信息
        console.log('updateUI - 准备更新标题信息:', {
            mainTitle: this.musicData.mainTitle,
            subTitle: this.musicData.subTitle,
            composer: this.musicData.composer,
            originalArtist: this.musicData.originalArtist,
            arranger: this.musicData.arranger,
            copyright: this.musicData.copyright
        });

        safeUpdateElement('main-title', this.musicData.mainTitle || '', 'value');
        safeUpdateElement('sub-title', this.musicData.subTitle || '', 'value');
        safeUpdateElement('composer', this.musicData.composer || '', 'value');
        safeUpdateElement('original-artist', this.musicData.originalArtist || '', 'value');
        safeUpdateElement('arranger', this.musicData.arranger || '', 'value');
        safeUpdateElement('copyright', this.musicData.copyright || '', 'value');

        // 更新表单控件
        safeUpdateElement('key-signature', this.musicData.keySignature, 'value');
        safeUpdateElement('time-signature', this.musicData.timeSignature, 'value');

        // 更新统计信息
        safeUpdateElement('measure-count', this.musicData.measures);
        safeUpdateElement('note-count', this.musicData.notes);
        safeUpdateElement('staff-count', this.musicData.staffs);

        // 更新调号显示
        const keyNames = {
            '-4': 'Ab大调 (4♭)',
            '-3': 'Eb大调 (3♭)',
            '-2': 'Bb大调 (2♭)',
            '-1': 'F大调 (1♭)',
            '0': 'C大调',
            '1': 'G大调 (1♯)',
            '2': 'D大调 (2♯)',
            '3': 'A大调 (3♯)',
            '4': 'E大调 (4♯)'
        };
        const keyDisplayText = keyNames[this.musicData.keySignature] || 'C大调';
        safeUpdateElement('key-display', keyDisplayText);

        // 更新拍号显示
        safeUpdateElement('time-display', this.musicData.timeSignature || '4/4');

        console.log('UI更新完成');
    }

    groupNotesWithChords(notesList) {
        const result = [];
        const processedNotes = new Set(); // 记录已处理的音符ID

        for (let i = 0; i < notesList.length; i++) {
            const currentNote = notesList[i];

            // 跳过已处理的音符
            if (processedNotes.has(currentNote.id)) {
                continue;
            }

            if (currentNote.isChord) {
                // 跳过和弦音符，它们会被主音符处理
                processedNotes.add(currentNote.id);
                continue;
            }

            // 查找与当前主音符在相同位置的所有和弦音符
            const chordNotes = [currentNote];
            processedNotes.add(currentNote.id);

            // 使用defaultX位置和容差来查找和弦音符
            const tolerance = 5;
            for (let j = 0; j < notesList.length; j++) {
                if (j === i || processedNotes.has(notesList[j].id)) {
                    continue; // 跳过当前音符和已处理的音符
                }

                const otherNote = notesList[j];
                if (otherNote.isChord &&
                    otherNote.measure === currentNote.measure &&
                    otherNote.staff === currentNote.staff &&
                    Math.abs(otherNote.defaultX - currentNote.defaultX) < tolerance) {
                    chordNotes.push(otherNote);
                    processedNotes.add(otherNote.id);
                }
            }

            if (chordNotes.length > 1) {
                // 这是一个和弦
                result.push({
                    id: `chord_${currentNote.id}`,
                    type: 'chord',
                    notes: chordNotes,
                    measure: currentNote.measure,
                    staff: currentNote.staff,
                    clef: currentNote.clef,
                    duration: currentNote.duration,
                    dotted: currentNote.dotted,
                    position: i
                });
            } else {
                // 这是一个单音符
                result.push({
                    id: `single_${currentNote.id}`,
                    type: 'single',
                    notes: chordNotes,
                    measure: currentNote.measure,
                    staff: currentNote.staff,
                    clef: currentNote.clef,
                    duration: currentNote.duration,
                    dotted: currentNote.dotted,
                    position: i
                });
            }
        }

        return result;
    }

    /**
     * 找到音符组在该小节该谱表内的相对位置（用于插入操作）
     * @param {Object} noteGroup 音符组对象
     * @returns {number} 在该小节该谱表内的相对位置
     */
    findActualPositionInNotesList(noteGroup) {
        // 获取该小节该谱表的所有音符，按时间顺序排列
        const notesInMeasureStaff = this.musicData.notesList.filter(n =>
            n.measure === noteGroup.measure && n.staff === noteGroup.staff
        );

        // 按defaultX位置排序（时间顺序）
        notesInMeasureStaff.sort((a, b) => a.defaultX - b.defaultX);

        if (noteGroup.type === 'chord') {
            // 对于和弦，找到和弦中第一个音符的位置（主音符，isChord: false）
            // insertNoteAfter方法会自动找到整个和弦的结束位置
            const mainNote = noteGroup.notes.find(note => !note.isChord);
            const firstNote = mainNote || noteGroup.notes[0];
            const relativeIndex = notesInMeasureStaff.findIndex(n => n.id === firstNote.id);
            console.log(`🔍 和弦位置计算: ${noteGroup.notes.map(n => n.pitch + n.octave).join('+')} 在小节${noteGroup.measure}谱表${noteGroup.staff}的起始位置: ${relativeIndex} (主音符: ${firstNote.pitch}${firstNote.octave})`);
            return relativeIndex;
        } else {
            // 对于单音符，找到该音符的位置
            const note = noteGroup.notes[0];
            const relativeIndex = notesInMeasureStaff.findIndex(n => n.id === note.id);
            console.log(`🔍 单音符位置计算: ${note.pitch}${note.octave} 在小节${noteGroup.measure}谱表${noteGroup.staff}的位置: ${relativeIndex}`);
            return relativeIndex;
        }
    }

    loadNotesList() {
        const container = document.getElementById('notes-container');

        if (!this.musicData || !this.musicData.notesList || this.musicData.notesList.length === 0) {
            container.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    没有识别到音符，您可以手动添加音符。
                </div>
            `;
            return;
        }

        // 处理和弦分组
        const processedNotes = this.groupNotesWithChords(this.musicData.notesList);

        // 按小节和谱表分组显示音符
        const notesByMeasureAndStaff = {};
        processedNotes.forEach(noteGroup => {
            const key = `${noteGroup.measure}-${noteGroup.staff}`;
            if (!notesByMeasureAndStaff[key]) {
                notesByMeasureAndStaff[key] = {
                    measure: noteGroup.measure,
                    staff: noteGroup.staff,
                    clef: noteGroup.clef || 'treble',
                    noteGroups: []
                };
            }
            notesByMeasureAndStaff[key].noteGroups.push(noteGroup);
        });

        // 按小节号排序（确保小节0排在最前面）
        const sortedGroups = Object.values(notesByMeasureAndStaff).sort((a, b) => {
            if (a.measure !== b.measure) {
                return a.measure - b.measure;
            }
            return a.staff - b.staff;
        });

        let html = '';
        let currentMeasure = -1;

        sortedGroups.forEach(group => {
            // 如果是新小节，添加小节标题
            if (group.measure !== currentMeasure) {
                if (currentMeasure !== -1) {
                    html += '</div>'; // 关闭前一个小节的容器
                }

                // 处理小节编号显示（兼容两种情况：修复后的编号从1开始，未修复的从0开始）
                const isPrepareMeasure = group.measure === 0 || group.measure === 1;
                const displayNumber = group.measure === 0 ? 1 : group.measure;
                const measureDisplayNumber = isPrepareMeasure && group.measure <= 1 ?
                    `第 ${displayNumber} 小节 (预备小节)` :
                    `第 ${displayNumber} 小节`;
                const measureClass = isPrepareMeasure && group.measure <= 1 ? 'bg-secondary' : 'bg-primary';

                html += `
                    <div class="card mb-4">
                        <div class="card-header ${measureClass} text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-music me-2"></i>${measureDisplayNumber}
                                ${isPrepareMeasure && group.measure <= 1 ? '<small class="ms-2">(拍号、调号设置)</small>' : ''}
                            </h5>
                        </div>
                        <div class="card-body">
                `;
                currentMeasure = group.measure;
            }

            // 添加谱表标题和音符
            const clefIcon = group.clef === 'treble' ? 'fa-music' : 'fa-music';
            const clefName = group.clef === 'treble' ? '高音谱表' : '低音谱表';
            const clefColor = group.clef === 'treble' ? 'primary' : 'success';

            html += `
                <div class="mb-3">
                    <h6 class="text-${clefColor}">
                        <i class="fas ${clefIcon} me-2"></i>${clefName}
                    </h6>
                    <div class="row">
            `;

            // 在同一小节同一谱表内按时间顺序排序音符组
            group.noteGroups.sort((a, b) => {
                // 首先按default-x位置排序（时间顺序）
                if (a.notes[0].defaultX !== b.notes[0].defaultX) {
                    return a.notes[0].defaultX - b.notes[0].defaultX;
                }
                // 然后按声部排序
                if (a.notes[0].voice !== b.notes[0].voice) {
                    return a.notes[0].voice - b.notes[0].voice;
                }
                // 最后按XML索引排序
                return a.notes[0].xmlIndex - b.notes[0].xmlIndex;
            });

            group.noteGroups.forEach((noteGroup, index) => {
                const durationName = this.getDurationName(noteGroup.duration, noteGroup.dotted);
                const noteColor = group.clef === 'treble' ? 'border-primary' : 'border-success';
                const position = index + 1;

                // 计算在notesList中的实际位置（用于插入操作）
                const actualPosition = this.findActualPositionInNotesList(noteGroup);

                if (noteGroup.type === 'chord') {
                    // 和弦显示
                    const chordNotes = noteGroup.notes.map(n => n.isRest ? '休止符' : `${n.pitch}${n.octave}`).join(' + ');

                    html += `
                        <div class="col-12 mb-1">
                            <div class="card border-info" style="border-width: 2px; cursor: pointer;"
                                 onclick="visualEditor.handleChordCardClick('${noteGroup.id}')"
                                 title="${this.generateChordTooltip(noteGroup.notes)}">
                                <div class="card-body p-2 bg-info bg-opacity-10">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-layer-group text-info me-2"></i>
                                                <span class="fw-bold text-info">和弦</span>
                                                <span class="badge bg-secondary ms-2">${position}</span>
                                                ${this.generateChordConnectionBadges(noteGroup.notes)}
                                            </div>
                                            <div class="mt-1">
                                                <span class="fw-bold">${chordNotes}</span>
                                                <span class="text-muted ms-2">${durationName}</span>
                                            </div>
                                        </div>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary btn-sm px-2" onclick="visualEditor.editChord('${noteGroup.id}', ${noteGroup.position})" title="编辑和弦">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm px-2" onclick="visualEditor.moveChordUp('${noteGroup.id}', ${noteGroup.position})" title="上移">
                                                <i class="fas fa-arrow-up"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm px-2" onclick="visualEditor.moveChordDown('${noteGroup.id}', ${noteGroup.position})" title="下移">
                                                <i class="fas fa-arrow-down"></i>
                                            </button>
                                            <button class="btn btn-outline-warning btn-sm px-2" onclick="visualEditor.insertNoteAfter(${actualPosition}, ${group.measure}, ${group.staff})" title="在此后插入">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            ${this.generateChordConnectionDeleteButtons(noteGroup.notes)}
                                            <button class="btn btn-outline-danger btn-sm px-2" onclick="visualEditor.deleteChord('${noteGroup.id}').catch(e => console.error('删除和弦失败:', e))" title="删除和弦">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    // 单音符显示
                    const note = noteGroup.notes[0];
                    const pitchDisplay = note.isRest ? '休止符' : `${note.pitch}${note.octave}`;

                    html += `
                        <div class="col-12 mb-1">
                            <div class="card ${noteColor}" style="border-width: 1px; cursor: pointer;"
                                 onclick="visualEditor.handleNoteCardClick(${note.id})"
                                 title="${this.generateNoteTooltip(note)}">
                                <div class="card-body p-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center">
                                                <span class="fw-bold">${pitchDisplay}</span>
                                                <span class="text-muted ms-2">${durationName}</span>
                                                <span class="badge bg-secondary ms-2">${position}</span>
                                                ${this.generateConnectionBadges(note.id)}
                                            </div>
                                        </div>
                                        <div class="btn-group btn-group-sm" onclick="event.stopPropagation()">
                                            <button class="btn btn-outline-primary btn-sm px-2" onclick="visualEditor.editNote(${note.id})" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm px-2" onclick="visualEditor.moveNoteUp(${note.id})" title="上移">
                                                <i class="fas fa-arrow-up"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm px-2" onclick="visualEditor.moveNoteDown(${note.id})" title="下移">
                                                <i class="fas fa-arrow-down"></i>
                                            </button>
                                            <button class="btn btn-outline-warning btn-sm px-2" onclick="visualEditor.insertNoteAfter(${actualPosition}, ${group.measure}, ${group.staff})" title="在此后插入">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            ${this.generateConnectionDeleteButtons(note.id)}
                                            <button class="btn btn-outline-danger btn-sm px-2" onclick="visualEditor.deleteNote(${note.id})" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }
            });

            html += `
                    </div>
                    <div class="btn-group btn-group-sm w-100">
                        <button class="btn btn-outline-${clefColor} btn-sm" onclick="visualEditor.addNoteToMeasureAndStaff(${group.measure}, ${group.staff}, 'single', 'end')">
                            <i class="fas fa-plus me-1"></i>添加单音符
                        </button>
                        <button class="btn btn-outline-${clefColor} btn-sm" onclick="visualEditor.addNoteToMeasureAndStaff(${group.measure}, ${group.staff}, 'chord', 'end')">
                            <i class="fas fa-layer-group me-1"></i>添加和弦
                        </button>
                    </div>
                </div>
            `;
        });

        if (currentMeasure !== -1) {
            // 结束当前小节
            html += `
                    </div>
                </div>
            `;
        }

        container.innerHTML = html;

        // 保存全局引用以便按钮调用
        window.visualEditor = this;
    }

    // 清理所有模态框遮罩层的紧急函数
    clearAllModals() {
        // 移除所有Bootstrap模态框
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            const bootstrapModal = bootstrap.Modal.getInstance(modal);
            if (bootstrapModal) {
                bootstrapModal.hide();
            }
            modal.remove();
        });

        // 移除所有遮罩层
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());

        // 恢复body的样式
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        console.log('已清理所有模态框和遮罩层');
    }

    getDurationName(duration, dotted = false) {
        const durationNames = {
            1: '全音符',
            2: '二分音符',
            4: '四分音符',
            8: '八分音符',
            16: '十六分音符',
            32: '三十二分音符',
            64: '六十四分音符'
        };
        const baseName = durationNames[duration] || `${duration}分音符`;
        return dotted ? `附点${baseName}` : baseName;
    }

    async generatePreview(forceRegenerate = false) {
        const previewImg = document.getElementById('recognition-preview');
        const loadingDiv = document.getElementById('preview-loading');
        const errorDiv = document.getElementById('preview-error');

        // 显示加载状态
        previewImg.classList.add('d-none');
        errorDiv.classList.add('d-none');
        loadingDiv.classList.remove('d-none');

        try {
            if (forceRegenerate) {
                // 强制重新生成预览
                console.log('强制重新生成预览，使用更新后的XML');
                await this.forceGenerateNewPreview(previewImg, loadingDiv, errorDiv);
            } else {
                // 首先尝试直接加载已存在的预览图
                const directPreviewUrl = `/api/file/${this.taskId}/preview`;
                console.log('尝试加载现有预览图:', directPreviewUrl);

                const testImg = new Image();
                testImg.onload = () => {
                    console.log('现有预览图加载成功');
                    previewImg.src = directPreviewUrl + '?t=' + Date.now();
                    previewImg.classList.remove('d-none');
                    loadingDiv.classList.add('d-none');
                };
                testImg.onerror = async () => {
                    console.log('现有预览图不存在，尝试生成新预览');
                    await this.forceGenerateNewPreview(previewImg, loadingDiv, errorDiv);
                };
                testImg.src = directPreviewUrl;
            }

        } catch (error) {
            console.error('预览加载失败:', error);
            errorDiv.classList.remove('d-none');
            loadingDiv.classList.add('d-none');
        }
    }

    async forceGenerateNewPreview(previewImg, loadingDiv, errorDiv) {
        try {
            console.log('🔄 开始强制生成新预览...');
            const response = await fetch('/api/generate-preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_id: this.taskId,
                    xml_content: this.currentXML
                })
            });

            const data = await response.json();
            console.log('🔄 预览生成响应:', data);

            if (data.success && data.preview_url) {
                previewImg.src = data.preview_url + '?t=' + Date.now();
                previewImg.classList.remove('d-none');
                console.log('✅ 新预览生成成功');
            } else {
                console.error('❌ 预览生成失败:', data.error);
                throw new Error(data.error || '预览生成失败');
            }
        } catch (error) {
            console.error('❌ 生成预览过程中发生错误:', error);
            errorDiv.classList.remove('d-none');
            errorDiv.textContent = '预览生成失败: ' + error.message;
        } finally {
            loadingDiv.classList.add('d-none');
        }
    }

    updateKeySignature() {
        const newKey = document.getElementById('key-signature').value;
        this.musicData.keySignature = parseInt(newKey);

        // 更新XML中的调号
        this.currentXML = this.updateXMLKeySignature(this.currentXML, newKey);
        this.updateUI();

        // 标记有未保存的修改
        this.hasUnsavedChanges = true;
        this.updateSaveButtonState();

        this.showMessage('调号已更新，请点击保存按钮应用修改', 'info');
    }

    updateTimeSignature() {
        const newTime = document.getElementById('time-signature').value;
        this.musicData.timeSignature = newTime;

        // 更新XML中的拍号
        this.currentXML = this.updateXMLTimeSignature(this.currentXML, newTime);

        // 标记有未保存的修改
        this.hasUnsavedChanges = true;
        this.updateSaveButtonState();

        this.showMessage('拍号已更新，请点击保存按钮应用修改', 'info');
    }

    updateTempo() {
        const newTempo = document.getElementById('tempo').value;
        this.musicData.tempo = newTempo;

        // 更新XML中的速度标记
        if (newTempo) {
            this.currentXML = this.updateXMLTempo(this.currentXML, newTempo);
            this.showMessage('速度标记已更新', 'success');
        }
    }

    updateTitleInfo() {
        // 获取标题信息
        const mainTitle = document.getElementById('main-title')?.value || '';
        const subTitle = document.getElementById('sub-title')?.value || '';
        const composer = document.getElementById('composer')?.value || '';
        const originalArtist = document.getElementById('original-artist')?.value || '';
        const arranger = document.getElementById('arranger')?.value || '';
        const copyright = document.getElementById('copyright')?.value || '';

        // 更新musicData
        this.musicData.mainTitle = mainTitle;
        this.musicData.subTitle = subTitle;
        this.musicData.composer = composer;
        this.musicData.originalArtist = originalArtist;
        this.musicData.arranger = arranger;
        this.musicData.copyright = copyright;

        // 更新XML中的标题信息
        console.log('🔄 准备更新XML标题信息:', {
            mainTitle, subTitle, composer, originalArtist, arranger, copyright
        });
        this.currentXML = this.updateXMLTitleInfo(this.currentXML, {
            mainTitle,
            subTitle,
            composer,
            originalArtist,
            arranger,
            copyright
        });
        console.log('✅ XML标题信息更新完成');

        // 标记有未保存的修改
        this.hasUnsavedChanges = true;
        this.updateSaveButtonState();

        this.showMessage('标题信息已更新，请点击保存按钮应用修改', 'info');
    }

    updateXMLKeySignature(xml, keyValue) {
        // 简单的XML更新，替换调号值
        return xml.replace(/<fifths>-?\d+<\/fifths>/g, `<fifths>${keyValue}</fifths>`);
    }

    updateXMLTimeSignature(xml, timeValue) {
        const [beats, beatType] = timeValue.split('/');
        xml = xml.replace(/<beats>\d+<\/beats>/g, `<beats>${beats}</beats>`);
        xml = xml.replace(/<beat-type>\d+<\/beat-type>/g, `<beat-type>${beatType}</beat-type>`);
        return xml;
    }

    updateXMLTempo(xml, tempoValue) {
        // 这里可以添加速度标记到XML中
        // 简化实现，实际需要更复杂的XML操作
        return xml;
    }

    updateXMLTitleInfo(xml, titleInfo) {
        try {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xml, 'application/xml');

            // 更新主标题 (work-title)
            let workTitle = xmlDoc.querySelector('work work-title');
            if (titleInfo.mainTitle) {
                if (!workTitle) {
                    // 如果不存在work元素，创建它
                    let work = xmlDoc.querySelector('work');
                    if (!work) {
                        work = xmlDoc.createElement('work');
                        const scorePartwise = xmlDoc.querySelector('score-partwise');
                        if (scorePartwise) {
                            scorePartwise.insertBefore(work, scorePartwise.firstChild);
                        }
                    }
                    workTitle = xmlDoc.createElement('work-title');
                    work.appendChild(workTitle);
                }
                workTitle.textContent = titleInfo.mainTitle;
            } else if (workTitle) {
                workTitle.remove();
            }

            // 更新副标题 (movement-title)
            let movementTitle = xmlDoc.querySelector('movement-title');
            if (titleInfo.subTitle) {
                if (!movementTitle) {
                    movementTitle = xmlDoc.createElement('movement-title');
                    const scorePartwise = xmlDoc.querySelector('score-partwise');
                    if (scorePartwise) {
                        // 插入到work元素之后
                        const work = xmlDoc.querySelector('work');
                        if (work && work.nextSibling) {
                            scorePartwise.insertBefore(movementTitle, work.nextSibling);
                        } else {
                            scorePartwise.insertBefore(movementTitle, scorePartwise.firstChild);
                        }
                    }
                }
                movementTitle.textContent = titleInfo.subTitle;
            } else if (movementTitle) {
                movementTitle.remove();
            }

            // 更新创作者信息
            const identification = xmlDoc.querySelector('identification') || this.createIdentificationElement(xmlDoc);

            // 清除现有的creator元素
            const existingCreators = identification.querySelectorAll('creator');
            existingCreators.forEach(creator => creator.remove());

            // 添加作曲信息
            if (titleInfo.composer) {
                const composer = xmlDoc.createElement('creator');
                composer.setAttribute('type', 'composer');
                composer.textContent = titleInfo.composer;
                identification.appendChild(composer);
            }

            // 添加原唱信息（使用lyricist类型）
            if (titleInfo.originalArtist) {
                const lyricist = xmlDoc.createElement('creator');
                lyricist.setAttribute('type', 'lyricist');
                lyricist.textContent = titleInfo.originalArtist;
                identification.appendChild(lyricist);

                // 同时创建或更新原唱的credit元素
                this.updateOriginalArtistCredit(xmlDoc, titleInfo.originalArtist);
            }

            // 添加编曲信息
            if (titleInfo.arranger) {
                const arranger = xmlDoc.createElement('creator');
                arranger.setAttribute('type', 'arranger');
                arranger.textContent = titleInfo.arranger;
                identification.appendChild(arranger);

                // 同时创建或更新编曲的credit元素
                this.updateArrangerCredit(xmlDoc, titleInfo.arranger);
            }

            // 更新版权信息
            let rights = identification.querySelector('rights');
            if (titleInfo.copyright) {
                if (!rights) {
                    rights = xmlDoc.createElement('rights');
                    identification.appendChild(rights);
                }
                // 直接使用用户输入的文本（textarea已经包含真实的换行符）
                const copyrightText = titleInfo.copyright;
                rights.textContent = copyrightText;

                // 同时更新或创建版权信息的credit元素
                console.log('正在更新版权信息credit元素:', copyrightText);
                this.updateCopyrightCredit(xmlDoc, copyrightText);
                console.log('版权信息credit元素更新完成');
            } else if (rights) {
                rights.remove();
                // 移除版权信息的credit元素
                this.removeCopyrightCredit(xmlDoc);
            }

            // 更新标题信息的credit元素（替换原有内容，不添加新的）
            console.log('🔄 正在更新标题信息credit元素...');
            this.replaceTitleCredits(xmlDoc, titleInfo);
            console.log('✅ 标题信息credit元素更新完成');

            // 序列化回XML字符串
            const serializer = new XMLSerializer();
            return serializer.serializeToString(xmlDoc);

        } catch (error) {
            console.error('更新XML标题信息失败:', error);
            return xml; // 返回原始XML
        }
    }

    updateCopyrightCredit(xmlDoc, copyrightText) {
        try {
            console.log('🔧 [版本2025-07-26-v5-极端测试] 开始更新版权credit元素，输入文本:', copyrightText);

            // 移除现有的版权credit元素
            this.removeCopyrightCredit(xmlDoc);

            if (!copyrightText || copyrightText.trim() === '') {
                console.log('版权文本为空，跳过处理');
                return;
            }

            // 获取页面布局信息
            const pageLayout = xmlDoc.querySelector('page-layout');
            let pageWidth = 800; // 默认值
            if (pageLayout) {
                const pageWidthElem = pageLayout.querySelector('page-width');
                if (pageWidthElem) {
                    pageWidth = parseInt(pageWidthElem.textContent) || 800;
                }
            }

            const centerX = Math.floor(pageWidth / 2);

            // 创建单个credit元素包含所有版权信息（使用换行符分隔多行）
            const partList = xmlDoc.querySelector('part-list');

            if (partList && copyrightText.trim() !== '') {
                // 使用新的版权信息样式设置
                const yPos = 80; // 新的Y坐标
                console.log(`准备创建单个版权信息credit元素，Y坐标:${yPos}, 页面宽度:${pageWidth}, 中心X:${centerX}`);

                const credit = xmlDoc.createElement('credit');
                credit.setAttribute('page', '1');

                const creditWords = xmlDoc.createElement('credit-words');
                creditWords.setAttribute('default-x', centerX.toString());
                creditWords.setAttribute('default-y', yPos.toString());
                creditWords.setAttribute('justify', 'center');
                creditWords.setAttribute('halign', 'center');
                creditWords.setAttribute('valign', 'bottom');
                creditWords.setAttribute('font-family', 'serif');
                creditWords.setAttribute('font-size', '8');
                creditWords.setAttribute('data-copyright', 'true'); // 标记为版权信息

                // 直接使用完整的版权文本（包含换行符）
                creditWords.textContent = copyrightText.trim();

                credit.appendChild(creditWords);

                // 插入到part-list之前
                partList.parentNode.insertBefore(credit, partList);
                console.log(`创建单个版权信息credit元素: "${copyrightText.trim()}" (x:${centerX}, y:${yPos}, font-size:8, justify:center, valign:bottom)`);
            }
        } catch (error) {
            console.error('更新版权credit失败:', error);
        }
    }

    removeCopyrightCredit(xmlDoc) {
        try {
            // 查找并移除所有标记为版权信息的credit元素
            const copyrightCredits = xmlDoc.querySelectorAll('credit-words[data-copyright="true"]');
            console.log(`找到${copyrightCredits.length}个现有的版权credit元素，准备移除`);
            copyrightCredits.forEach(creditWords => {
                const credit = creditWords.parentNode;
                if (credit && credit.tagName === 'credit') {
                    credit.parentNode.removeChild(credit);
                    console.log('移除版权credit元素:', creditWords.textContent);
                }
            });
        } catch (error) {
            console.error('移除版权credit失败:', error);
        }
    }

    updateOriginalArtistCredit(xmlDoc, originalArtistText) {
        try {
            console.log('🔧 更新原唱credit元素:', originalArtistText);

            // 移除现有的原唱credit元素
            const existingCredits = xmlDoc.querySelectorAll('credit-words[data-original-artist="true"]');
            existingCredits.forEach(creditWords => {
                const credit = creditWords.parentNode;
                if (credit && credit.tagName === 'credit') {
                    credit.parentNode.removeChild(credit);
                }
            });

            if (originalArtistText && originalArtistText.trim()) {
                // 创建新的原唱credit元素
                const credit = xmlDoc.createElement('credit');
                credit.setAttribute('page', '1');

                const creditWords = xmlDoc.createElement('credit-words');
                creditWords.setAttribute('default-x', '845');
                creditWords.setAttribute('default-y', '1540');
                creditWords.setAttribute('font-family', 'serif');
                creditWords.setAttribute('font-size', '9');
                creditWords.setAttribute('justify', 'right');
                creditWords.setAttribute('halign', 'right');
                creditWords.setAttribute('data-original-artist', 'true');
                creditWords.textContent = originalArtistText.trim();

                credit.appendChild(creditWords);

                // 插入到part-list之前
                const partList = xmlDoc.querySelector('part-list');
                if (partList) {
                    partList.parentNode.insertBefore(credit, partList);
                }

                console.log('✅ 原唱credit元素创建完成');
            }
        } catch (error) {
            console.error('更新原唱credit失败:', error);
        }
    }

    updateArrangerCredit(xmlDoc, arrangerText) {
        try {
            console.log('🔧 更新编曲credit元素:', arrangerText);

            // 移除现有的编曲credit元素
            const existingCredits = xmlDoc.querySelectorAll('credit-words[data-arranger="true"]');
            existingCredits.forEach(creditWords => {
                const credit = creditWords.parentNode;
                if (credit && credit.tagName === 'credit') {
                    credit.parentNode.removeChild(credit);
                }
            });

            if (arrangerText && arrangerText.trim()) {
                // 创建新的编曲credit元素
                const credit = xmlDoc.createElement('credit');
                credit.setAttribute('page', '1');

                const creditWords = xmlDoc.createElement('credit-words');
                creditWords.setAttribute('default-x', '845');
                creditWords.setAttribute('default-y', '1500');
                creditWords.setAttribute('font-family', 'serif');
                creditWords.setAttribute('font-size', '9');
                creditWords.setAttribute('justify', 'right');
                creditWords.setAttribute('halign', 'right');
                creditWords.setAttribute('data-arranger', 'true');
                creditWords.textContent = arrangerText.trim();

                credit.appendChild(creditWords);

                // 插入到part-list之前
                const partList = xmlDoc.querySelector('part-list');
                if (partList) {
                    partList.parentNode.insertBefore(credit, partList);
                }

                console.log('✅ 编曲credit元素创建完成');
            }
        } catch (error) {
            console.error('更新编曲credit失败:', error);
        }
    }

    replaceTitleCredits(xmlDoc, titleInfo) {
        try {
            console.log('🔧 开始替换标题信息credit元素:', {
                mainTitle: titleInfo.mainTitle,
                subTitle: titleInfo.subTitle,
                composer: titleInfo.composer,
                originalArtist: titleInfo.originalArtist,
                arranger: titleInfo.arranger,
                copyright: titleInfo.copyright
            });

            // 获取所有现有的credit元素
            const credits = xmlDoc.querySelectorAll('credit');
            console.log(`📋 找到${credits.length}个现有的credit元素`);

            // 输出所有credit元素的详细信息用于调试
            credits.forEach((credit, index) => {
                const creditWords = credit.querySelector('credit-words');
                if (creditWords) {
                    const text = creditWords.textContent.trim();
                    const x = creditWords.getAttribute('default-x');
                    const y = creditWords.getAttribute('default-y');
                    const allAttrs = {};
                    for (let i = 0; i < creditWords.attributes.length; i++) {
                        const attr = creditWords.attributes[i];
                        allAttrs[attr.name] = attr.value;
                    }
                    console.log(`📄 XML中的Credit ${index}: "${text}"`, allAttrs);
                }
            });

            // 遍历每个credit元素，根据位置和内容判断其类型并替换
            credits.forEach((credit, index) => {
                const creditWords = credit.querySelector('credit-words');
                if (!creditWords) return;

                const text = creditWords.textContent.trim();
                const x = parseInt(creditWords.getAttribute('default-x') || '0');
                const y = parseInt(creditWords.getAttribute('default-y') || '0');
                const fontSize = parseInt(creditWords.getAttribute('font-size') || '12');
                const halign = creditWords.getAttribute('halign') || '';
                const justify = creditWords.getAttribute('justify') || '';
                const dataOriginalArtist = creditWords.getAttribute('data-original-artist') === 'true';
                const dataArranger = creditWords.getAttribute('data-arranger') === 'true';

                console.log(`🔍 Credit ${index}: "${text}" (x:${x}, y:${y}, size:${fontSize}, halign:${halign}, justify:${justify}, data-original-artist:${dataOriginalArtist}, data-arranger:${dataArranger})`);

                // 输出所有属性以便调试
                const allAttributes = {};
                for (let i = 0; i < creditWords.attributes.length; i++) {
                    const attr = creditWords.attributes[i];
                    allAttributes[attr.name] = attr.value;
                }
                console.log(`🔍 Credit ${index} 所有属性:`, allAttributes);

                // 根据位置和字体大小判断credit类型并替换内容
                if (this.isMainTitleCredit(creditWords, text, x, y, fontSize)) {
                    if (titleInfo.mainTitle && titleInfo.mainTitle.trim()) {
                        console.log(`✅ 替换主标题: "${text}" → "${titleInfo.mainTitle}"`);
                        creditWords.textContent = titleInfo.mainTitle;
                    }
                } else if (this.isSubTitleCredit(creditWords, text, x, y, fontSize)) {
                    if (titleInfo.subTitle && titleInfo.subTitle.trim()) {
                        console.log(`✅ 替换副标题: "${text}" → "${titleInfo.subTitle}"`);
                        creditWords.textContent = titleInfo.subTitle;
                    }
                } else if (this.isComposerCredit(creditWords, text, x, y, fontSize)) {
                    if (titleInfo.composer && titleInfo.composer.trim()) {
                        console.log(`✅ 替换作曲: "${text}" → "${titleInfo.composer}"`);
                        creditWords.textContent = titleInfo.composer;
                    }
                } else if (this.isOriginalArtistCredit(creditWords, text, x, y, fontSize)) {
                    // 处理原唱信息（独立的credit）
                    console.log(`🔥 识别到原唱credit: "${text}" (x:${x}, y:${y}, size:${fontSize})`);
                    console.log(`🔥 原唱识别条件检查:`, {
                        hasKeyword: text.includes('原唱') || text.includes('演唱') || text.includes('歌手'),
                        hasDataAttribute: dataOriginalArtist,
                        positionMatch: (x == 845 && y == 1540),
                        rangeMatch: (halign === 'right' && y >= 1530 && y <= 1550),
                        newOriginalArtist: titleInfo.originalArtist
                    });
                    if (titleInfo.originalArtist && titleInfo.originalArtist.trim()) {
                        console.log(`✅ 替换原唱信息: "${text}" → "${titleInfo.originalArtist}"`);
                        creditWords.textContent = titleInfo.originalArtist;
                    } else {
                        console.log(`❌ 没有新的原唱信息可替换，保持原文: "${text}"`);
                    }
                } else if (this.isArrangerCredit(creditWords, text, x, y, fontSize)) {
                    // 处理编曲信息（独立的credit）
                    console.log(`🔥 识别到编曲credit: "${text}" (x:${x}, y:${y}, size:${fontSize})`);
                    console.log(`🔥 编曲识别条件检查:`, {
                        hasKeyword: text.includes('编曲'),
                        hasDataAttribute: dataArranger,
                        positionMatch: (x == 845 && y == 1500),
                        rangeMatch: (halign === 'right' && y >= 1490 && y <= 1510),
                        newArranger: titleInfo.arranger
                    });
                    if (titleInfo.arranger && titleInfo.arranger.trim()) {
                        console.log(`✅ 替换编曲信息: "${text}" → "${titleInfo.arranger}"`);
                        creditWords.textContent = titleInfo.arranger;
                    } else {
                        console.log(`❌ 没有新的编曲信息可替换，保持原文: "${text}"`);
                    }
                } else {
                    console.log(`❓ 未识别的credit类型: "${text}" (x:${x}, y:${y}, size:${fontSize})`);
                }
            });

            console.log('标题信息credit元素替换完成');

        } catch (error) {
            console.error('替换标题credit失败:', error);
        }
    }

    // 判断是否为主标题credit（通常字体最大，位置居中靠上）
    isMainTitleCredit(creditWords, text, x, y, fontSize) {
        const halign = creditWords.getAttribute('halign') || '';
        // 主标题特征：字体大(>=14)，居中对齐，位置较高(y>=1400)
        return fontSize >= 14 && (halign === 'center' || x > 600) && y >= 1400;
    }

    // 判断是否为副标题credit（字体中等，位置居中，在主标题下方）
    isSubTitleCredit(creditWords, text, x, y, fontSize) {
        const halign = creditWords.getAttribute('halign') || '';
        // 副标题特征：字体中等(10-16)，居中对齐，位置中等(y在1000-1500之间)
        // 或者包含副标题的特征字符（·、—、－等）
        const hasSubtitleChars = text.includes('·') || text.includes('—') || text.includes('－') || text.includes('太阳的后裔');
        return (fontSize >= 10 && fontSize <= 16 && (halign === 'center' || x > 500) && y >= 1000 && y < 1500) || hasSubtitleChars;
    }

    // 判断是否为作曲credit（包含"作曲"关键词或位置在右上角）
    isComposerCredit(creditWords, text, x, y, fontSize) {
        const halign = creditWords.getAttribute('halign') || '';
        // 作曲特征：包含"作曲"关键词，或者右对齐且位置较高
        return text.includes('作曲') || (halign === 'right' && y >= 1300) || (x > 1200 && y >= 1300);
    }

    // 判断是否为原唱credit（包含"原唱"、"演唱"、"歌手"关键词）
    isOriginalArtistCredit(creditWords, text, x, y, fontSize) {
        const halign = creditWords.getAttribute('halign') || '';
        const justify = creditWords.getAttribute('justify') || '';
        const dataOriginalArtist = creditWords.getAttribute('data-original-artist') === 'true';

        const hasKeyword = text.includes('原唱') || text.includes('演唱') || text.includes('歌手');
        const hasDataAttribute = dataOriginalArtist;
        const positionMatch = (x == 845 && y == 1540);
        const rangeMatch = ((halign === 'right' || justify === 'right') && y >= 1530 && y <= 1550);

        const result = hasKeyword || hasDataAttribute || positionMatch || rangeMatch;

        console.log(`🔍 isOriginalArtistCredit检查: "${text}" (x:${x}, y:${y})`, {
            hasKeyword, hasDataAttribute, positionMatch, rangeMatch, result
        });

        return result;
    }

    // 判断是否为编曲credit（包含"编曲"关键词）
    isArrangerCredit(creditWords, text, x, y, fontSize) {
        const halign = creditWords.getAttribute('halign') || '';
        const justify = creditWords.getAttribute('justify') || '';
        const dataArranger = creditWords.getAttribute('data-arranger') === 'true';

        const hasKeyword = text.includes('编曲');
        const hasDataAttribute = dataArranger;
        const positionMatch = (x == 845 && y == 1500);
        const rangeMatch = ((halign === 'right' || justify === 'right') && y >= 1490 && y <= 1510);

        const result = hasKeyword || hasDataAttribute || positionMatch || rangeMatch;

        console.log(`🔍 isArrangerCredit检查: "${text}" (x:${x}, y:${y})`, {
            hasKeyword, hasDataAttribute, positionMatch, rangeMatch, result
        });

        return result;
    }

    // 构建创作者信息文本（合并原唱和编曲）
    buildCreatorText(titleInfo, originalText) {
        const parts = [];

        console.log('🔥 构建创作者信息:', {
            originalArtist: titleInfo.originalArtist,
            arranger: titleInfo.arranger,
            originalText: originalText
        });

        // 添加原唱信息
        if (titleInfo.originalArtist && titleInfo.originalArtist.trim()) {
            parts.push(titleInfo.originalArtist.trim());
            console.log('🔥 添加原唱信息:', titleInfo.originalArtist.trim());
        }

        // 添加编曲信息
        if (titleInfo.arranger && titleInfo.arranger.trim()) {
            parts.push(titleInfo.arranger.trim());
            console.log('🔥 添加编曲信息:', titleInfo.arranger.trim());
        }

        // 如果都没有，保持原文
        if (parts.length === 0) {
            console.log('🔥 没有新的创作者信息，保持原文:', originalText);
            return originalText;
        }

        // 合并信息，用换行分隔
        const result = parts.join('\n');
        console.log('🔥 合并后的创作者信息:', result);
        return result;
    }

    createIdentificationElement(xmlDoc) {
        const identification = xmlDoc.createElement('identification');
        const scorePartwise = xmlDoc.querySelector('score-partwise');
        if (scorePartwise) {
            // 插入到适当位置
            const movementTitle = xmlDoc.querySelector('movement-title');
            if (movementTitle && movementTitle.nextSibling) {
                scorePartwise.insertBefore(identification, movementTitle.nextSibling);
            } else {
                scorePartwise.insertBefore(identification, scorePartwise.firstChild);
            }
        }
        return identification;
    }

    editNote(noteId) {
        console.log(`🎵 尝试编辑音符，ID: ${noteId}`);
        const note = this.musicData.notesList.find(n => n.id === noteId);
        if (!note) {
            console.error(`❌ 未找到ID为 ${noteId} 的音符`);
            console.log('当前notesList中的所有音符ID:');
            this.musicData.notesList.forEach((n, index) => {
                console.log(`  ${index}: ID=${n.id}, ${n.pitch}${n.octave}`);
            });
            return;
        }
        console.log(`✅ 找到音符: ${note.pitch}${note.octave}`);

        // 创建编辑对话框
        const modal = this.createNoteEditModal(note);
        document.body.appendChild(modal);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // 清理模态框
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    createNoteEditModal(note) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">编辑音符</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="container-fluid">
                            <div class="mb-3">
                                <label class="form-label">音符类型</label>
                                <select class="form-select" id="note-type">
                                    <option value="note" ${!note.isRest ? 'selected' : ''}>音符</option>
                                    <option value="rest" ${note.isRest ? 'selected' : ''}>休止符</option>
                                </select>
                                </div>
                            <div class="mb-3" id="pitch-section" ${note.isRest ? 'style="display:none"' : ''}>
                                <label class="form-label">音高</label>
                                <div class="row">
                                    <div class="col-12">
                                        <select class="form-select" id="note-pitch">
                                            <option value="C" ${note.pitch === 'C' ? 'selected' : ''}>C</option>
                                            <option value="C#" ${note.pitch === 'C#' ? 'selected' : ''}>C#</option>
                                            <option value="Db" ${note.pitch === 'Db' ? 'selected' : ''}>Db</option>
                                            <option value="D" ${note.pitch === 'D' ? 'selected' : ''}>D</option>
                                            <option value="D#" ${note.pitch === 'D#' ? 'selected' : ''}>D#</option>
                                            <option value="Eb" ${note.pitch === 'Eb' ? 'selected' : ''}>Eb</option>
                                            <option value="E" ${note.pitch === 'E' ? 'selected' : ''}>E</option>
                                            <option value="F" ${note.pitch === 'F' ? 'selected' : ''}>F</option>
                                            <option value="F#" ${note.pitch === 'F#' ? 'selected' : ''}>F#</option>
                                            <option value="Gb" ${note.pitch === 'Gb' ? 'selected' : ''}>Gb</option>
                                            <option value="G" ${note.pitch === 'G' ? 'selected' : ''}>G</option>
                                            <option value="G#" ${note.pitch === 'G#' ? 'selected' : ''}>G#</option>
                                            <option value="Ab" ${note.pitch === 'Ab' ? 'selected' : ''}>Ab</option>
                                            <option value="A" ${note.pitch === 'A' ? 'selected' : ''}>A</option>
                                            <option value="A#" ${note.pitch === 'A#' ? 'selected' : ''}>A#</option>
                                            <option value="Bb" ${note.pitch === 'Bb' ? 'selected' : ''}>Bb</option>
                                            <option value="B" ${note.pitch === 'B' ? 'selected' : ''}>B</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3" id="octave-section" ${note.isRest ? 'style="display:none"' : ''}>
                                <label class="form-label">八度</label>
                                <select class="form-select" id="note-octave">
                                    <option value="0" ${note.octave === 0 ? 'selected' : ''}>0 (极低音)</option>
                                    <option value="1" ${note.octave === 1 ? 'selected' : ''}>1 (极低音)</option>
                                    <option value="2" ${note.octave === 2 ? 'selected' : ''}>2 (低音)</option>
                                    <option value="3" ${note.octave === 3 ? 'selected' : ''}>3 (低音)</option>
                                    <option value="4" ${note.octave === 4 ? 'selected' : ''}>4 (中音)</option>
                                    <option value="5" ${note.octave === 5 ? 'selected' : ''}>5 (高音)</option>
                                    <option value="6" ${note.octave === 6 ? 'selected' : ''}>6 (高音)</option>
                                    <option value="7" ${note.octave === 7 ? 'selected' : ''}>7 (超高音)</option>
                                    <option value="8" ${note.octave === 8 ? 'selected' : ''}>8 (极高音)</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">时值</label>
                                <select class="form-select" id="note-duration">
                                    <option value="1" ${note.duration === 1 ? 'selected' : ''}>全音符</option>
                                    <option value="2" ${note.duration === 2 ? 'selected' : ''}>二分音符</option>
                                    <option value="4" ${note.duration === 4 ? 'selected' : ''}>四分音符</option>
                                    <option value="8" ${note.duration === 8 ? 'selected' : ''}>八分音符</option>
                                    <option value="16" ${note.duration === 16 ? 'selected' : ''}>十六分音符</option>
                                    <option value="32" ${note.duration === 32 ? 'selected' : ''}>三十二分音符</option>
                                    <option value="64" ${note.duration === 64 ? 'selected' : ''}>六十四分音符</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">附点</label>
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="checkbox" id="note-dotted" ${note.dotted ? 'checked' : ''}>
                                    <label class="form-check-label" for="note-dotted">
                                        ${note.isRest ? '附点休止符' : '附点音符'} (增加一半时值)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="visualEditor.cancelNoteEdit(${note.id})">取消</button>
                        <button type="button" class="btn btn-primary" onclick="visualEditor.saveNoteEdit(${note.id})">保存</button>
                    </div>
                </div>
            </div>
        `;

        // 添加音符类型切换事件
        const noteTypeSelect = modal.querySelector('#note-type');
        const pitchSection = modal.querySelector('#pitch-section');
        const octaveSection = modal.querySelector('#octave-section');
        const dottedLabel = modal.querySelector('label[for="note-dotted"]');

        noteTypeSelect.addEventListener('change', (e) => {
            if (e.target.value === 'rest') {
                pitchSection.style.display = 'none';
                octaveSection.style.display = 'none';
                // 更新附点标签为休止符模式
                if (dottedLabel) {
                    dottedLabel.innerHTML = '附点休止符 (增加一半时值)';
                }
            } else {
                pitchSection.style.display = 'block';
                octaveSection.style.display = 'block';
                // 更新附点标签为音符模式
                if (dottedLabel) {
                    dottedLabel.innerHTML = '附点音符 (增加一半时值)';
                }
            }
        });

        return modal;
    }

    async saveNoteEdit(noteId) {
        try {
            // 获取编辑后的值
            const noteType = document.getElementById('note-type').value;
            const pitch = document.getElementById('note-pitch').value;
            const octave = parseInt(document.getElementById('note-octave').value);
            const duration = parseInt(document.getElementById('note-duration').value);
            const dotted = document.getElementById('note-dotted').checked;

            console.log(`🔄 音符编辑值: type=${noteType}, pitch=${pitch}, octave=${octave}, duration=${duration}, dotted=${dotted}`);

            // 🔧 使用新的XML操作架构
            await this.executeXMLOperation('保存音符编辑', async () => {
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(this.currentXML, 'application/xml');

                // 查找要编辑的音符元素
                const noteElement = xmlDoc.querySelector(`note[data-note-id="${noteId}"]`);
                if (!noteElement) {
                    // 如果是新音符，创建新的音符元素
                    if (this.pendingSingleNoteData && this.pendingSingleNoteData.note.id === noteId) {
                        console.log('🎵 创建新音符XML元素');
                        const newNoteElement = this.createNoteXMLElement(xmlDoc, {
                            id: noteId,
                            isRest: (noteType === 'rest'),
                            pitch: pitch,
                            octave: octave,
                            duration: duration,
                            dotted: dotted,
                            measure: this.pendingSingleNoteData.note.measure,
                            staff: this.pendingSingleNoteData.note.staff,
                            voice: this.pendingSingleNoteData.note.voice,
                            defaultX: this.pendingSingleNoteData.note.defaultX  // 🔧 重要修复：添加defaultX
                        });

                        // 🔧 修复：使用insertNoteInOrder方法插入到正确位置
                        const measureElement = xmlDoc.querySelector(`measure[number="${this.pendingSingleNoteData.note.measure}"]`);
                        if (measureElement) {
                            this.insertNoteInOrder(xmlDoc, measureElement, newNoteElement, {
                                measure: this.pendingSingleNoteData.note.measure,
                                staff: this.pendingSingleNoteData.note.staff,
                                defaultX: this.pendingSingleNoteData.note.defaultX,
                                pitch: pitch,
                                octave: octave
                            });
                        }
                        this.pendingSingleNoteData = null;
                    } else {
                        throw new Error(`未找到ID为${noteId}的音符元素`);
                    }
                } else {
                    // 更新现有音符元素
                    console.log('🔄 更新现有音符XML元素');
                    this.updateNoteXMLElement(noteElement, {
                        isRest: (noteType === 'rest'),
                        pitch: pitch,
                        octave: octave,
                        duration: duration,
                        dotted: dotted
                    });
                }

                // 更新currentXML
                const serializer = new XMLSerializer();
                this.currentXML = serializer.serializeToString(xmlDoc);

                console.log(`✅ 音符${noteId}编辑完成`);
                return true;
            });

            // 关闭模态框
            const modal = document.querySelector('.modal.show');
            if (modal) {
                bootstrap.Modal.getInstance(modal).hide();
            }

            this.showMessage('音符已更新，请点击保存按钮应用修改', 'info');

        } catch (error) {
            console.error('❌ 保存音符编辑失败:', error);
            this.showMessage(`保存音符编辑失败: ${error.message}`, 'error');
        }
    }

    cancelNoteEdit(noteId) {
        // 如果这是新添加的单音符，需要回滚
        if (this.pendingSingleNoteData && this.pendingSingleNoteData.note.id === noteId) {
            console.log('🔄 取消新音符编辑，回滚音符列表');

            // 从notesList中移除新添加的音符
            this.musicData.notesList = this.musicData.notesList.filter(n => n.id !== noteId);

            // 刷新显示
            this.loadNotesList();

            // 清除待处理数据
            this.pendingSingleNoteData = null;

            console.log('✅ 音符添加已取消');
        } else {
            console.log('🔄 取消现有音符编辑，无需回滚');
        }

        // 关闭模态框
        const modal = document.querySelector('.modal.show');
        if (modal) {
            bootstrap.Modal.getInstance(modal).hide();
        }
    }

    async deleteNote(noteId) {
        if (!confirm('确定要删除这个音符吗？')) return;

        try {
            // 🔧 使用新的XML操作架构
            await this.executeXMLOperation('删除音符', async () => {
                // 直接从XML中删除音符元素
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(this.currentXML, 'application/xml');

                // 查找要删除的音符元素
                const noteElement = xmlDoc.querySelector(`note[data-note-id="${noteId}"]`);
                if (!noteElement) {
                    throw new Error(`未找到ID为${noteId}的音符元素`);
                }

                // 从XML中移除音符元素
                noteElement.parentNode.removeChild(noteElement);

                // 更新currentXML
                const serializer = new XMLSerializer();
                this.currentXML = serializer.serializeToString(xmlDoc);

                console.log(`✅ 音符${noteId}已从XML中删除`);
                return true;
            });

            this.showMessage('音符已删除，请点击保存按钮应用修改', 'info');

        } catch (error) {
            console.error('❌ 删除音符失败:', error);
            this.showMessage(`删除音符失败: ${error.message}`, 'error');
        }
    }

    async moveNoteUp(noteId) {
        try {
            // 🔧 使用新的XML操作架构
            await this.executeXMLOperation('音符上移', async () => {
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(this.currentXML, 'application/xml');

                // 查找要移动的音符元素
                const noteElement = xmlDoc.querySelector(`note[data-note-id="${noteId}"]`);
                if (!noteElement) {
                    throw new Error(`未找到ID为${noteId}的音符元素`);
                }

                // 检查是否是和弦中的音符
                if (noteElement.querySelector('chord')) {
                    throw new Error('不能单独移动和弦中的音符，请移动整个和弦');
                }

                // 获取当前音符的measure
                const measureElement = noteElement.closest('measure');
                if (!measureElement) {
                    throw new Error('无法找到音符所在的小节');
                }

                // 获取同一小节中的所有音符元素
                const allNotesInMeasure = Array.from(measureElement.querySelectorAll('note'));
                const currentIndex = allNotesInMeasure.indexOf(noteElement);

                if (currentIndex <= 0) {
                    throw new Error('音符已经是小节中的第一个');
                }

                // 找到前一个音符（或和弦的第一个音符）
                let previousNoteElement = allNotesInMeasure[currentIndex - 1];

                // 如果前一个音符是和弦的一部分，找到和弦的主音符
                if (previousNoteElement.querySelector('chord')) {
                    // 向前查找和弦的主音符（没有chord元素的音符）
                    for (let i = currentIndex - 1; i >= 0; i--) {
                        if (!allNotesInMeasure[i].querySelector('chord')) {
                            previousNoteElement = allNotesInMeasure[i];
                            break;
                        }
                    }
                }

                // 在XML中交换位置：将当前音符插入到前一个音符之前
                measureElement.insertBefore(noteElement, previousNoteElement);

                // 更新currentXML
                const serializer = new XMLSerializer();
                this.currentXML = serializer.serializeToString(xmlDoc);

                console.log(`✅ 音符${noteId}已上移`);
                return true;
            });

            this.showMessage('音符已上移', 'info');

        } catch (error) {
            console.error('❌ 音符上移失败:', error);
            this.showMessage(error.message, 'warning');
        }
    }

    async moveNoteDown(noteId) {
        try {
            // 🔧 使用新的XML操作架构
            await this.executeXMLOperation('音符下移', async () => {
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(this.currentXML, 'application/xml');

                // 查找要移动的音符元素
                const noteElement = xmlDoc.querySelector(`note[data-note-id="${noteId}"]`);
                if (!noteElement) {
                    throw new Error(`未找到ID为${noteId}的音符元素`);
                }

                // 检查是否是和弦中的音符
                if (noteElement.querySelector('chord')) {
                    throw new Error('不能单独移动和弦中的音符，请移动整个和弦');
                }

                // 获取当前音符的measure
                const measureElement = noteElement.closest('measure');
                if (!measureElement) {
                    throw new Error('无法找到音符所在的小节');
                }

                // 获取同一小节中的所有音符元素
                const allNotesInMeasure = Array.from(measureElement.querySelectorAll('note'));
                const currentIndex = allNotesInMeasure.indexOf(noteElement);

                if (currentIndex >= allNotesInMeasure.length - 1) {
                    throw new Error('音符已经是小节中的最后一个');
                }

                // 找到下一个音符（或和弦的最后一个音符）
                let nextNoteElement = allNotesInMeasure[currentIndex + 1];

                // 如果下一个音符是和弦的主音符，找到和弦的最后一个音符
                if (!nextNoteElement.querySelector('chord')) {
                    // 查找这个和弦的所有音符
                    for (let i = currentIndex + 2; i < allNotesInMeasure.length; i++) {
                        if (!allNotesInMeasure[i].querySelector('chord')) {
                            // 找到下一个非和弦音符，前一个就是和弦的最后一个音符
                            nextNoteElement = allNotesInMeasure[i - 1];
                            break;
                        }
                        if (i === allNotesInMeasure.length - 1) {
                            // 如果到了最后，当前就是和弦的最后一个音符
                            nextNoteElement = allNotesInMeasure[i];
                            break;
                        }
                    }
                }

                // 在XML中交换位置：将当前音符插入到下一个音符之后
                if (nextNoteElement.nextSibling) {
                    measureElement.insertBefore(noteElement, nextNoteElement.nextSibling);
                } else {
                    measureElement.appendChild(noteElement);
                }

                // 更新currentXML
                const serializer = new XMLSerializer();
                this.currentXML = serializer.serializeToString(xmlDoc);

                console.log(`✅ 音符${noteId}已下移`);
                return true;
            });

            this.showMessage('音符已下移', 'info');

        } catch (error) {
            console.error('❌ 音符下移失败:', error);
            this.showMessage(error.message, 'warning');
        }
    }

    // 获取小节内的音乐单位（单音符或完整和弦）
    getMusicUnitsInMeasureStaff(measureNum, staffNum) {
        const notesInPosition = this.musicData.notesList.filter(n =>
            n.measure === measureNum && n.staff === staffNum
        ).sort((a, b) => {
            // 按在notesList中的顺序排序（代表时间顺序）
            return this.musicData.notesList.indexOf(a) - this.musicData.notesList.indexOf(b);
        });

        const musicUnits = [];
        let i = 0;

        while (i < notesInPosition.length) {
            const currentNote = notesInPosition[i];

            if (currentNote.isChord) {
                // 跳过和弦音符，它们应该被主音符处理
                i++;
                continue;
            }

            // 检查是否是和弦的主音符（后面跟着和弦音符）
            const chordNotes = [currentNote];
            let j = i + 1;

            while (j < notesInPosition.length && notesInPosition[j].isChord) {
                chordNotes.push(notesInPosition[j]);
                j++;
            }

            if (chordNotes.length > 1) {
                // 这是一个和弦
                musicUnits.push({
                    type: 'chord',
                    notes: chordNotes,
                    mainNoteId: currentNote.id,
                    position: musicUnits.length
                });
            } else {
                // 这是一个单音符
                musicUnits.push({
                    type: 'single',
                    notes: [currentNote],
                    mainNoteId: currentNote.id,
                    position: musicUnits.length
                });
            }

            i = j;
        }

        return musicUnits;
    }

    // 交换两个音乐单位（单音符或完整和弦）的位置
    swapMusicUnits(unit1, unit2) {
        console.log(`🔄 交换音乐单位: ${unit1.type}(${unit1.notes.length}音符) ↔ ${unit2.type}(${unit2.notes.length}音符)`);
        console.log('Unit1音符:', unit1.notes.map(n => `${n.pitch}${n.octave}`));
        console.log('Unit2音符:', unit2.notes.map(n => `${n.pitch}${n.octave}`));

        // 获取两个单位在notesList中的所有音符
        const unit1Notes = unit1.notes;
        const unit2Notes = unit2.notes;

        // 获取它们在notesList中的索引范围
        const unit1Indices = unit1Notes.map(note => this.musicData.notesList.findIndex(n => n.id === note.id));
        const unit2Indices = unit2Notes.map(note => this.musicData.notesList.findIndex(n => n.id === note.id));

        // 确保索引有效
        if (unit1Indices.some(i => i === -1) || unit2Indices.some(i => i === -1)) {
            console.error('找不到音符索引');
            return;
        }

        // 创建新的notesList副本
        const newNotesList = [...this.musicData.notesList];

        // 移除两个单位的所有音符（从后往前移除避免索引变化）
        const allIndices = [...unit1Indices, ...unit2Indices].sort((a, b) => b - a);
        const removedNotes = [];

        allIndices.forEach(index => {
            removedNotes.unshift(newNotesList.splice(index, 1)[0]);
        });

        // 确定插入位置（使用较小的原始索引）
        const insertPosition = Math.min(...unit1Indices, ...unit2Indices);

        // 确定哪个单位在前面（索引更小的在前面）
        const unit1MinIndex = Math.min(...unit1Indices);
        const unit2MinIndex = Math.min(...unit2Indices);

        let reorderedNotes;
        if (unit1MinIndex < unit2MinIndex) {
            // unit1原来在前面，现在要交换，所以unit2在前面
            reorderedNotes = [...unit2Notes, ...unit1Notes];
            console.log(`交换顺序: unit1(${unit1MinIndex}) 和 unit2(${unit2MinIndex}) -> unit2在前`);
        } else {
            // unit2原来在前面，现在要交换，所以unit1在前面
            reorderedNotes = [...unit1Notes, ...unit2Notes];
            console.log(`交换顺序: unit2(${unit2MinIndex}) 和 unit1(${unit1MinIndex}) -> unit1在前`);
        }

        newNotesList.splice(insertPosition, 0, ...reorderedNotes);

        // 更新notesList
        this.musicData.notesList = newNotesList;

        // 🔧 修复：更新交换后音符的位置信息
        this.updateSwappedNotesPositions(unit1, unit2);

        // 🔧 修复：重新排序整个notesList以保持一致性
        this.resortNotesListAfterSwap();

        console.log(`✅ 音乐单位交换完成`);
    }

    /**
     * 更新交换后音符的位置信息（defaultX和xmlIndex）
     * @param {Object} unit1 第一个音乐单位
     * @param {Object} unit2 第二个音乐单位
     */
    updateSwappedNotesPositions(unit1, unit2) {
        console.log(`🔧 更新交换后音符的位置信息`);

        // 获取交换前的位置信息
        const unit1OriginalX = unit1.notes[0].defaultX;
        const unit2OriginalX = unit2.notes[0].defaultX;
        const unit1OriginalIndex = Math.min(...unit1.notes.map(n => n.xmlIndex || 0));
        const unit2OriginalIndex = Math.min(...unit2.notes.map(n => n.xmlIndex || 0));

        console.log(`交换前位置: unit1(defaultX=${unit1OriginalX}, xmlIndex=${unit1OriginalIndex}) ↔ unit2(defaultX=${unit2OriginalX}, xmlIndex=${unit2OriginalIndex})`);

        // 交换defaultX位置
        unit1.notes.forEach(note => {
            note.defaultX = unit2OriginalX;
        });
        unit2.notes.forEach(note => {
            note.defaultX = unit1OriginalX;
        });

        // 交换xmlIndex（保持相对顺序）
        const indexDiff = unit2OriginalIndex - unit1OriginalIndex;
        unit1.notes.forEach((note, i) => {
            note.xmlIndex = unit2OriginalIndex + i;
        });
        unit2.notes.forEach((note, i) => {
            note.xmlIndex = unit1OriginalIndex + i;
        });

        console.log(`交换后位置: unit1音符defaultX=${unit2OriginalX}, unit2音符defaultX=${unit1OriginalX}`);
    }

    /**
     * 在位置交换后重新排序整个notesList
     */
    resortNotesListAfterSwap() {
        console.log(`🔧 重新排序notesList以保持一致性`);

        // 使用与loadNotesList相同的排序逻辑
        this.musicData.notesList.sort((a, b) => {
            // 首先按小节排序
            if (a.measure !== b.measure) {
                return a.measure - b.measure;
            }
            // 然后按谱表排序
            if (a.staff !== b.staff) {
                return a.staff - b.staff;
            }
            // 然后按default-x位置排序（时间顺序）
            if (a.defaultX !== b.defaultX) {
                return a.defaultX - b.defaultX;
            }
            // 然后按声部排序
            if (a.voice !== b.voice) {
                return a.voice - b.voice;
            }
            // 最后按XML索引排序
            return (a.xmlIndex || 0) - (b.xmlIndex || 0);
        });

        console.log(`✅ notesList重新排序完成`);
    }

    updateNoteOrderInXML(measureNum, staffNum) {
        try {
            console.log(`🔄 更新第${measureNum}小节谱表${staffNum}的音符顺序`);

            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(this.currentXML, 'application/xml');

            // 找到指定小节
            const parts = xmlDoc.querySelectorAll('part');
            if (parts.length === 0) return;

            const part = parts[0];
            const measures = part.querySelectorAll('measure');
            const measureIndex = measureNum - 1;

            if (measureIndex < 0 || measureIndex >= measures.length) return;

            const measure = measures[measureIndex];

            // 获取该小节该谱表的所有编辑器音符
            const editorNotes = measure.querySelectorAll(`note[data-added-by-editor="true"]`);
            const notesToReorder = [];

            editorNotes.forEach(noteElement => {
                const staffElement = noteElement.querySelector('staff');
                const noteStaff = staffElement ? parseInt(staffElement.textContent) : 1;

                if (noteStaff === staffNum) {
                    const noteId = noteElement.getAttribute('data-note-id');
                    if (noteId) {
                        notesToReorder.push({
                            element: noteElement,
                            noteId: parseInt(noteId)
                        });
                    }
                }
            });

            // 按notesList中的顺序重新排序
            const orderedNotes = this.musicData.notesList
                .filter(n => n.measure === measureNum && n.staff === staffNum)
                .map(noteData => {
                    const found = notesToReorder.find(item => item.noteId === noteData.id);
                    return found ? found.element : null;
                })
                .filter(element => element !== null);

            // 移除原有的音符元素
            notesToReorder.forEach(item => {
                if (item.element.parentNode) {
                    item.element.parentNode.removeChild(item.element);
                }
            });

            // 按新顺序重新插入
            orderedNotes.forEach(noteElement => {
                measure.appendChild(noteElement); // 简化插入逻辑
            });

            // 更新XML
            const serializer = new XMLSerializer();
            const newXML = serializer.serializeToString(xmlDoc);

            if (this.validateXMLFormat(newXML)) {
                this.currentXML = newXML;
                console.log(`✅ 第${measureNum}小节谱表${staffNum}的音符顺序已更新`);
            } else {
                console.error('❌ 更新音符顺序后XML格式无效');
            }

        } catch (error) {
            console.error('更新音符顺序失败:', error);
        }
    }

    moveChordUp(chordId, position) {
        // 找到和弦的主音符（第一个音符）
        const chordNotes = this.findChordNotes(chordId, position);
        if (!chordNotes || chordNotes.length === 0) {
            this.showMessage('找不到和弦音符', 'warning');
            return;
        }

        const mainNote = chordNotes[0]; // 和弦的主音符

        // 使用统一的移动逻辑（和单音符移动相同）
        this.moveNoteUp(mainNote.id);
    }

    moveChordDown(chordId, position) {
        // 找到和弦的主音符（第一个音符）
        const chordNotes = this.findChordNotes(chordId, position);
        if (!chordNotes || chordNotes.length === 0) {
            this.showMessage('找不到和弦音符', 'warning');
            return;
        }

        const mainNote = chordNotes[0]; // 和弦的主音符

        // 使用统一的移动逻辑（和单音符移动相同）
        this.moveNoteDown(mainNote.id);
    }



    addNote() {
        // 🔧 使用新的全局ID管理器创建新音符
        const newNote = {
            id: this.generateNoteId(), // 使用全局ID管理器
            isRest: false,
            pitch: 'C',
            octave: 4,
            duration: 4,
            measure: 1,
            voice: 1,
            staff: 1,
            clef: 'treble',
            defaultX: 0, // 将在插入时计算
            xmlIndex: this.getNextXmlIndex()
        };

        // 计算正确的插入位置
        const insertPosition = this.findInsertPositionForMeasureAndStaff(1, 1);
        this.musicData.notesList.splice(insertPosition, 0, newNote);

        // 插入后更新defaultX位置
        newNote.defaultX = this.estimateDefaultX(newNote);
        console.log(`🎵 新音符defaultX设置为: ${newNote.defaultX}`);

        // 保存待处理的单音符数据
        this.pendingSingleNoteData = {
            note: newNote,
            insertPosition: insertPosition
        };

        // 立即编辑新音符（不先添加到XML）
        this.editNote(newNote.id);
    }

    addNoteToMeasure(measureNum) {
        // 创建新音符并指定小节（默认高音谱表）
        const newNote = {
            id: Date.now(),
            isRest: false,
            pitch: 'C',
            octave: 4,
            duration: 4,
            measure: parseInt(measureNum),
            voice: 1,
            staff: 1,
            clef: 'treble',
            defaultX: 0, // 将在插入时计算
            xmlIndex: this.getNextXmlIndex()
        };

        // 计算正确的插入位置
        const insertPosition = this.findInsertPositionForMeasureAndStaff(measureNum, 1);
        this.musicData.notesList.splice(insertPosition, 0, newNote);

        // 插入后更新defaultX位置
        newNote.defaultX = this.estimateDefaultX(newNote);
        console.log(`🎵 新音符defaultX设置为: ${newNote.defaultX}`);

        // 保存待处理的单音符数据
        this.pendingSingleNoteData = {
            note: newNote,
            insertPosition: insertPosition
        };

        // 立即编辑新音符（不先添加到XML）
        this.editNote(newNote.id);
    }

    addNoteToMeasureAndStaff(measureNum, staffNum, type = 'single', insertMode = null) {
        if (type === 'chord') {
            this.addChordToMeasureAndStaff(measureNum, staffNum, insertMode);
        } else {
            // 创建新音符并指定小节和谱表
            const defaultOctave = staffNum === 1 ? 5 : 3; // 高音谱表默认5，低音谱表默认3
            // 获取该谱表的合适voice
            const targetVoice = this.getAppropriateVoiceForStaff(measureNum, staffNum);

            const newNote = {
                id: Date.now(),
                isRest: false,
                pitch: 'C',
                octave: defaultOctave,
                duration: 4,
                measure: parseInt(measureNum),
                voice: targetVoice,
                staff: parseInt(staffNum),
                clef: staffNum === 1 ? 'treble' : 'bass',
                isChord: false,
                defaultX: 0, // 将在插入时计算
                xmlIndex: this.getNextXmlIndex() // 分配新的XML索引
            };

            // 根据插入模式计算插入位置
            let insertPosition;
            if (insertMode === 'end') {
                // 插入到该谱表的末尾
                insertPosition = this.findEndPositionForMeasureAndStaff(measureNum, staffNum);
                console.log(`🎵 插入到谱表末尾，位置: ${insertPosition}`);
            } else {
                // 默认行为：插入到指定位置之后
                insertPosition = this.findInsertPositionForMeasureAndStaff(measureNum, staffNum, insertMode);
                console.log(`🎵 插入到指定位置之后，位置: ${insertPosition}`);
            }

            this.musicData.notesList.splice(insertPosition, 0, newNote);

            // 插入后更新defaultX位置
            newNote.defaultX = this.estimateDefaultX(newNote);
            console.log(`🎵 新音符defaultX设置为: ${newNote.defaultX}`);

            // 保存待处理的单音符数据
            this.pendingSingleNoteData = {
                note: newNote,
                insertPosition: insertPosition
            };

            // 立即编辑新音符（不先添加到XML）
            this.editNote(newNote.id);
        }
    }

    /**
     * 计算在指定小节和谱表中插入音符的正确位置（基于defaultX位置）
     * @param {number} measureNum 小节号
     * @param {number} staffNum 谱表号
     * @param {number} afterPosition 可选，在指定位置后插入（该小节该谱表中的相对位置）
     * @returns {number} 插入位置的索引
     */
    findInsertPositionForMeasureAndStaff(measureNum, staffNum, afterPosition = null) {
        console.log(`🔍 查找插入位置: 小节${measureNum}, 谱表${staffNum}, afterPosition=${afterPosition}`);

        if (afterPosition !== null) {
            // 如果指定了位置，则在该位置后插入
            const notesInMeasureStaff = this.musicData.notesList.filter(n =>
                n.measure === measureNum && n.staff === staffNum
            );

            // 按defaultX排序，确保位置准确
            notesInMeasureStaff.sort((a, b) => a.defaultX - b.defaultX);

            if (afterPosition >= notesInMeasureStaff.length) {
                // 如果位置超出范围，插入到该小节谱表的末尾
                const lastNote = notesInMeasureStaff[notesInMeasureStaff.length - 1];
                if (lastNote) {
                    return this.musicData.notesList.findIndex(n => n.id === lastNote.id) + 1;
                }
            } else {
                // 在指定位置的音符之后插入
                const targetNote = notesInMeasureStaff[afterPosition];
                if (targetNote) {
                    return this.musicData.notesList.findIndex(n => n.id === targetNote.id) + 1;
                }
            }
        }

        // 默认行为：插入到该小节该谱表的末尾
        // 使用更可靠的方法：按小节和谱表顺序查找插入位置
        let insertPosition = 0;

        for (let i = 0; i < this.musicData.notesList.length; i++) {
            const note = this.musicData.notesList[i];

            // 如果找到更晚的小节，或者同小节但更大的谱表号，插入在这里
            if (note.measure > measureNum ||
                (note.measure === measureNum && note.staff > staffNum)) {
                insertPosition = i;
                break;
            }

            // 如果是同小节同谱表，插入在最后一个音符之后
            if (note.measure === measureNum && note.staff === staffNum) {
                insertPosition = i + 1;
            }
        }

        // 如果没有找到更晚的位置，插入到末尾
        if (insertPosition === 0 && this.musicData.notesList.length > 0) {
            insertPosition = this.musicData.notesList.length;
        }

        console.log(`🔍 计算出的插入位置: ${insertPosition}`);
        return insertPosition;
    }

    /**
     * 找到指定小节和谱表的末尾位置（用于插入到谱表末尾）
     * @param {number} measureNum 小节号
     * @param {number} staffNum 谱表号
     * @returns {number} 插入位置的索引
     */
    findEndPositionForMeasureAndStaff(measureNum, staffNum) {
        console.log(`🔍 查找小节${measureNum}谱表${staffNum}的末尾位置`);

        // 使用更可靠的方法：按小节和谱表顺序查找末尾位置
        let endPosition = this.musicData.notesList.length;

        // 从后往前查找，找到该小节该谱表的最后一个音符
        for (let i = this.musicData.notesList.length - 1; i >= 0; i--) {
            const note = this.musicData.notesList[i];

            if (note.measure === measureNum && note.staff === staffNum) {
                // 找到该小节该谱表的音符，插入在其后
                endPosition = i + 1;
                console.log(`🔍 找到该谱表最后一个音符: ${note.pitch}${note.octave}, 插入位置: ${endPosition}`);
                break;
            } else if (note.measure < measureNum ||
                      (note.measure === measureNum && note.staff < staffNum)) {
                // 找到更早的小节或更小的谱表，插入在其后
                endPosition = i + 1;
                console.log(`🔍 该谱表无音符，插入在位置: ${endPosition}`);
                break;
            }
        }

        // 如果没有找到任何音符，插入在开头
        if (endPosition === this.musicData.notesList.length && this.musicData.notesList.length > 0) {
            // 检查是否所有音符都在更晚的位置
            const firstNote = this.musicData.notesList[0];
            if (firstNote.measure > measureNum ||
                (firstNote.measure === measureNum && firstNote.staff > staffNum)) {
                endPosition = 0;
                console.log(`🔍 所有音符都在更晚位置，插入在开头: ${endPosition}`);
            }
        }

        console.log(`🔍 计算出的末尾位置: ${endPosition}`);
        return endPosition;
    }

    getAppropriateVoiceForStaff(measureNum, staffNum) {
        // 查找该小节该谱表中已有音符的voice
        const existingNotesInStaff = this.musicData.notesList.filter(n =>
            n.measure === measureNum && n.staff === staffNum
        );

        if (existingNotesInStaff.length > 0) {
            // 使用该谱表中最常用的voice
            const voiceCounts = {};
            existingNotesInStaff.forEach(note => {
                voiceCounts[note.voice] = (voiceCounts[note.voice] || 0) + 1;
            });

            // 找到使用最多的voice
            let mostUsedVoice = parseInt(Object.keys(voiceCounts)[0]);
            let maxCount = voiceCounts[mostUsedVoice];

            for (const voice in voiceCounts) {
                if (voiceCounts[voice] > maxCount) {
                    mostUsedVoice = parseInt(voice);
                    maxCount = voiceCounts[voice];
                }
            }

            console.log(`🎵 该谱表最常用的voice: ${mostUsedVoice} (使用${maxCount}次)`);
            return mostUsedVoice;
        } else {
            // 该谱表没有音符，使用默认voice
            const defaultVoice = staffNum === 1 ? 1 : 5;  // 高音谱表用voice=1，低音谱表用voice=5
            console.log(`🎵 该谱表无音符，使用默认voice: ${defaultVoice}`);
            return defaultVoice;
        }
    }

    addChordToMeasureAndStaff(measureNum, staffNum, insertMode = null) {
        // 创建和弦（默认3个音符）
        const defaultOctave = staffNum === 1 ? 5 : 3;
        const baseTime = Date.now();

        // 获取该谱表的合适voice
        const targetVoice = this.getAppropriateVoiceForStaff(measureNum, staffNum);

        const chordNotes = [
            {
                id: baseTime,
                isRest: false,
                pitch: 'C',
                octave: defaultOctave,
                duration: 4,
                measure: parseInt(measureNum),
                voice: targetVoice,
                staff: parseInt(staffNum),
                clef: staffNum === 1 ? 'treble' : 'bass',
                isChord: false,  // 主音符
                defaultX: 0, // 将在插入时计算
                xmlIndex: this.getNextXmlIndex()
            },
            {
                id: baseTime + 1,
                isRest: false,
                pitch: 'E',
                octave: defaultOctave,
                duration: 4,
                measure: parseInt(measureNum),
                voice: targetVoice,
                staff: parseInt(staffNum),
                clef: staffNum === 1 ? 'treble' : 'bass',
                isChord: true,  // 和弦音符
                defaultX: 0, // 将在插入时计算
                xmlIndex: this.getNextXmlIndex()
            },
            {
                id: baseTime + 2,
                isRest: false,
                pitch: 'G',
                octave: defaultOctave,
                duration: 4,
                measure: parseInt(measureNum),
                voice: targetVoice,
                staff: parseInt(staffNum),
                clef: staffNum === 1 ? 'treble' : 'bass',
                isChord: true,  // 和弦音符
                defaultX: 0, // 将在插入时计算
                xmlIndex: this.getNextXmlIndex()
            }
        ];

        // 根据插入模式计算插入位置
        let insertPosition;
        if (insertMode === 'end') {
            // 插入到该谱表的末尾
            insertPosition = this.findEndPositionForMeasureAndStaff(measureNum, staffNum);
            console.log(`🎵 和弦插入到谱表末尾，位置: ${insertPosition}`);
        } else {
            // 默认行为：插入到指定位置之后
            insertPosition = this.findInsertPositionForMeasureAndStaff(measureNum, staffNum, insertMode);
            console.log(`🎵 和弦插入到指定位置之后，位置: ${insertPosition}`);
        }

        // 先计算defaultX位置（在插入到notesList之前）
        const estimatedX = this.estimateDefaultX(chordNotes[0]);
        chordNotes.forEach(note => {
            note.defaultX = estimatedX;
        });
        console.log(`🎵 和弦音符defaultX设置为: ${estimatedX}`);

        // 将和弦音符添加到notesList中（用于编辑）
        chordNotes.forEach((note, index) => {
            this.musicData.notesList.splice(insertPosition + index, 0, note);
        });

        // 保存插入位置和音符信息，用于取消时回滚
        this.pendingChordData = {
            notes: chordNotes,
            insertPosition: insertPosition
        };

        // 直接显示和弦编辑模态框（不通过findChordNotes查找）
        this.showChordEditModal(chordNotes);
    }

    insertNoteAfter(position, measureNum, staffNum) {
        console.log(`🎵 在位置${position}后插入音符，小节${measureNum}，谱表${staffNum}`);

        // 获取该小节谱表的所有音符，按当前顺序排列
        const notesInMeasureStaff = this.musicData.notesList.filter(n =>
            n.measure === measureNum && n.staff === staffNum
        );

        console.log(`该小节谱表共有 ${notesInMeasureStaff.length} 个音符`);

        // 调试：显示该小节该谱表的所有音符
        console.log('该小节该谱表的音符结构:');
        notesInMeasureStaff.forEach((note, index) => {
            console.log(`  ${index}: ${note.pitch}${note.octave} (isChord: ${note.isChord}, defaultX: ${note.defaultX}, id: ${note.id})`);
        });

        // 计算实际的插入位置（在notesList数组中的索引）
        let actualInsertIndex;

        console.log(`🔍 检查位置: position=${position}, notesInMeasureStaff.length=${notesInMeasureStaff.length}`);

        if (position >= notesInMeasureStaff.length) {
            // 如果位置超出范围，插入到该小节谱表的末尾
            console.log(`⚠️ 位置超出范围，插入到谱表末尾`);
            const lastNote = notesInMeasureStaff[notesInMeasureStaff.length - 1];
            if (lastNote) {
                actualInsertIndex = this.musicData.notesList.findIndex(n => n.id === lastNote.id) + 1;
                console.log(`📍 谱表末尾插入位置: ${actualInsertIndex}`);
            } else {
                // 该小节谱表没有音符，插入到整个列表末尾
                actualInsertIndex = this.musicData.notesList.length;
                console.log(`📍 空谱表，插入到全局末尾: ${actualInsertIndex}`);
            }
        } else {
            // 在指定位置的音符/和弦之后插入
            const targetNote = notesInMeasureStaff[position];
            console.log(`目标音符: ${targetNote.pitch}${targetNote.octave}, isChord: ${targetNote.isChord}`);

            if (targetNote) {
                // 找到目标音符在主列表中的位置
                const targetNoteIndex = this.musicData.notesList.findIndex(n => n.id === targetNote.id);
                console.log(`目标音符在全局列表中的索引: ${targetNoteIndex}`);

                // 如果目标音符是和弦的一部分，需要找到整个和弦的最后一个音符
                let insertAfterIndex = targetNoteIndex;

                // 检查目标音符后面是否还有和弦音符（基于相同的defaultX位置）
                const targetDefaultX = targetNote.defaultX;
                console.log(`🔍 目标音符defaultX: ${targetDefaultX}`);

                for (let i = position + 1; i < notesInMeasureStaff.length; i++) {
                    const nextNote = notesInMeasureStaff[i];
                    console.log(`检查后续音符 ${i}: ${nextNote.pitch}${nextNote.octave} (defaultX: ${nextNote.defaultX}, isChord: ${nextNote.isChord})`);

                    // 如果下一个音符的defaultX相同，说明是同一个和弦的一部分
                    if (Math.abs(nextNote.defaultX - targetDefaultX) < 1) {
                        // 找到这个和弦音符在全局列表中的位置
                        const nextNoteGlobalIndex = this.musicData.notesList.findIndex(n => n.id === nextNote.id);
                        insertAfterIndex = nextNoteGlobalIndex;
                        console.log(`找到同位置和弦音符，更新插入位置到: ${insertAfterIndex}`);
                    } else {
                        console.log(`defaultX不同 (${nextNote.defaultX} vs ${targetDefaultX})，和弦结束`);
                        break; // 遇到不同位置的音符，和弦结束
                    }
                }

                actualInsertIndex = insertAfterIndex + 1;
                console.log(`目标音符索引: ${targetNoteIndex}, 和弦结束索引: ${insertAfterIndex}, 插入位置: ${actualInsertIndex}`);
            } else {
                actualInsertIndex = this.musicData.notesList.length;
            }
        }

        console.log(`实际插入索引: ${actualInsertIndex}`);

        // 创建插入选择模态框
        this.showInsertChoiceModal(actualInsertIndex, measureNum, staffNum);
    }

    showInsertChoiceModal(position, measureNum, staffNum) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-sm">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">选择插入类型</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <p class="mb-3">请选择要插入的音符类型：</p>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-primary" onclick="visualEditor.insertSingleNoteAt(${position}, ${measureNum}, ${staffNum}); bootstrap.Modal.getInstance(this.closest('.modal')).hide();">
                                <i class="fas fa-music me-2"></i>单音符
                            </button>
                            <button type="button" class="btn btn-info" onclick="visualEditor.insertChordAt(${position}, ${measureNum}, ${staffNum}); bootstrap.Modal.getInstance(this.closest('.modal')).hide();">
                                <i class="fas fa-layer-group me-2"></i>和弦
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // 清理模态框
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    /**
     * 基于插入位置计算正确的defaultX值
     * @param {number} position 插入位置（在notesList中的索引）
     * @param {number} measureNum 小节号
     * @param {number} staffNum 谱表号
     * @returns {number} 计算出的defaultX值
     */
    calculateDefaultXForPosition(globalPosition, measureNum, staffNum) {
        // 获取该小节该谱表的所有音符，按时间顺序排列
        const notesInMeasureStaff = this.musicData.notesList.filter(n =>
            n.measure === measureNum && n.staff === staffNum
        );

        console.log(`🔍 计算全局位置${globalPosition}的defaultX，该谱表共有${notesInMeasureStaff.length}个音符`);

        if (notesInMeasureStaff.length === 0) {
            // 该谱表没有音符，使用默认起始位置
            console.log(`🔍 谱表为空，使用默认起始位置: 12`);
            return 12;
        }

        // 按defaultX排序
        notesInMeasureStaff.sort((a, b) => a.defaultX - b.defaultX);

        // 🔧 关键修复：基于音符组来计算位置，而不是单个音符
        // 首先将音符分组（和弦作为一个整体）
        const noteGroups = [];
        const processedNotes = new Set();

        for (const note of notesInMeasureStaff) {
            if (processedNotes.has(note.id)) continue;

            // 找到与当前音符在相同位置的所有音符（和弦）
            const groupNotes = notesInMeasureStaff.filter(n =>
                !processedNotes.has(n.id) &&
                Math.abs(n.defaultX - note.defaultX) < 5
            );

            groupNotes.forEach(n => processedNotes.add(n.id));
            noteGroups.push({
                defaultX: note.defaultX,
                notes: groupNotes,
                mainNote: groupNotes.find(n => !n.isChord) || groupNotes[0]
            });
        }

        console.log(`🔍 音符分组结果: ${noteGroups.length}个组`);
        noteGroups.forEach((group, index) => {
            const noteNames = group.notes.map(n => `${n.pitch}${n.octave}`).join('+');
            console.log(`  组${index}: ${noteNames} (defaultX: ${group.defaultX})`);
        });

        // 🔧 关键修复：globalPosition是插入位置，不是目标音符位置
        // 我们需要找到插入位置前一个音符所在的组，然后在该组之后插入
        let targetGroupIndex = -1;

        if (globalPosition > 0) {
            // 查找插入位置前一个音符
            const prevNote = this.musicData.notesList[globalPosition - 1];
            console.log(`🔍 插入位置${globalPosition}的前一个音符: ${prevNote ? prevNote.pitch + prevNote.octave : 'null'}`);

            if (prevNote && prevNote.measure === measureNum && prevNote.staff === staffNum) {
                // 找到前一个音符所在的组
                targetGroupIndex = noteGroups.findIndex(group =>
                    group.notes.some(n => n.id === prevNote.id)
                );
                console.log(`🔍 前一个音符${prevNote.pitch}${prevNote.octave}在第${targetGroupIndex}组，要在此组之后插入`);
            }
        } else {
            // 插入到开头，targetGroupIndex保持-1
            console.log(`🔍 插入到开头位置`);
        }

        if (targetGroupIndex === -1) {
            // 插入到开头或者没有找到前一个音符
            if (noteGroups.length > 0) {
                const firstGroup = noteGroups[0];
                const newX = Math.max(12, firstGroup.defaultX - 30);
                console.log(`🔍 插入到开头，在第一组前: ${newX}`);
                return newX;
            } else {
                console.log(`🔍 谱表为空，使用默认位置: 12`);
                return 12;
            }
        } else if (targetGroupIndex >= noteGroups.length - 1) {
            // 在最后一组之后插入
            const lastGroup = noteGroups[targetGroupIndex];
            const newX = lastGroup.defaultX + 40;
            console.log(`🔍 在最后一组后插入: ${newX}`);
            return newX;
        } else {
            // 在目标组和下一组之间插入
            const currentGroup = noteGroups[targetGroupIndex];
            const nextGroup = noteGroups[targetGroupIndex + 1];
            const newX = (currentGroup.defaultX + nextGroup.defaultX) / 2;
            console.log(`🔍 在目标组${targetGroupIndex}(${currentGroup.defaultX})和下一组${targetGroupIndex+1}(${nextGroup.defaultX})之间插入: ${newX}`);
            return newX;
        }
    }

    insertSingleNoteAt(position, measureNum, staffNum) {
        console.log(`🎵 在索引${position}插入单音符，小节${measureNum}，谱表${staffNum}`);

        // 调试：显示插入前的音符列表
        console.log('插入前的音符列表:');
        this.musicData.notesList.forEach((note, index) => {
            if (note.measure === measureNum && note.staff === staffNum) {
                console.log(`  ${index}: ${note.pitch}${note.octave} ${note.isChord ? '(和弦)' : ''}`);
            }
        });

        const defaultOctave = staffNum === 1 ? 5 : 3;

        // 获取目标音符的声部信息，用于保持一致性
        // 如果position > 0，则使用前一个音符的声部；否则查找该小节该谱表的第一个音符的声部
        let targetVoice = staffNum === 1 ? 1 : 5;  // 默认声部
        if (position > 0) {
            const targetNote = this.musicData.notesList[position - 1];
            if (targetNote && targetNote.measure === measureNum && targetNote.staff === staffNum) {
                targetVoice = targetNote.voice;
            }
        } else {
            // 查找该小节该谱表的第一个音符的声部
            const firstNoteInMeasureStaff = this.musicData.notesList.find(note =>
                note.measure === measureNum && note.staff === staffNum
            );
            if (firstNoteInMeasureStaff) {
                targetVoice = firstNoteInMeasureStaff.voice;
            }
        }

        // "+"按钮始终创建独立的单音符，不是和弦音符
        const newNote = {
            id: Date.now(),
            isRest: false,
            pitch: 'C',
            octave: defaultOctave,
            duration: 4, // 默认四分音符（duration=4对应四分音符，基于divisions=4）
            measure: parseInt(measureNum),
            voice: targetVoice,  // 使用目标音符的声部
            staff: parseInt(staffNum),
            clef: staffNum === 1 ? 'treble' : 'bass',
            isChord: false,  // "+"按钮始终创建独立单音符
            dotted: false,  // 默认不是附点音符
            defaultX: 0, // 将在插入时计算
            xmlIndex: this.getNextXmlIndex()
        };

        // 在插入前计算正确的defaultX位置
        newNote.defaultX = this.calculateDefaultXForPosition(position, measureNum, staffNum);
        console.log(`🎵 新音符defaultX设置为: ${newNote.defaultX}`);

        // 保存待处理的单音符数据（不插入到notesList，避免重复）
        this.pendingSingleNoteData = {
            note: newNote,
            insertPosition: position
        };

        // 直接显示新音符编辑模态框（不需要在notesList中查找）
        this.showNewNoteEditModal(newNote);

        console.log(`✅ 单音符已准备插入到位置${position}`);
    }

    showNewNoteEditModal(note) {
        console.log(`🎵 显示新音符编辑模态框，ID: ${note.id}`);

        // 创建编辑对话框
        const modal = this.createNoteEditModal(note);
        document.body.appendChild(modal);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // 清理模态框
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    insertChordAt(position, measureNum, staffNum) {
        const defaultOctave = staffNum === 1 ? 5 : 3;
        const baseTime = Date.now();

        const chordNotes = [
            {
                id: baseTime,
                isRest: false,
                pitch: 'C',
                octave: defaultOctave,
                duration: 4,
                measure: parseInt(measureNum),
                voice: 1,
                staff: parseInt(staffNum),
                clef: staffNum === 1 ? 'treble' : 'bass',
                isChord: false
            },
            {
                id: baseTime + 1,
                isRest: false,
                pitch: 'E',
                octave: defaultOctave,
                duration: 4,
                measure: parseInt(measureNum),
                voice: 1,
                staff: parseInt(staffNum),
                clef: staffNum === 1 ? 'treble' : 'bass',
                isChord: true
            },
            {
                id: baseTime + 2,
                isRest: false,
                pitch: 'G',
                octave: defaultOctave,
                duration: 4,
                measure: parseInt(measureNum),
                voice: 1,
                staff: parseInt(staffNum),
                clef: staffNum === 1 ? 'treble' : 'bass',
                isChord: true
            }
        ];

        console.log(`🎵 在索引${position}插入和弦，小节${measureNum}，谱表${staffNum}`);

        // 在指定位置插入和弦的所有音符
        chordNotes.forEach((note, index) => {
            this.musicData.notesList.splice(position + index, 0, note);
        });

        // 保存插入位置和音符信息，用于取消时回滚
        this.pendingChordData = {
            notes: chordNotes,
            insertPosition: position
        };

        // 直接显示和弦编辑模态框（不通过findChordNotes查找）
        this.showChordEditModal(chordNotes);

        console.log(`✅ 和弦已准备插入到位置${position}`);
    }

    editChord(chordId, position) {
        console.log(`🎵 编辑和弦: chordId=${chordId}, position=${position}`);

        // 从chordId中提取和弦的主音符ID
        const mainNoteId = chordId.replace('chord_', '');
        console.log(`🔍 查找主音符ID: ${mainNoteId}`);

        // 找到和弦的所有音符
        const chordNotes = this.findChordNotesByMainId(mainNoteId);
        if (!chordNotes || chordNotes.length === 0) {
            console.error(`❌ 找不到和弦音符，主音符ID: ${mainNoteId}`);
            this.showMessage('找不到和弦音符', 'warning');
            return;
        }

        console.log(`✅ 找到和弦，包含 ${chordNotes.length} 个音符:`, chordNotes.map(n => `${n.pitch}${n.octave}`));

        // 详细打印每个音符的信息
        chordNotes.forEach((note, index) => {
            console.log(`🔍 和弦音符 ${index}: ID=${note.id}, pitch=${note.pitch}, octave=${note.octave}, isChord=${note.isChord}, defaultX=${note.defaultX}`);
        });

        // 创建和弦编辑模态框
        this.showChordEditModal(chordNotes);
    }

    findChordNotesByMainId(mainNoteId) {
        // 根据主音符ID找到和弦的所有音符
        const mainNote = this.musicData.notesList.find(n => n.id.toString() === mainNoteId.toString());
        if (!mainNote) {
            console.error(`❌ 找不到主音符，ID: ${mainNoteId}`);
            return null;
        }

        console.log(`🔍 找到主音符: ${mainNote.pitch}${mainNote.octave}, measure=${mainNote.measure}, staff=${mainNote.staff}, defaultX=${mainNote.defaultX}`);

        // 使用groupNotesWithChords函数来正确识别和弦
        const processedNotes = this.groupNotesWithChords(this.musicData.notesList);

        // 查找包含这个主音符的和弦组
        for (let group of processedNotes) {
            if (group.type === 'chord' && group.id === `chord_${mainNoteId}`) {
                console.log(`✅ 通过groupNotesWithChords找到和弦，包含 ${group.notes.length} 个音符:`, group.notes.map(n => `${n.pitch}${n.octave}`));
                return group.notes;
            }
        }

        // 如果groupNotesWithChords没有找到和弦，可能是因为连续性被破坏
        // 使用defaultX位置作为备用方法
        console.log(`⚠️ groupNotesWithChords未找到和弦，使用defaultX位置查找...`);

        const chordNotes = [mainNote];
        const tolerance = 5; // 增加位置容差

        // 查找所有在相同位置的音符（不依赖连续性）
        this.musicData.notesList.forEach(note => {
            if (note.id !== mainNote.id && // 不是主音符本身
                note.isChord && // 是和弦音符
                note.measure === mainNote.measure && // 同一小节
                note.staff === mainNote.staff && // 同一谱表
                Math.abs(note.defaultX - mainNote.defaultX) < tolerance) { // 位置接近
                chordNotes.push(note);
                console.log(`🔍 找到和弦音符: ${note.pitch}${note.octave}, defaultX=${note.defaultX}`);
            }
        });

        console.log(`✅ 最终找到和弦，包含 ${chordNotes.length} 个音符:`, chordNotes.map(n => `${n.pitch}${n.octave}`));
        return chordNotes;
    }

    showChordEditModal(chordNotes) {
        // 创建和弦编辑模态框HTML
        const modalHtml = `
            <div class="modal fade" id="chordEditModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-layer-group me-2"></i>编辑和弦
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- 和弦整体设置 -->
                            <div class="card mb-3 border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-cog me-2"></i>和弦整体设置</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="form-label">时值</label>
                                            <select class="form-select" id="chord-global-duration">
                                                <option value="1" ${chordNotes[0].duration === 1 ? 'selected' : ''}>全音符</option>
                                                <option value="2" ${chordNotes[0].duration === 2 ? 'selected' : ''}>二分音符</option>
                                                <option value="4" ${chordNotes[0].duration === 4 ? 'selected' : ''}>四分音符</option>
                                                <option value="8" ${chordNotes[0].duration === 8 ? 'selected' : ''}>八分音符</option>
                                                <option value="16" ${chordNotes[0].duration === 16 ? 'selected' : ''}>十六分音符</option>
                                                <option value="32" ${chordNotes[0].duration === 32 ? 'selected' : ''}>三十二分音符</option>
                                                <option value="64" ${chordNotes[0].duration === 64 ? 'selected' : ''}>六十四分音符</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">附点</label>
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="checkbox" id="chord-global-dotted" ${chordNotes[0].dotted ? 'checked' : ''}>
                                                <label class="form-check-label" for="chord-global-dotted">
                                                    附点音符 (增加一半时值)
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">类型</label>
                                            <select class="form-select" id="chord-global-type" disabled>
                                                <option value="note" selected>音符</option>
                                            </select>
                                            <small class="text-muted">和弦只能包含音符</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 各音符设置 -->
                            <div id="chord-notes-container">
                                <h6 class="mb-3"><i class="fas fa-music me-2"></i>音符设置</h6>
                                ${chordNotes.map((note, index) => `
                                    <div class="card mb-2" data-note-index="${index}">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <span class="fw-bold">音符 ${index + 1}</span>
                                            ${chordNotes.length > 2 ? `
                                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="visualEditor.removeChordNote(${index})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            ` : ''}
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label class="form-label">音高</label>
                                                    <select class="form-select" id="chord-pitch-${index}">
                                                        <option value="C" ${note.pitch === 'C' ? 'selected' : ''}>C</option>
                                                        <option value="C#" ${note.pitch === 'C#' ? 'selected' : ''}>C#</option>
                                                        <option value="Db" ${note.pitch === 'Db' ? 'selected' : ''}>Db</option>
                                                        <option value="D" ${note.pitch === 'D' ? 'selected' : ''}>D</option>
                                                        <option value="D#" ${note.pitch === 'D#' ? 'selected' : ''}>D#</option>
                                                        <option value="Eb" ${note.pitch === 'Eb' ? 'selected' : ''}>Eb</option>
                                                        <option value="E" ${note.pitch === 'E' ? 'selected' : ''}>E</option>
                                                        <option value="F" ${note.pitch === 'F' ? 'selected' : ''}>F</option>
                                                        <option value="F#" ${note.pitch === 'F#' ? 'selected' : ''}>F#</option>
                                                        <option value="Gb" ${note.pitch === 'Gb' ? 'selected' : ''}>Gb</option>
                                                        <option value="G" ${note.pitch === 'G' ? 'selected' : ''}>G</option>
                                                        <option value="G#" ${note.pitch === 'G#' ? 'selected' : ''}>G#</option>
                                                        <option value="Ab" ${note.pitch === 'Ab' ? 'selected' : ''}>Ab</option>
                                                        <option value="A" ${note.pitch === 'A' ? 'selected' : ''}>A</option>
                                                        <option value="A#" ${note.pitch === 'A#' ? 'selected' : ''}>A#</option>
                                                        <option value="Bb" ${note.pitch === 'Bb' ? 'selected' : ''}>Bb</option>
                                                        <option value="B" ${note.pitch === 'B' ? 'selected' : ''}>B</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">八度</label>
                                                    <select class="form-select" id="chord-octave-${index}">
                                                        <option value="0" ${note.octave === 0 ? 'selected' : ''}>0 (极低音)</option>
                                                        <option value="1" ${note.octave === 1 ? 'selected' : ''}>1 (极低音)</option>
                                                        <option value="2" ${note.octave === 2 ? 'selected' : ''}>2 (低音)</option>
                                                        <option value="3" ${note.octave === 3 ? 'selected' : ''}>3 (低音)</option>
                                                        <option value="4" ${note.octave === 4 ? 'selected' : ''}>4 (中音)</option>
                                                        <option value="5" ${note.octave === 5 ? 'selected' : ''}>5 (高音)</option>
                                                        <option value="6" ${note.octave === 6 ? 'selected' : ''}>6 (高音)</option>
                                                        <option value="7" ${note.octave === 7 ? 'selected' : ''}>7 (超高音)</option>
                                                        <option value="8" ${note.octave === 8 ? 'selected' : ''}>8 (极高音)</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="visualEditor.addChordNote()">
                                <i class="fas fa-plus me-2"></i>添加音符到和弦
                            </button>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="visualEditor.cancelChordEdit()">取消</button>
                            <button type="button" class="btn btn-primary" onclick="visualEditor.saveChordChanges()">保存和弦</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除现有模态框
        const existingModal = document.getElementById('chordEditModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('chordEditModal'));
        modal.show();

        // 保存当前编辑的和弦音符
        this.currentEditingChord = chordNotes;
    }

    async deleteChord(chordId) {
        if (!confirm('确定要删除这个和弦吗？')) return;

        console.log(`🗑️ 开始删除和弦，ID: ${chordId}`);

        try {
            // 🔧 使用新的XML操作架构
            await this.executeXMLOperation('删除和弦', async () => {
                // 1. 解析和弦ID，提取主音符ID
                // chordId格式通常是 "chord_主音符ID"
                let mainNoteId;
                if (chordId.startsWith('chord_')) {
                    mainNoteId = parseInt(chordId.replace('chord_', ''));
                } else {
                    mainNoteId = parseInt(chordId);
                }

                console.log(`🔍 解析和弦ID: ${chordId} -> 主音符ID: ${mainNoteId}`);

                // 2. 找到主音符
                const mainNote = this.musicData.notesList.find(n => n.id === mainNoteId);
                if (!mainNote) {
                    throw new Error(`未找到主音符ID: ${mainNoteId}`);
                }

                console.log(`✅ 找到主音符: ${mainNote.pitch}${mainNote.octave}`);

                // 3. 🔧 修复：更精确地找到和弦的所有音符
                const chordNotes = [mainNote];

                console.log(`🔍 开始查找和弦音符，主音符信息:`);
                console.log(`   ID: ${mainNote.id}, pitch: ${mainNote.pitch}${mainNote.octave}`);
                console.log(`   isChord: ${mainNote.isChord}, chordGroup: ${mainNote.chordGroup}`);
                console.log(`   defaultX: ${mainNote.defaultX}, measure: ${mainNote.measure}, staff: ${mainNote.staff}`);

                // 🔧 使用更严格的条件查找和弦音符
                this.musicData.notesList.forEach(note => {
                    // 基本条件检查
                    if (note.id === mainNote.id) {
                        return; // 跳过主音符本身
                    }

                    // 🔧 严格的和弦音符检查
                    const isValidChordNote = (
                        note.isChord === true && // 必须是和弦音符
                        note.measure === mainNote.measure && // 同一小节
                        note.staff === mainNote.staff && // 同一谱表
                        note.chordGroup && mainNote.chordGroup && // 都有和弦组信息
                        note.chordGroup === mainNote.chordGroup // 和弦组完全匹配
                    );

                    if (isValidChordNote) {
                        chordNotes.push(note);
                        console.log(`🔍 找到有效和弦音符: ${note.pitch}${note.octave}`);
                        console.log(`   ID: ${note.id}, chordGroup: ${note.chordGroup}, defaultX: ${note.defaultX}`);
                    } else {
                        // 调试：记录为什么音符不匹配
                        if (note.measure === mainNote.measure && note.staff === mainNote.staff) {
                            console.log(`🔍 跳过音符 ${note.pitch}${note.octave} (ID: ${note.id}):`);
                            console.log(`   isChord: ${note.isChord}, chordGroup: ${note.chordGroup}`);
                            console.log(`   原因: ${!note.isChord ? '不是和弦音符' : '和弦组不匹配'}`);
                        }
                    }
                });

                console.log(`✅ 找到完整和弦，包含 ${chordNotes.length} 个音符`);

                // 4. 直接从XML中删除和弦的所有音符元素
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(this.currentXML, 'application/xml');

                // 检查XML解析是否成功
                const parserError = xmlDoc.querySelector('parsererror');
                if (parserError) {
                    console.error('❌ XML解析失败:', parserError.textContent);
                    throw new Error('XML解析失败: ' + parserError.textContent);
                }

                // 调试：检查XML中的所有音符元素
                const allNoteElements = xmlDoc.querySelectorAll('note[data-note-id]');
                console.log(`🔍 XML中共有 ${allNoteElements.length} 个音符元素`);

                if (allNoteElements.length === 0) {
                    // 如果没有找到带data-note-id的音符，检查所有音符
                    const allNotes = xmlDoc.querySelectorAll('note');
                    console.log(`🔍 XML中共有 ${allNotes.length} 个note元素（不限data-note-id）`);

                    if (allNotes.length === 0) {
                        console.error('❌ XML中没有找到任何note元素');
                        console.log(`🔍 XML根元素:`, xmlDoc.documentElement?.tagName);
                        console.log(`🔍 XML前500字符:`, this.currentXML.substring(0, 500));
                        throw new Error('XML中没有找到任何note元素');
                    }
                } else {
                    console.log(`🔍 XML中的音符ID列表:`, Array.from(allNoteElements).map(el => el.getAttribute('data-note-id')));
                }

                let deletedCount = 0;

                // 获取所有note元素，按照在XML中的顺序
                const allNotes = xmlDoc.querySelectorAll('note');
                console.log(`🔍 XML中共有 ${allNotes.length} 个note元素`);

                for (const note of chordNotes) {
                    console.log(`🔍 尝试删除音符: ID=${note.id}, pitch=${note.pitch}${note.octave}`);

                    let noteElement = null;

                    // 方法1: 通过data-note-id查找（如果存在）
                    try {
                        noteElement = xmlDoc.querySelector(`note[data-note-id="${note.id}"]`);
                        if (noteElement) {
                            console.log(`🔍 通过data-note-id找到音符元素: ${note.id}`);
                        }
                    } catch (e) {
                        console.warn(`⚠️ querySelector失败:`, e.message);
                    }

                    // 🔧 修复：删除有问题的索引查找方法，因为新添加的音符ID不对应XML位置
                    // 这个方法会导致删除错误的音符，特别是当有新添加的音符时

                    // 方法3: 遍历查找匹配的音符（更精确的匹配）
                    if (!noteElement) {
                        console.log(`🔍 开始遍历查找音符: ${note.pitch}${note.octave} (ID=${note.id})`);

                        // 重新获取当前的note元素列表（因为可能已经删除了一些）
                        const currentNotes = xmlDoc.querySelectorAll('note');
                        const targetPitch = note.pitch + note.octave;

                        // 🔧 收集所有匹配的候选音符，然后选择最合适的
                        const candidates = [];

                        for (let i = 0; i < currentNotes.length; i++) {
                            const el = currentNotes[i];

                            // 🔧 首先检查是否有data-note-id属性匹配
                            const elementId = el.getAttribute('data-note-id');
                            if (elementId && elementId === note.id.toString()) {
                                noteElement = el;
                                console.log(`🔍 通过遍历找到精确ID匹配的音符: ID=${note.id}`);
                                break;
                            }

                            const pitchElement = el.querySelector('pitch');
                            const isRest = el.querySelector('rest') !== null;

                            if (!isRest && pitchElement) {
                                const step = pitchElement.querySelector('step')?.textContent;
                                const alter = pitchElement.querySelector('alter')?.textContent;
                                const octave = pitchElement.querySelector('octave')?.textContent;

                                if (step && octave) {
                                    // 处理升降号 - 与parseNoteData保持一致的格式
                                    let candidatePitch = step;
                                    if (alter) {
                                        const alterValue = parseInt(alter);
                                        if (alterValue > 0) {
                                            candidatePitch += '#'.repeat(alterValue); // 升号
                                        } else if (alterValue < 0) {
                                            candidatePitch += 'b'.repeat(Math.abs(alterValue)); // 降号
                                        }
                                    }
                                    candidatePitch += octave;

                                    console.log(`🔍 检查候选音符: ${candidatePitch} vs 目标${targetPitch}`);

                                    if (candidatePitch === targetPitch) {
                                        // 检查小节和谱表是否匹配
                                        const measureElement = el.closest('measure');
                                        const measureNumber = measureElement?.getAttribute('number');
                                        const staffElement = el.querySelector('staff');
                                        const staffNumber = staffElement ? parseInt(staffElement.textContent) : null;

                                        console.log(`🔍 找到候选音符: ${candidatePitch}, 小节${measureNumber}, 谱表${staffNumber}`);

                                        // 检查是否在同一小节和谱表
                                        if (parseInt(measureNumber) === note.measure && staffNumber === note.staff) {
                                            const candidateDefaultX = parseFloat(el.getAttribute('default-x') || '0');
                                            candidates.push({
                                                element: el,
                                                index: i,
                                                hasId: !!elementId,
                                                id: elementId,
                                                defaultX: candidateDefaultX
                                            });
                                        }
                                    }
                                }
                            }
                        }

                        // 🔧 如果没有精确ID匹配，从候选音符中选择最合适的
                        if (!noteElement && candidates.length > 0) {
                            // 🔧 使用defaultX位置信息进行更精确的匹配
                            let bestCandidate = null;

                            if (candidates.length === 1) {
                                bestCandidate = candidates[0];
                            } else {
                                // 多个候选音符时，使用defaultX进行匹配
                                console.log(`🔍 多个候选音符，使用defaultX进行精确匹配，目标defaultX: ${note.defaultX}`);

                                for (const candidate of candidates) {
                                    const candidateDefaultX = parseFloat(candidate.element.getAttribute('default-x') || '0');
                                    console.log(`🔍 候选音符defaultX: ${candidateDefaultX}`);

                                    // 如果defaultX匹配（误差小于5），则选择这个候选音符
                                    if (Math.abs(candidateDefaultX - note.defaultX) < 5) {
                                        bestCandidate = candidate;
                                        console.log(`🔍 找到defaultX匹配的候选音符: ${candidateDefaultX} ≈ ${note.defaultX}`);
                                        break;
                                    }
                                }

                                // 如果没有defaultX匹配，则选择有ID的音符，或者第一个
                                if (!bestCandidate) {
                                    bestCandidate = candidates.find(c => c.hasId) || candidates[0];
                                    console.log(`🔍 没有defaultX匹配，使用备选方案`);
                                }
                            }

                            if (bestCandidate) {
                                noteElement = bestCandidate.element;
                                console.log(`🔍 从${candidates.length}个候选音符中选择: 索引${bestCandidate.index}, ID=${bestCandidate.id || '无'}`);
                            }
                        }
                    }

                    if (noteElement) {
                        noteElement.parentNode.removeChild(noteElement);
                        deletedCount++;
                        console.log(`✅ 音符${note.id}(${note.pitch}${note.octave})已从XML中删除`);
                    } else {
                        console.error(`❌ 未找到音符${note.id}(${note.pitch}${note.octave})的XML元素`);
                    }
                }

                if (deletedCount === 0) {
                    throw new Error(`未能删除任何音符元素。和弦包含${chordNotes.length}个音符，但XML中都找不到对应元素。`);
                }

                // 5. 清理孤立的连音线
                this.cleanupOrphanedTies(xmlDoc, chordNotes);

                // 6. 更新currentXML
                const serializer = new XMLSerializer();
                this.currentXML = serializer.serializeToString(xmlDoc);

                console.log(`✅ 和弦删除完成，共删除${deletedCount}个音符`);
                return true;
            });

            this.showMessage('和弦已删除，请点击保存按钮应用修改', 'info');

        } catch (error) {
            console.error('❌ 删除和弦失败:', error);
            this.showMessage(`删除和弦失败: ${error.message}`, 'error');
        }
    }

    /**
     * 清理删除音符后产生的孤立连音线
     * @param {Document} xmlDoc XML文档
     * @param {Array} deletedNotes 被删除的音符数组
     */
    cleanupOrphanedTies(xmlDoc, deletedNotes) {
        console.log('🧹 开始清理孤立的连音线...');

        // 获取被删除音符的音高信息
        const deletedPitches = deletedNotes.map(note => `${note.pitch}${note.octave}`);
        console.log(`🧹 被删除的音高: ${deletedPitches.join(', ')}`);

        // 查找所有剩余的音符中的连音线
        const allNotes = xmlDoc.querySelectorAll('note');
        let cleanedCount = 0;

        allNotes.forEach(noteElement => {
            const ties = noteElement.querySelectorAll('tie');
            ties.forEach(tie => {
                const tieType = tie.getAttribute('type');
                if (tieType === 'stop') {
                    // 对于结束连音线，检查是否有对应的开始连音线
                    const pitchElement = noteElement.querySelector('pitch');
                    if (pitchElement) {
                        const step = pitchElement.querySelector('step')?.textContent;
                        const octave = pitchElement.querySelector('octave')?.textContent;
                        const alter = pitchElement.querySelector('alter')?.textContent;

                        let notePitch = step;
                        if (alter) {
                            const alterValue = parseInt(alter);
                            if (alterValue > 0) {
                                notePitch += '#'.repeat(alterValue);
                            } else if (alterValue < 0) {
                                notePitch += 'b'.repeat(Math.abs(alterValue));
                            }
                        }
                        notePitch += octave;

                        // 如果这个音高在被删除的音符中，移除孤立的结束连音线
                        if (deletedPitches.includes(notePitch)) {
                            console.log(`🧹 移除孤立的连音线结束标记: ${notePitch}`);
                            tie.parentNode.removeChild(tie);

                            // 同时移除对应的notation元素
                            const notations = noteElement.querySelector('notations');
                            if (notations) {
                                const tiedNotations = notations.querySelectorAll('tied[type="stop"]');
                                tiedNotations.forEach(tiedNotation => {
                                    tiedNotation.parentNode.removeChild(tiedNotation);
                                });

                                // 如果notations元素为空，也移除它
                                if (notations.children.length === 0) {
                                    notations.parentNode.removeChild(notations);
                                }
                            }

                            cleanedCount++;
                        }
                    }
                }
            });
        });

        console.log(`🧹 连音线清理完成，共清理${cleanedCount}个孤立连音线`);
    }

    async saveChordChanges() {
        if (!this.currentEditingChord) return;

        try {
            console.log(`🔄 保存和弦修改，当前和弦包含 ${this.currentEditingChord.length} 个音符`);

            // 读取和弦整体设置
            const globalDuration = parseInt(document.getElementById('chord-global-duration').value);
            const globalDotted = document.getElementById('chord-global-dotted').checked;

            console.log(`🔄 和弦整体设置: duration=${globalDuration}, dotted=${globalDotted}`);

            // 收集所有音符的编辑数据
            const container = document.getElementById('chord-notes-container');
            const noteCards = container.querySelectorAll('.card[data-note-index]');
            const chordNotesData = [];

            noteCards.forEach((card) => {
                const noteIndex = parseInt(card.dataset.noteIndex);
                if (noteIndex < this.currentEditingChord.length) {
                    const note = this.currentEditingChord[noteIndex];
                    const pitch = document.getElementById(`chord-pitch-${noteIndex}`).value;
                    const octave = parseInt(document.getElementById(`chord-octave-${noteIndex}`).value);

                    chordNotesData.push({
                        id: note.id,
                        pitch: pitch,
                        octave: octave,
                        duration: globalDuration,
                        dotted: globalDotted,
                        isChord: noteIndex > 0, // 第一个音符为主音符，其余为和弦音符
                        measure: note.measure,
                        staff: note.staff,
                        voice: note.voice
                    });
                }
            });

            // 🔧 使用新的XML操作架构
            await this.executeXMLOperation('保存和弦修改', async () => {
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(this.currentXML, 'application/xml');

                // 处理每个音符
                for (let i = 0; i < chordNotesData.length; i++) {
                    const noteData = chordNotesData[i];

                    // 🔧 使用增强的音符查找逻辑
                    let noteElement = this.findNoteElementInXML(xmlDoc, noteData);

                    if (!noteElement) {
                        // 如果是新和弦，创建新的音符元素
                        if (this.pendingChordData) {
                            console.log(`🎵 创建新和弦音符XML元素: ${noteData.pitch}${noteData.octave}`);
                            const newNoteElement = this.createNoteXMLElement(xmlDoc, noteData);

                            // 为和弦音符添加chord元素（除了第一个主音符）
                            if (noteData.isChord) {
                                const chordElement = xmlDoc.createElement('chord');
                                newNoteElement.insertBefore(chordElement, newNoteElement.firstChild);
                            }

                            // 插入到适当的measure中
                            const measureElement = xmlDoc.querySelector(`measure[number="${noteData.measure}"]`);
                            if (measureElement) {
                                measureElement.appendChild(newNoteElement);
                            }
                        } else {
                            throw new Error(`未找到ID为${noteData.id}的音符元素`);
                        }
                    } else {
                        // 更新现有音符元素
                        console.log(`🔄 更新现有和弦音符XML元素: ${noteData.pitch}${noteData.octave}`);
                        this.updateNoteXMLElement(noteElement, noteData);

                        // 处理chord元素
                        const existingChord = noteElement.querySelector('chord');
                        if (noteData.isChord && !existingChord) {
                            // 需要添加chord元素
                            const chordElement = xmlDoc.createElement('chord');
                            noteElement.insertBefore(chordElement, noteElement.firstChild);
                        } else if (!noteData.isChord && existingChord) {
                            // 需要移除chord元素
                            noteElement.removeChild(existingChord);
                        }
                    }
                }

                // 清除待处理数据
                if (this.pendingChordData) {
                    this.pendingChordData = null;
                }

                // 更新currentXML
                const serializer = new XMLSerializer();
                this.currentXML = serializer.serializeToString(xmlDoc);

                console.log(`✅ 和弦保存完成，共处理${chordNotesData.length}个音符`);
                return true;
            });

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('chordEditModal'));
            modal.hide();

            this.showMessage('和弦已更新，请点击保存按钮应用修改', 'info');

        } catch (error) {
            console.error('❌ 保存和弦修改失败:', error);
            this.showMessage(`保存和弦修改失败: ${error.message}`, 'error');
        }
    }

    cancelChordEdit() {
        // 如果这是新添加的和弦，需要回滚
        if (this.pendingChordData) {
            console.log('🔄 取消新和弦编辑，回滚音符列表');

            // 从notesList中移除新添加的音符
            this.pendingChordData.notes.forEach(note => {
                this.musicData.notesList = this.musicData.notesList.filter(n => n.id !== note.id);
            });

            // 刷新显示
            this.loadNotesList();

            // 清除待处理数据
            this.pendingChordData = null;

            console.log('✅ 和弦添加已取消');
        } else {
            console.log('🔄 取消现有和弦编辑，无需回滚');
        }

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('chordEditModal'));
        modal.hide();

        // 清除当前编辑状态
        this.currentEditingChord = null;
    }

    addChordNote() {
        if (!this.currentEditingChord) return;

        // 创建新的和弦音符
        const baseNote = this.currentEditingChord[0];
        const newNote = {
            id: Date.now(),
            isRest: false,
            pitch: 'C',
            octave: baseNote.octave,
            duration: baseNote.duration,
            measure: baseNote.measure,
            voice: baseNote.voice,
            staff: baseNote.staff,
            clef: baseNote.clef,
            isChord: true,
            dotted: baseNote.dotted || false, // 继承基础音符的附点属性
            defaultX: baseNote.defaultX || 0, // 使用基础音符的位置
            xmlIndex: this.getNextXmlIndex()
        };

        // 添加到和弦编辑列表
        this.currentEditingChord.push(newNote);

        // 找到和弦在主列表中的最后一个音符位置
        const lastChordNoteIndex = this.findLastChordNoteIndex(baseNote);
        if (lastChordNoteIndex !== -1) {
            // 在和弦的最后一个音符后插入新音符
            this.musicData.notesList.splice(lastChordNoteIndex + 1, 0, newNote);
        } else {
            // 如果找不到位置，则添加到末尾（备用方案）
            this.musicData.notesList.push(newNote);
        }

        // 更新现有模态框内容而不是重新创建
        this.updateChordEditModalContent();
    }

    /**
     * 找到和弦在主音符列表中的最后一个音符的索引
     * @param {Object} baseNote 和弦的基础音符
     * @returns {number} 最后一个和弦音符的索引，如果找不到返回-1
     */
    findLastChordNoteIndex(baseNote) {
        let lastIndex = -1;
        for (let i = 0; i < this.musicData.notesList.length; i++) {
            const note = this.musicData.notesList[i];
            if (note.measure === baseNote.measure &&
                note.staff === baseNote.staff &&
                note.voice === baseNote.voice &&
                (note.id === baseNote.id || note.isChord)) {
                lastIndex = i;
            }
        }
        return lastIndex;
    }

    updateChordEditModalContent() {
        const modal = document.getElementById('chordEditModal');
        if (!modal || !this.currentEditingChord) return;

        // 更新和弦整体设置
        const globalDuration = modal.querySelector('#chord-global-duration');
        const globalDotted = modal.querySelector('#chord-global-dotted');
        if (globalDuration && this.currentEditingChord.length > 0) {
            globalDuration.value = this.currentEditingChord[0].duration;
        }
        if (globalDotted && this.currentEditingChord.length > 0) {
            globalDotted.checked = this.currentEditingChord[0].dotted || false;
        }

        const container = modal.querySelector('#chord-notes-container');
        if (!container) return;

        // 更新和弦音符容器内容（只包含音高和八度设置）
        const notesHtml = this.currentEditingChord.map((note, index) => `
            <div class="card mb-2" data-note-index="${index}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span class="fw-bold">音符 ${index + 1}</span>
                    ${this.currentEditingChord.length > 2 ? `
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="visualEditor.removeChordNote(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">音高</label>
                            <select class="form-select" id="chord-pitch-${index}">
                                <option value="C" ${note.pitch === 'C' ? 'selected' : ''}>C</option>
                                <option value="C#" ${note.pitch === 'C#' ? 'selected' : ''}>C#</option>
                                <option value="Db" ${note.pitch === 'Db' ? 'selected' : ''}>Db</option>
                                <option value="D" ${note.pitch === 'D' ? 'selected' : ''}>D</option>
                                <option value="D#" ${note.pitch === 'D#' ? 'selected' : ''}>D#</option>
                                <option value="Eb" ${note.pitch === 'Eb' ? 'selected' : ''}>Eb</option>
                                <option value="E" ${note.pitch === 'E' ? 'selected' : ''}>E</option>
                                <option value="F" ${note.pitch === 'F' ? 'selected' : ''}>F</option>
                                <option value="F#" ${note.pitch === 'F#' ? 'selected' : ''}>F#</option>
                                <option value="Gb" ${note.pitch === 'Gb' ? 'selected' : ''}>Gb</option>
                                <option value="G" ${note.pitch === 'G' ? 'selected' : ''}>G</option>
                                <option value="G#" ${note.pitch === 'G#' ? 'selected' : ''}>G#</option>
                                <option value="Ab" ${note.pitch === 'Ab' ? 'selected' : ''}>Ab</option>
                                <option value="A" ${note.pitch === 'A' ? 'selected' : ''}>A</option>
                                <option value="A#" ${note.pitch === 'A#' ? 'selected' : ''}>A#</option>
                                <option value="Bb" ${note.pitch === 'Bb' ? 'selected' : ''}>Bb</option>
                                <option value="B" ${note.pitch === 'B' ? 'selected' : ''}>B</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">八度</label>
                            <select class="form-select" id="chord-octave-${index}">
                                <option value="0" ${note.octave === 0 ? 'selected' : ''}>0 (极低音)</option>
                                <option value="1" ${note.octave === 1 ? 'selected' : ''}>1 (极低音)</option>
                                <option value="2" ${note.octave === 2 ? 'selected' : ''}>2 (低音)</option>
                                <option value="3" ${note.octave === 3 ? 'selected' : ''}>3 (低音)</option>
                                <option value="4" ${note.octave === 4 ? 'selected' : ''}>4 (中音)</option>
                                <option value="5" ${note.octave === 5 ? 'selected' : ''}>5 (高音)</option>
                                <option value="6" ${note.octave === 6 ? 'selected' : ''}>6 (高音)</option>
                                <option value="7" ${note.octave === 7 ? 'selected' : ''}>7 (超高音)</option>
                                <option value="8" ${note.octave === 8 ? 'selected' : ''}>8 (极高音)</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        // 找到音符设置部分并更新
        const notesSection = container.querySelector('h6') ? container : container.parentElement;
        if (notesSection) {
            const titleElement = notesSection.querySelector('h6');
            if (titleElement) {
                titleElement.nextElementSibling.innerHTML = notesHtml;
            } else {
                // 如果没有标题，直接替换内容
                container.innerHTML = `
                    <h6 class="mb-3"><i class="fas fa-music me-2"></i>音符设置</h6>
                    ${notesHtml}
                `;
            }
        }
    }

    removeChordNote(noteIndex) {
        if (!this.currentEditingChord || this.currentEditingChord.length <= 2) {
            this.showMessage('和弦至少需要2个音符', 'warning');
            return;
        }

        // 记录被删除音符是否为主音符
        const noteToRemove = this.currentEditingChord[noteIndex];
        const isRemovingMainNote = (noteToRemove.isChord === false);

        console.log(`🗑️ 删除和弦音符: ${noteToRemove.pitch}${noteToRemove.octave}, isChord: ${noteToRemove.isChord}, 是否为主音符: ${isRemovingMainNote}`);

        // 从音符列表中移除
        this.musicData.notesList = this.musicData.notesList.filter(n => n.id !== noteToRemove.id);

        // 从当前编辑的和弦中移除
        this.currentEditingChord.splice(noteIndex, 1);

        // 如果删除的是主音符，将第一个剩余音符设为主音符
        if (isRemovingMainNote && this.currentEditingChord.length > 0) {
            console.log(`🔄 删除了主音符，将 ${this.currentEditingChord[0].pitch}${this.currentEditingChord[0].octave} 设为新的主音符`);
            this.currentEditingChord[0].isChord = false;

            // 同时更新notesList中对应的音符
            const noteInList = this.musicData.notesList.find(n => n.id === this.currentEditingChord[0].id);
            if (noteInList) {
                noteInList.isChord = false;
                console.log(`✅ 已更新notesList中的音符 ${noteInList.pitch}${noteInList.octave} 为主音符`);
            }
        }

        // 更新现有模态框内容而不是重新创建
        this.updateChordEditModalContent();
    }

    autoFix() {
        // 智能修复功能正在开发中
        this.showMessage('🚧 智能修复功能正在开发中，敬请期待！', 'info');
    }

    /**
     * 🔧 确保所有notesList中的音符都在XML中存在
     */
    async ensureAllNotesInXML() {
        console.log('🔧 开始确保所有音符都在XML中...');

        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(this.currentXML, 'text/xml');

        let addedCount = 0;

        // 检查每个notesList中的音符是否在XML中存在
        for (const noteData of this.musicData.notesList) {
            const existingNote = xmlDoc.querySelector(`note[data-note-id="${noteData.id}"]`);

            if (!existingNote) {
                console.log(`🔧 音符 ${noteData.pitch}${noteData.octave} (ID=${noteData.id}) 在XML中缺失，重新添加...`);

                // 重新添加缺失的音符到XML
                const measures = xmlDoc.querySelectorAll('measure');
                const measure = measures[noteData.measure - 1];

                if (measure) {
                    // 创建音符元素
                    const noteElement = this.createNoteElement(xmlDoc, noteData);
                    noteElement.setAttribute('data-note-id', noteData.id.toString());

                    // 如果是新添加的音符，标记它
                    if (noteData.isModified) {
                        noteElement.setAttribute('data-added-by-editor', 'true');
                    }

                    // 插入到正确位置
                    this.insertNoteInOrder(xmlDoc, measure, noteElement, noteData);
                    addedCount++;

                    console.log(`✅ 重新添加音符 ${noteData.pitch}${noteData.octave} (ID=${noteData.id}) 到XML`);
                }
            }
        }

        if (addedCount > 0) {
            // 更新currentXML
            const serializer = new XMLSerializer();
            this.currentXML = serializer.serializeToString(xmlDoc);
            console.log(`🔧 重新添加了 ${addedCount} 个缺失的音符到XML`);
        } else {
            console.log('✅ 所有音符都在XML中，无需添加');
        }
    }

    async updateXMLWithNoteChanges(deletedPositions = null) {
        // 这个方法现在主要用于音符编辑后的同步，不再用于添加新音符
        return await this.safeXmlOperation('updateXMLWithNoteChanges', async () => {
            console.log('🔄 开始同步编辑器音符更改到XML...');

            const xmlDoc = this.getWorkingXmlDoc(true); // 跳过锁检查，因为我们已经在安全操作内部
            if (!xmlDoc) {
                throw new Error('无法获取工作XML文档');
            }

            // 找到第一个part（通常是钢琴部分）
            const parts = xmlDoc.querySelectorAll('part');
            if (parts.length === 0) {
                console.error('未找到part元素');
                return;
            }

            const part = parts[0];
            const measures = part.querySelectorAll('measure');

            // 1. 识别真正需要更新的音符（有修改标记的音符）
            const modifiedNoteIds = this.musicData.notesList
                .filter(note => note.isModified === true)
                .map(note => note.id);

            console.log(`🎯 检测到 ${modifiedNoteIds.length} 个修改的音符: [${modifiedNoteIds.join(', ')}]`);

            // 2. 🔧 修复：先为音符添加data-note-id，再删除多余的音符
            // 2.1 为所有音符添加data-note-id属性，确保删除逻辑能正确识别音符
            this.fixMissingNoteIds(xmlDoc, null); // null表示处理所有音符

            // 2.2 删除XML中不存在于notesList的音符
            this.removeOrphanedXMLNotes(xmlDoc);

            // 3. 更新已存在的编辑器音符（但只更新修改过的音符）
            this.musicData.notesList.forEach(noteData => {
                // 只处理修改过的音符
                if (!noteData.isModified) {
                    return;
                }

                // 查找对应的XML音符元素
                const existingNote = xmlDoc.querySelector(`note[data-note-id="${noteData.id}"]`);
                if (existingNote) {
                    // 检查是否是新添加的音符（有data-added-by-editor属性）
                    const isNewlyAdded = existingNote.hasAttribute('data-added-by-editor');
                    if (isNewlyAdded) {
                        console.log(`跳过新添加的音符 ${noteData.id}: ${noteData.pitch}${noteData.octave}（已经是正确的）`);
                        return; // 跳过新添加的音符，它们已经是正确的
                    }

                    // 更新现有音符的属性
                    this.updateExistingNoteElement(xmlDoc, existingNote, noteData);
                    console.log(`✅ 更新音符 ${noteData.id}: ${noteData.pitch}${noteData.octave}`);
                } else {
                    console.log(`⚠️ 未找到XML音符 ${noteData.id}: ${noteData.pitch}${noteData.octave}`);
                }
            });

            // 2. 建立notesList中音符ID的映射（用于精确同步）
            const notesListIds = new Set(this.musicData.notesList.map(note => note.id.toString()));
            console.log(`🔍 notesList中的音符ID: [${Array.from(notesListIds).slice(0, 5).join(', ')}${notesListIds.size > 5 ? '...' : ''}] (共${notesListIds.size}个)`);

            // 3. 🔧 修复：改进同步验证逻辑
            const remainingXMLNotes = xmlDoc.querySelectorAll('note[data-note-id]');
            const notesListCount = this.musicData.notesList.length;

            console.log(`🔍 同步后验证: XML中有ID音符=${remainingXMLNotes.length}, notesList音符=${notesListCount}`);

            // 🔧 更精确的验证：检查每个notesList中的音符是否在XML中存在
            let missingInXML = 0;
            let extraInXML = 0;

            this.musicData.notesList.forEach(note => {
                const xmlNote = xmlDoc.querySelector(`note[data-note-id="${note.id}"]`);
                if (!xmlNote) {
                    missingInXML++;
                    console.warn(`⚠️ notesList中的音符${note.id}在XML中不存在`);
                }
            });

            remainingXMLNotes.forEach(xmlNote => {
                const noteId = xmlNote.getAttribute('data-note-id');
                const noteInList = this.musicData.notesList.find(n => n.id == noteId);
                if (!noteInList) {
                    extraInXML++;
                    console.warn(`⚠️ XML中的音符${noteId}在notesList中不存在`);
                }
            });

            if (missingInXML > 0 || extraInXML > 0) {
                console.warn(`⚠️ 同步验证失败: notesList缺失${missingInXML}个, XML多余${extraInXML}个`);
                // 尝试自动修复
                this.performEmergencyXMLSync(xmlDoc, notesListIds);
            } else {
                console.log(`✅ 同步验证通过: 数据一致`);
            }

            // 更新XML
            const serializer = new XMLSerializer();
            const newXML = serializer.serializeToString(xmlDoc);

            // 🔧 使用统一的XML提交系统
            if (!this.commitXmlChanges(xmlDoc, true)) { // 跳过锁检查，因为我们已经在安全操作内部
                throw new Error('XML更改提交失败');
            }

            // 更新统计信息
            this.musicData.notes = this.musicData.notesList.length;
            this.updateUI();

            console.log('✅ 编辑器音符更改已同步到XML');
            return true;
        });
    }

    /**
     * 🔧 内部版本的XML同步方法 - 用于事务内部调用，不获取锁
     * @param {Array} deletedPositions 被删除音符的位置数组
     * @returns {Promise<boolean>} 是否成功
     */
    async updateXMLWithNoteChangesInternal(deletedPositions = null) {
        console.log('🔄 开始同步编辑器音符更改到XML (内部调用)...');
        const xmlDoc = this.getWorkingXmlDoc(true); // 跳过锁检查
        if (!xmlDoc) {
            throw new Error('无法获取工作XML文档');
        }

        // 找到第一个part（通常是钢琴部分）
        const parts = xmlDoc.querySelectorAll('part');
        if (parts.length === 0) {
            console.error('未找到part元素');
            return false;
        }

        const part = parts[0];
        const measures = part.querySelectorAll('measure');

        // 1. 识别真正需要更新的音符（有修改标记的音符）
        const modifiedNoteIds = this.musicData.notesList
            .filter(note => note.isModified === true)
            .map(note => note.id);

        console.log(`🎯 检测到 ${modifiedNoteIds.length} 个修改的音符: [${modifiedNoteIds.join(', ')}]`);

        // 2. 🔧 修复：为所有音符添加data-note-id属性，确保同步验证正常工作
        this.fixMissingNoteIds(xmlDoc, null); // null表示处理所有音符

        // 3. 更新已存在的编辑器音符（但只更新修改过的音符）
        this.musicData.notesList.forEach(noteData => {
            // 只处理修改过的音符
            if (!noteData.isModified) {
                return;
            }

            // 查找对应的XML音符元素
            const existingNote = xmlDoc.querySelector(`note[data-note-id="${noteData.id}"]`);
            if (existingNote) {
                // 检查是否是新添加的音符（有data-added-by-editor属性）
                const isNewlyAdded = existingNote.hasAttribute('data-added-by-editor');
                if (isNewlyAdded) {
                    console.log(`跳过新添加的音符 ${noteData.id}: ${noteData.pitch}${noteData.octave}（已经是正确的）`);
                    return; // 跳过新添加的音符，它们已经是正确的
                }

                // 更新现有音符的属性
                this.updateExistingNoteElement(xmlDoc, existingNote, noteData);
                console.log(`✅ 更新音符 ${noteData.id}: ${noteData.pitch}${noteData.octave}`);
            } else {
                console.log(`⚠️ 未找到XML音符 ${noteData.id}: ${noteData.pitch}${noteData.octave}`);
            }
        });

        // 4. 建立notesList中音符ID的映射（用于精确同步）
        const notesListIds = new Set(this.musicData.notesList.map(note => note.id.toString()));
        console.log(`🔍 notesList中的音符ID: [${Array.from(notesListIds).slice(0, 5).join(', ')}${notesListIds.size > 5 ? '...' : ''}] (共${notesListIds.size}个)`);

        // 5. 精确删除XML中不存在于notesList的音符
        const allXMLNotes = Array.from(xmlDoc.querySelectorAll('note'));
        const xmlNotesToDelete = [];

        console.log(`🔍 XML中找到 ${allXMLNotes.length} 个音符，notesList有 ${notesListIds.size} 个音符 (内部)`);

        allXMLNotes.forEach((xmlNote, index) => {
            const noteId = xmlNote.getAttribute('data-note-id');
            const isOriginalNote = xmlNote.getAttribute('data-original-note') === 'true';
            const isAddedByEditor = xmlNote.getAttribute('data-added-by-editor') === 'true';

            // 🔧 修复：检查所有有ID的音符是否还在notesList中
            if (noteId) {
                console.log(`🔍 检查有ID音符: ID=${noteId}, 在notesList中=${notesListIds.has(noteId)} (内部)`);
                if (!notesListIds.has(noteId)) {
                    xmlNotesToDelete.push({
                        element: xmlNote,
                        noteId: noteId,
                        index: index
                    });
                    console.log(`🗑️ 标记删除已被删除的音符: ID=${noteId}, 索引=${index}, 类型=${isAddedByEditor ? '编辑器添加' : isOriginalNote ? '原始音符' : '未知'} (内部)`);
                }
            }
            // 🔧 修复：对于没有ID的音符，使用增强匹配算法
            else {
                console.log(`🔍 检查无ID音符: 索引=${index} (内部)`);

                // 🔧 临时修复：跳过休止符的删除检查，避免误删除
                const isRest = xmlNote.querySelector('rest') !== null;
                if (isRest) {
                    console.log(`⏭️ 跳过休止符删除检查: 索引=${index} (内部)`);
                } else {
                    const shouldDelete = this.shouldDeleteOriginalNoteWithoutId(xmlNote, notesListIds);
                    if (shouldDelete) {
                        xmlNotesToDelete.push({
                            element: xmlNote,
                            noteId: 'no-id',
                            index: index
                        });
                        console.log(`🗑️ 标记删除无ID的音符: 音高=${this.getNotePitchFromXML(xmlNote)}, 索引=${index} (内部)`);
                    }
                }
            }
        });

        // 6. 统一删除标记的音符
        if (xmlNotesToDelete.length > 0) {
            xmlNotesToDelete.forEach((noteInfo, index) => {
                console.log(`🗑️ 删除第 ${index + 1}/${xmlNotesToDelete.length} 个孤立的XML音符 (ID: ${noteInfo.noteId})`);
                noteInfo.element.remove();
            });
            console.log(`🗑️ 删除了 ${xmlNotesToDelete.length} 个孤立的XML音符`);
        } else {
            console.log(`✅ 没有发现孤立的XML音符`);
        }

        // 7. 🔧 修复：改进同步验证逻辑
        const remainingXMLNotes = xmlDoc.querySelectorAll('note[data-note-id]');
        const notesListCount = this.musicData.notesList.length;

        console.log(`🔍 同步后验证: XML中有ID音符=${remainingXMLNotes.length}, notesList音符=${notesListCount}`);

        // 🔧 更精确的验证：检查每个notesList中的音符是否在XML中存在
        let missingInXML = 0;
        let extraInXML = 0;

        this.musicData.notesList.forEach(note => {
            const xmlNote = xmlDoc.querySelector(`note[data-note-id="${note.id}"]`);
            if (!xmlNote) {
                missingInXML++;
                console.warn(`⚠️ notesList中的音符${note.id}在XML中不存在`);
            }
        });

        remainingXMLNotes.forEach(xmlNote => {
            const noteId = xmlNote.getAttribute('data-note-id');
            const noteInList = this.musicData.notesList.find(n => n.id == noteId);
            if (!noteInList) {
                extraInXML++;
                console.warn(`⚠️ XML中的音符${noteId}在notesList中不存在`);
            }
        });

        if (missingInXML > 0 || extraInXML > 0) {
            console.warn(`⚠️ 同步验证失败: notesList缺失${missingInXML}个, XML多余${extraInXML}个`);
            // 尝试自动修复
            this.performEmergencyXMLSync(xmlDoc, notesListIds);
        } else {
            console.log(`✅ 同步验证通过: 数据一致`);
        }

        // 🔧 使用统一的XML提交系统
        if (!this.commitXmlChanges(xmlDoc, true)) { // 跳过锁检查
            throw new Error('XML更改提交失败');
        }

        // 更新统计信息
        this.musicData.notes = this.musicData.notesList.length;
        this.updateUI();

        console.log('✅ 编辑器音符更改已同步到XML (内部调用)');
        return true;
    }

    /**
     * 🔧 记录最近删除的音符信息，用于精确删除匹配
     */
    recordRecentlyDeletedNotes() {
        // 获取当前XML中的所有音符
        const currentXmlDoc = (() => {
            const xmlParser = new DOMParser();
            return xmlParser.parseFromString(this.currentXML, 'application/xml');
        })();

        const currentXMLNotes = Array.from(currentXmlDoc.querySelectorAll('note'));
        const currentNotesListIds = new Set(this.musicData.notesList.map(note => note.id.toString()));

        // 找到在XML中存在但在notesList中不存在的音符（即被删除的音符）
        this.recentlyDeletedNotes = [];

        currentXMLNotes.forEach(xmlNote => {
            const noteId = xmlNote.getAttribute('data-note-id');

            // 如果音符有ID且不在notesList中，说明被删除了
            if (noteId && !currentNotesListIds.has(noteId)) {
                const xmlPitch = this.getNotePitchFromXML(xmlNote);
                const xmlMeasure = parseInt(xmlNote.closest('measure')?.getAttribute('number') || '1');
                const xmlStaff = parseInt(xmlNote.querySelector('staff')?.textContent || '1');
                const xmlVoice = parseInt(xmlNote.querySelector('voice')?.textContent || '1');
                const xmlDuration = this.getDurationFromXMLNote(xmlNote);
                const xmlDefaultX = parseFloat(xmlNote.getAttribute('default-x') || '0');
                const xmlIsRest = xmlNote.querySelector('rest') !== null;

                this.recentlyDeletedNotes.push({
                    id: noteId,
                    pitch: xmlIsRest ? 'Rest' : xmlPitch.replace(/\d+$/, ''),
                    octave: xmlIsRest ? 0 : parseInt(xmlPitch.match(/\d+$/)?.[0] || '4'),
                    measure: xmlMeasure,
                    staff: xmlStaff,
                    voice: xmlVoice,
                    duration: xmlDuration,
                    defaultX: xmlDefaultX,
                    isRest: xmlIsRest
                });

                console.log(`🗑️ 记录被删除音符: ID=${noteId}, ${xmlPitch}, 小节${xmlMeasure}, 谱表${xmlStaff}, 声部${xmlVoice}, 位置${xmlDefaultX}`);
            }
        });

        console.log(`🗑️ 总共记录了 ${this.recentlyDeletedNotes.length} 个被删除的音符`);
    }

    /**
     * 🎯 判断没有ID的原始音符是否应该被删除 (使用增强匹配算法)
     * @param {Element} xmlNote XML音符元素
     * @param {Set} notesListIds notesList中的音符ID集合
     * @returns {boolean} 是否应该删除
     */
    shouldDeleteOriginalNoteWithoutId(xmlNote, notesListIds) {
        // 🎯 使用增强的音符匹配算法
        const xmlSignature = this.generateXMLNoteSignature(xmlNote);
        console.log(`🔍 检查无ID音符: ${xmlSignature}`);

        // 🔧 关键修复：检查这个音符是否是刚刚被删除的音符
        // 通过检查最近删除的音符列表来判断
        if (this.recentlyDeletedNotes && this.recentlyDeletedNotes.length > 0) {
            const xmlPitch = this.getNotePitchFromXML(xmlNote);
            const xmlMeasure = parseInt(xmlNote.closest('measure')?.getAttribute('number') || '1');
            const xmlStaff = parseInt(xmlNote.querySelector('staff')?.textContent || '1');
            const xmlVoice = parseInt(xmlNote.querySelector('voice')?.textContent || '1');
            const xmlDuration = this.getDurationFromXMLNote(xmlNote);
            const xmlDefaultX = parseFloat(xmlNote.getAttribute('default-x') || '0');

            // 检查是否匹配最近删除的音符
            const matchesDeletedNote = this.recentlyDeletedNotes.some(deletedNote => {
                const pitchMatch = (deletedNote.pitch + deletedNote.octave) === xmlPitch;
                const measureMatch = deletedNote.measure === xmlMeasure;
                const staffMatch = deletedNote.staff === xmlStaff;

                console.log(`🔍 检查删除匹配: 被删除音符(${deletedNote.pitch}${deletedNote.octave}, 小节${deletedNote.measure}, 谱表${deletedNote.staff}, 声部${deletedNote.voice}, 时值${deletedNote.duration}, 位置${deletedNote.defaultX})`);
                console.log(`🔍 检查删除匹配: XML音符(${xmlPitch}, 小节${xmlMeasure}, 谱表${xmlStaff}, 声部${xmlVoice}, 时值${xmlDuration}, 位置${xmlDefaultX})`);
                console.log(`🔍 匹配结果: pitch=${pitchMatch}, measure=${measureMatch}, staff=${staffMatch}`);

                // 🔧 激进修复：只要音高、小节、谱表匹配就认为是被删除的音符
                // 这样可以处理时值、声部、位置发生变化的情况
                const isMatch = pitchMatch && measureMatch && staffMatch;

                if (isMatch) {
                    console.log(`🎯 找到匹配的被删除音符: ${xmlPitch} 在小节${xmlMeasure}谱表${xmlStaff}`);
                }

                return isMatch;
            });

            if (matchesDeletedNote) {
                console.log(`🗑️ 匹配最近删除的音符: ${xmlSignature} -> 应删除`);
                return true;
            }
        }

        // 🔧 修复：使用更严格的精确匹配，包括位置信息
        const xmlMeasure = parseInt(xmlNote.closest('measure')?.getAttribute('number') || '1');
        const xmlStaff = parseInt(xmlNote.querySelector('staff')?.textContent || '1');
        const xmlVoice = parseInt(xmlNote.querySelector('voice')?.textContent || '1');
        const xmlDefaultX = parseFloat(xmlNote.getAttribute('default-x') || '0');
        const xmlPitch = this.getNotePitchFromXML(xmlNote);
        const xmlDuration = this.getDurationFromXMLNote(xmlNote);
        const xmlIsRest = xmlNote.querySelector('rest') !== null;

        // 🔧 关键修复：统计notesList中具有相同特征的音符数量
        const similarNotes = this.musicData.notesList.filter(note => {
            // 基本信息匹配
            const pitchMatch = xmlIsRest ? note.isRest :
                              (note.pitch + note.octave) === xmlPitch;
            const measureMatch = note.measure === xmlMeasure;
            const staffMatch = note.staff === xmlStaff;
            const voiceMatch = note.voice === xmlVoice;
            const durationMatch = note.duration === xmlDuration;

            // 🔧 修复：休止符不使用位置匹配，因为删除操作会改变位置
            if (xmlIsRest && note.isRest) {
                return pitchMatch && measureMatch && staffMatch && voiceMatch && durationMatch;
            }

            // 🔧 关键修复：对于重复音符问题，使用极严格的匹配条件
            // 位置匹配必须完全相同（误差0像素），并且声部也必须匹配
            const positionMatch = Math.abs(note.defaultX - xmlDefaultX) === 0;
            const isMatch = pitchMatch && measureMatch && staffMatch && voiceMatch && durationMatch && positionMatch;

            // 🔍 详细调试：显示匹配过程
            if (pitchMatch && measureMatch && staffMatch) {
                console.log(`🔍 候选音符: ID=${note.id}, ${note.pitch}${note.octave}, 小节${note.measure}, 谱表${note.staff}, 声部${note.voice}, 时值${note.duration}, 位置${note.defaultX}`);
                console.log(`🔍 匹配检查: pitch=${pitchMatch}, measure=${measureMatch}, staff=${staffMatch}, voice=${voiceMatch}, duration=${durationMatch}, position=${positionMatch} (差值=${Math.abs(note.defaultX - xmlDefaultX)})`);
                console.log(`🔍 最终匹配: ${isMatch}`);
            }

            return isMatch;
        });

        // 🔧 关键修复：统计XML中具有相同特征的音符数量（包括当前音符）
        const allXMLNotes = Array.from(xmlNote.ownerDocument.querySelectorAll('note'));
        const similarXMLNotes = allXMLNotes.filter(otherXmlNote => {
            const otherMeasure = parseInt(otherXmlNote.closest('measure')?.getAttribute('number') || '1');
            const otherStaff = parseInt(otherXmlNote.querySelector('staff')?.textContent || '1');
            const otherVoice = parseInt(otherXmlNote.querySelector('voice')?.textContent || '1');
            const otherDefaultX = parseFloat(otherXmlNote.getAttribute('default-x') || '0');
            const otherPitch = this.getNotePitchFromXML(otherXmlNote);
            const otherDuration = this.getDurationFromXMLNote(otherXmlNote);
            const otherIsRest = otherXmlNote.querySelector('rest') !== null;

            // 基本信息匹配
            const pitchMatch = xmlIsRest ? otherIsRest : otherPitch === xmlPitch;
            const measureMatch = otherMeasure === xmlMeasure;
            const staffMatch = otherStaff === xmlStaff;
            const voiceMatch = otherVoice === xmlVoice;
            const durationMatch = otherDuration === xmlDuration;

            if (xmlIsRest && otherIsRest) {
                return pitchMatch && measureMatch && staffMatch && voiceMatch && durationMatch;
            }

            // 🔧 关键修复：使用极严格的位置匹配（完全相同）
            const positionMatch = Math.abs(otherDefaultX - xmlDefaultX) === 0;

            return pitchMatch && measureMatch && staffMatch && voiceMatch && durationMatch && positionMatch;
        });

        console.log(`🔍 匹配分析: XML中${similarXMLNotes.length}个相似音符, notesList中${similarNotes.length}个相似音符`);

        // 🔧 关键逻辑：如果XML中的相似音符数量 > notesList中的相似音符数量，说明有多余的音符需要删除
        if (similarXMLNotes.length > similarNotes.length) {
            console.log(`🗑️ 发现多余音符: ${xmlSignature} -> 应删除 (XML:${similarXMLNotes.length} > notesList:${similarNotes.length})`);
            return true;
        }

        console.log(`✅ 音符数量匹配: ${xmlSignature} -> 保留 (XML:${similarXMLNotes.length} = notesList:${similarNotes.length})`);
        return false;
    }

    /**
     * 从XML音符元素中提取音高信息
     * @param {Element} xmlNote XML音符元素
     * @returns {string} 音高字符串，如"D3"
     */
    getNotePitchFromXML(xmlNote) {
        const pitchElement = xmlNote.querySelector('pitch');
        if (!pitchElement) return 'Unknown';

        const step = pitchElement.querySelector('step')?.textContent || '';
        const octave = pitchElement.querySelector('octave')?.textContent || '';
        const alter = pitchElement.querySelector('alter')?.textContent;

        let pitchName = step;
        if (alter) {
            const alterValue = parseInt(alter);
            if (alterValue > 0) {
                pitchName += '#'.repeat(alterValue);
            } else if (alterValue < 0) {
                pitchName += 'b'.repeat(Math.abs(alterValue));
            }
        }

        return pitchName + octave;
    }

    /**
     * 紧急XML同步修复 - 当常规同步失败时使用
     * @param {Document} xmlDoc XML文档对象
     * @param {Set} notesListIds notesList中的音符ID集合
     */
    performEmergencyXMLSync(xmlDoc, notesListIds) {
        console.log('🚨 执行紧急XML同步修复...');

        try {
            // 1. 获取所有XML音符
            const allXMLNotes = Array.from(xmlDoc.querySelectorAll('note'));
            let fixedCount = 0;

            // 2. 🔧 修复：检查所有有ID的音符，不限制类型
            allXMLNotes.forEach((xmlNote, index) => {
                const noteId = xmlNote.getAttribute('data-note-id');
                const isAddedByEditor = xmlNote.getAttribute('data-added-by-editor') === 'true';
                const isOriginalNote = xmlNote.getAttribute('data-original-note') === 'true';

                // 🔧 修复：删除所有不在notesList中的有ID音符
                if (noteId && !notesListIds.has(noteId)) {
                    console.log(`🚨 紧急删除孤立音符: ID=${noteId}, 类型=${isAddedByEditor ? '编辑器添加' : isOriginalNote ? '原始音符' : '未知'}`);
                    xmlNote.remove();
                    fixedCount++;
                }
            });

            console.log(`🚨 紧急修复完成，删除了 ${fixedCount} 个孤立音符`);

        } catch (error) {
            console.error('🚨 紧急同步修复失败:', error);
        }
    }

    updateExistingNoteElement(xmlDoc, noteElement, noteData) {
        try {
            // 保守更新：只更新需要修改的元素，保留其他重要信息（如beam、slur、tie等）

            // 1. 更新或创建pitch元素
            if (!noteData.isRest) {
                let pitch = noteElement.querySelector('pitch');
                if (!pitch) {
                    pitch = xmlDoc.createElement('pitch');
                    // 插入到适当位置（在chord元素之后，duration元素之前）
                    const chordElement = noteElement.querySelector('chord');
                    const durationElement = noteElement.querySelector('duration');
                    if (chordElement) {
                        noteElement.insertBefore(pitch, chordElement.nextSibling);
                    } else if (durationElement) {
                        noteElement.insertBefore(pitch, durationElement);
                    } else {
                        noteElement.appendChild(pitch);
                    }
                }

                // 更新step
                let step = pitch.querySelector('step');
                if (!step) {
                    step = xmlDoc.createElement('step');
                    pitch.appendChild(step);
                }
                step.textContent = noteData.pitch;

                // 更新octave
                let octave = pitch.querySelector('octave');
                if (!octave) {
                    octave = xmlDoc.createElement('octave');
                    pitch.appendChild(octave);
                }
                octave.textContent = noteData.octave.toString();
            } else {
                const rest = xmlDoc.createElement('rest');
                noteElement.appendChild(rest);
            }

            // 如果是和弦音符（isChord: true），确保有chord元素
            if (noteData.isChord === true) {
                let chord = noteElement.querySelector('chord');
                if (!chord) {
                    chord = xmlDoc.createElement('chord');
                    // 插入到pitch元素之后
                    const pitchElement = noteElement.querySelector('pitch');
                    if (pitchElement) {
                        noteElement.insertBefore(chord, pitchElement.nextSibling);
                    } else {
                        noteElement.appendChild(chord);
                    }
                    console.log(`🎵 为和弦音符 ${noteData.pitch}${noteData.octave} 添加 <chord/> 标记`);
                } else {
                    console.log(`🎵 和弦音符 ${noteData.pitch}${noteData.octave} 已有 <chord/> 标记，跳过`);
                }
            }

            // 2. 更新duration元素
            let duration = noteElement.querySelector('duration');
            if (!duration) {
                duration = xmlDoc.createElement('duration');
                // 插入到voice元素之前
                const voiceElement = noteElement.querySelector('voice');
                if (voiceElement) {
                    noteElement.insertBefore(duration, voiceElement);
                } else {
                    noteElement.appendChild(duration);
                }
            }

            const divisions = this.getDivisionsFromXML(xmlDoc) || 4;
            let baseDurationValue = Math.round(divisions * (4 / noteData.duration));
            if (noteData.dotted) {
                baseDurationValue = Math.round(baseDurationValue * 1.5);
            }
            duration.textContent = baseDurationValue.toString();
            console.log(`🔧 更新duration: ${baseDurationValue} (noteData.duration=${noteData.duration}, dotted=${noteData.dotted}, divisions=${divisions})`);

            // 3. 更新type元素
            let type = noteElement.querySelector('type');
            if (!type) {
                type = xmlDoc.createElement('type');
                // 插入到stem元素之前
                const stemElement = noteElement.querySelector('stem');
                if (stemElement) {
                    noteElement.insertBefore(type, stemElement);
                } else {
                    noteElement.appendChild(type);
                }
            }

            // 🔧 使用正确的MusicXML type值
            let mappedType;
            if (noteData.duration === 1) {
                mappedType = 'whole';
            } else if (noteData.duration === 2) {
                mappedType = 'half';
            } else if (noteData.duration === 4) {
                mappedType = 'quarter';
            } else if (noteData.duration === 8) {
                mappedType = 'eighth';
            } else if (noteData.duration === 16) {
                mappedType = '16th';       // 🔧 修复：使用正确的MusicXML值
            } else if (noteData.duration === 32) {
                mappedType = '32nd';
            } else if (noteData.duration === 64) {
                mappedType = '64th';
            } else if (noteData.duration === 128) {
                mappedType = '128th';
            } else {
                mappedType = 'quarter';
            }
            type.textContent = mappedType;
            console.log(`🔧 更新type: ${type.textContent} (noteData.duration=${noteData.duration})`);

            // 4. 处理附点元素
            let dot = noteElement.querySelector('dot');
            if (noteData.dotted && !dot) {
                // 需要附点但没有，添加
                dot = xmlDoc.createElement('dot');
                // 插入到stem元素之前
                const stemElement = noteElement.querySelector('stem');
                if (stemElement) {
                    noteElement.insertBefore(dot, stemElement);
                } else {
                    noteElement.appendChild(dot);
                }
                console.log(`🔧 添加附点标记`);
            } else if (!noteData.dotted && dot) {
                // 不需要附点但有，删除
                noteElement.removeChild(dot);
                console.log(`🔧 删除附点标记`);
            }

            // 注意：不更新stem、staff、beam、slur、tie等元素
            // 这些元素包含重要的音乐连线信息，应该保持原样

        } catch (error) {
            console.error('更新音符元素失败:', error);
        }
    }

    createNoteElement(xmlDoc, noteData) {
        console.log(`🔧 开始创建音符元素: ${noteData.pitch}${noteData.octave}, isChord: ${noteData.isChord}`);
        const noteElement = xmlDoc.createElement('note');

        // 为新增音符设置default-x属性（基于插入位置估算）
        if (noteData.defaultX !== undefined) {
            noteElement.setAttribute('default-x', noteData.defaultX.toString());
            console.log(`🔧 设置default-x: ${noteData.defaultX}`);
        } else {
            // 如果没有defaultX，根据小节和位置估算一个合理的值
            const estimatedX = this.estimateDefaultX(noteData);
            noteElement.setAttribute('default-x', estimatedX.toString());
            console.log(`🔧 估算default-x: ${estimatedX}`);
        }

        // 确保元素属于正确的文档
        console.log(`🔧 noteElement.ownerDocument === xmlDoc: ${noteElement.ownerDocument === xmlDoc}`);
        console.log(`🔧 创建的noteElement:`, noteElement);

        // 添加音高信息（如果不是休止符）
        if (!noteData.isRest) {
            const pitch = xmlDoc.createElement('pitch');

            const step = xmlDoc.createElement('step');
            step.textContent = noteData.pitch;
            pitch.appendChild(step);

            const octave = xmlDoc.createElement('octave');
            octave.textContent = noteData.octave.toString();
            pitch.appendChild(octave);

            noteElement.appendChild(pitch);
            console.log(`🔧 已添加pitch元素: ${noteData.pitch}${noteData.octave}`);
        } else {
            const rest = xmlDoc.createElement('rest');
            noteElement.appendChild(rest);
            console.log(`🔧 已添加rest元素`);
        }

        // 如果是和弦音符（isChord: true），在pitch之后添加chord元素
        if (noteData.isChord === true) {
            const chord = xmlDoc.createElement('chord');
            noteElement.appendChild(chord);
            console.log(`🎵 为和弦音符 ${noteData.pitch}${noteData.octave} 添加 <chord/> 标记`);
        } else {
            console.log(`🎵 单音符 ${noteData.pitch}${noteData.octave} 不添加 <chord/> 标记`);
        }

        // 添加时值 - 使用正确的duration计算
        const duration = xmlDoc.createElement('duration');
        // 从原始XML中获取divisions值，如果没有则使用默认值4
        const divisions = this.getDivisionsFromXML(xmlDoc) || 4;
        // 计算正确的duration值：对于四分音符，duration应该等于divisions
        // noteData.duration=4表示四分音符，应该对应XML中的duration=divisions
        let baseDurationValue = Math.round(divisions * (4 / noteData.duration));
        // 如果是附点音符，需要增加一半时值
        if (noteData.dotted) {
            baseDurationValue = Math.round(baseDurationValue * 1.5); // 附点增加一半时值
        }
        duration.textContent = baseDurationValue.toString();
        noteElement.appendChild(duration);
        console.log(`🔧 设置duration: ${baseDurationValue} (divisions=${divisions}, noteData.duration=${noteData.duration}, dotted=${noteData.dotted})`);

        // 添加声部
        const voice = xmlDoc.createElement('voice');
        voice.textContent = noteData.voice.toString();
        noteElement.appendChild(voice);

        // 添加音符类型
        const type = xmlDoc.createElement('type');
        // 🔧 使用正确的MusicXML type值
        let mappedType;
        if (noteData.duration === 1) {
            mappedType = 'whole';
        } else if (noteData.duration === 2) {
            mappedType = 'half';
        } else if (noteData.duration === 4) {
            mappedType = 'quarter';
        } else if (noteData.duration === 8) {
            mappedType = 'eighth';
        } else if (noteData.duration === 16) {
            mappedType = '16th';       // 🔧 修复：使用正确的MusicXML值
        } else if (noteData.duration === 32) {
            mappedType = '32nd';
        } else if (noteData.duration === 64) {
            mappedType = '64th';
        } else if (noteData.duration === 128) {
            mappedType = '128th';
        } else {
            mappedType = 'quarter';
        }
        type.textContent = mappedType;
        noteElement.appendChild(type);
        console.log(`🔧 设置type: ${type.textContent} (noteData.duration=${noteData.duration})`);

        // 处理附点：只有当noteData.dotted为true时才添加<dot/>元素
        if (noteData.dotted) {
            const dot = xmlDoc.createElement('dot');
            noteElement.appendChild(dot);
            console.log(`🔧 添加附点标记`);
        }

        // 添加符干方向（stem）- 根据标准音乐记谱规则
        if (!noteData.isRest) {
            const stem = xmlDoc.createElement('stem');

            if (noteData.staff === 2) {
                // 低音谱表：以D3为分界线
                // D3及以下向上，D3以上向下
                const isLowPitch = (noteData.octave < 3) || (noteData.octave === 3 && ['C', 'D'].includes(noteData.pitch));
                stem.textContent = isLowPitch ? 'up' : 'down';
                stem.setAttribute('default-y', isLowPitch ? '16' : '-56');
                console.log(`🔧 为低音谱表音符添加stem: ${stem.textContent} (${noteData.pitch}${noteData.octave})`);
            } else {
                // 高音谱表：以B4为分界线（第三线）
                // B4及以下向上，C5及以上向下
                const stemUp = (noteData.octave < 5) || (noteData.octave === 5 && noteData.pitch === 'B');
                stem.textContent = stemUp ? 'up' : 'down';
                stem.setAttribute('default-y', stemUp ? '16' : '-56');
                console.log(`🔧 为高音谱表音符添加stem: ${stem.textContent} (${noteData.pitch}${noteData.octave})`);
            }
            noteElement.appendChild(stem);
            console.log(`🔧 stem元素已添加到noteElement`);
        } else {
            console.log(`🔧 休止符不添加stem元素`);
        }

        // 添加谱表信息
        if (noteData.staff) {
            const staff = xmlDoc.createElement('staff');
            staff.textContent = noteData.staff.toString();
            noteElement.appendChild(staff);
        }

        console.log(`🔧 完成创建音符元素，子元素数量: ${noteElement.children.length}`);
        console.log(`🔧 最终noteElement的innerHTML:`, noteElement.innerHTML);
        return noteElement;
    }

    /**
     * 估算新增音符的default-x位置
     * @param {Object} noteData 音符数据
     * @returns {number} 估算的default-x值
     */
    /**
     * 估算新增音符的default-x位置（增强版，支持并发保护和验证）
     * @param {Object} noteData 音符数据
     * @returns {number} 估算的default-x值
     */
    estimateDefaultX(noteData) {
        console.log(`🔍 开始计算defaultX，音符: ${noteData.pitch}${noteData.octave}, 小节${noteData.measure}, 谱表${noteData.staff}`);

        // 1. 创建当前状态的快照，避免并发修改影响
        const notesSnapshot = [...this.musicData.notesList];

        // 2. 查找同一小节同一谱表的现有音符（排除当前音符，排除defaultX为0的音符）
        const existingNotes = notesSnapshot.filter(n =>
            n.measure === noteData.measure &&
            n.staff === noteData.staff &&
            n.defaultX !== undefined &&
            n.defaultX > 0 &&  // 排除defaultX为0的音符
            n.id !== noteData.id
        );

        console.log(`🔍 找到${existingNotes.length}个有效已有音符:`, existingNotes.map(n => `${n.pitch}${n.octave}(${n.defaultX})`));

        if (existingNotes.length === 0) {
            // 如果没有现有音符，使用默认起始位置
            console.log(`🔍 没有已有音符，使用默认位置: 12`);
            return 12;
        }

        // 3. 按defaultX排序
        existingNotes.sort((a, b) => a.defaultX - b.defaultX);

        // 4. 简化逻辑：直接基于已有音符的defaultX位置计算新位置
        // 不再依赖notesList的顺序，而是基于实际的defaultX坐标
        console.log(`🔍 该谱表共有${existingNotes.length}个有效音符`);

        // 5. 根据已有音符的defaultX分布计算新位置
        let calculatedX;

        if (existingNotes.length === 1) {
            // 只有一个已有音符，在其后添加
            calculatedX = existingNotes[0].defaultX + 40;
            console.log(`🔍 只有一个已有音符，在其后添加: ${existingNotes[0].defaultX} + 40 = ${calculatedX}`);
        } else {
            // 多个已有音符，在最后一个之后添加
            const lastExisting = existingNotes[existingNotes.length - 1];
            calculatedX = lastExisting.defaultX + 40;
            console.log(`🔍 在最后一个音符后添加: ${lastExisting.defaultX} + 40 = ${calculatedX}`);
        }

        // 6. 验证计算结果的合理性
        const validatedX = this.validateDefaultXPosition(calculatedX, existingNotes, noteData);

        if (validatedX !== calculatedX) {
            console.log(`🔧 位置验证修正: ${calculatedX} → ${validatedX}`);
        }

        return validatedX;
    }

    /**
     * 验证defaultX位置的合理性
     * @param {number} calculatedX 计算出的defaultX值
     * @param {Array} existingNotes 已有音符列表
     * @param {Object} noteData 当前音符数据
     * @returns {number} 验证后的defaultX值
     */
    validateDefaultXPosition(calculatedX, existingNotes, noteData) {
        // 1. 确保最小值
        if (calculatedX < 12) {
            console.log(`🔧 defaultX过小，调整为最小值: ${calculatedX} → 12`);
            return 12;
        }

        // 2. 检查是否与现有音符位置冲突（容差5像素）
        const tolerance = 5;
        const conflictNote = existingNotes.find(note =>
            Math.abs(note.defaultX - calculatedX) < tolerance
        );

        if (conflictNote) {
            // 发生冲突，向右偏移
            const adjustedX = conflictNote.defaultX + 15;
            console.log(`🔧 位置冲突，调整: ${calculatedX} → ${adjustedX} (避开${conflictNote.pitch}${conflictNote.octave})`);
            return adjustedX;
        }

        // 3. 检查是否超出合理范围（一般不超过500）
        if (calculatedX > 500) {
            console.log(`🔧 defaultX过大，调整为合理值: ${calculatedX} → 500`);
            return 500;
        }

        return calculatedX;
    }

    /**
     * 获取下一个可用的XML索引
     * @returns {number} 新的XML索引
     */
    getNextXmlIndex() {
        const maxIndex = Math.max(...this.musicData.notesList.map(n => n.xmlIndex || 0));
        return maxIndex + 1;
    }

    /**
     * 修复缺少data-note-id属性的原始音符
     */
    fixMissingNoteIds(xmlDoc, targetNoteIds = null) {
        console.log('🔧 开始修复缺少data-note-id的音符...');
        let fixedCount = 0;

        // 获取所有XML音符
        const allXMLNotes = Array.from(xmlDoc.querySelectorAll('note'));

        // 如果指定了目标音符ID，只处理这些音符
        const notesToProcess = targetNoteIds ?
            this.musicData.notesList.filter(note => targetNoteIds.includes(note.id)) :
            this.musicData.notesList;

        console.log(`🎯 需要处理的音符数量: ${notesToProcess.length}${targetNoteIds ? ` (指定目标: [${targetNoteIds.join(', ')}])` : ' (全部音符)'}`);

        // 为每个需要处理的音符找到对应的XML音符并添加ID
        notesToProcess.forEach(noteData => {
            // 如果已经有data-note-id，跳过
            const existingNote = xmlDoc.querySelector(`note[data-note-id="${noteData.id}"]`);
            if (existingNote) {
                console.log(`⏭️ 音符 ${noteData.pitch}${noteData.octave} (ID=${noteData.id}) 已有data-note-id，跳过`);
                return;
            }

            // 尝试通过音高、八度、小节、谱表匹配找到对应的XML音符
            const matchingXMLNote = allXMLNotes.find(xmlNote => {
                // 跳过已经有data-note-id的音符
                if (xmlNote.hasAttribute('data-note-id')) {
                    return false;
                }

                // 获取XML音符的基本信息
                const xmlPitchInfo = this.getNoteElementPitch(xmlNote);
                const xmlMeasure = parseInt(xmlNote.closest('measure')?.getAttribute('number') || '0');
                const xmlStaff = parseInt(xmlNote.querySelector('staff')?.textContent || '1');

                // 🔧 修复：处理休止符匹配
                if (xmlPitchInfo.step === 'Rest' && noteData.isRest) {
                    // 休止符匹配：小节、谱表、时值相同
                    const xmlDuration = this.getDurationFromXMLNote(xmlNote);
                    return xmlMeasure === noteData.measure &&
                           xmlStaff === noteData.staff &&
                           xmlDuration === noteData.duration;
                }

                // 🔧 修复：如果noteData是休止符但XML不是，或反之，则不匹配
                if (xmlPitchInfo.step === 'Rest' || noteData.isRest) {
                    return false;
                }

                // 匹配条件：音高、小节、谱表相同
                const xmlPitch = `${xmlPitchInfo.step}${xmlPitchInfo.octave}`;
                const notePitch = `${noteData.pitch}${noteData.octave}`;

                return xmlPitch === notePitch &&
                       xmlMeasure === noteData.measure &&
                       xmlStaff === noteData.staff;
            });

            if (matchingXMLNote) {
                matchingXMLNote.setAttribute('data-note-id', noteData.id.toString());
                console.log(`🔧 为音符 ${noteData.pitch}${noteData.octave} (ID=${noteData.id}) 添加data-note-id属性`);
                fixedCount++;
            } else {
                console.log(`⚠️ 未找到匹配的XML音符: ${noteData.pitch}${noteData.octave} (ID=${noteData.id})`);
            }
        });

        console.log(`🔧 修复完成，共为 ${fixedCount} 个音符添加了data-note-id属性`);
    }

    /**
     * 删除XML中的孤儿音符（不存在于notesList中的音符）
     */
    removeOrphanedXMLNotes(xmlDoc) {
        console.log('🗑️ 开始删除XML中的孤儿音符...');

        // 1. 获取notesList中所有音符的ID集合
        const notesListIds = new Set(this.musicData.notesList.map(note => note.id.toString()));
        console.log(`🔍 notesList中的音符ID: [${Array.from(notesListIds).slice(0, 10).join(', ')}...] (共${notesListIds.size}个)`);

        // 2. 精确删除XML中不存在于notesList的音符
        const allXMLNotes = Array.from(xmlDoc.querySelectorAll('note'));
        const xmlNotesToDelete = [];

        console.log(`🔍 XML中找到 ${allXMLNotes.length} 个音符，notesList有 ${notesListIds.size} 个音符`);

        allXMLNotes.forEach((xmlNote, index) => {
            const noteId = xmlNote.getAttribute('data-note-id');

            // 🔧 修复：优先检查有ID的音符（无论类型）
            if (noteId) {
                console.log(`🔍 检查有ID音符: ID=${noteId}, 在notesList中=${notesListIds.has(noteId)}`);
                if (!notesListIds.has(noteId)) {
                    xmlNotesToDelete.push({
                        element: xmlNote,
                        noteId: noteId,
                        index: index
                    });
                    console.log(`🗑️ 标记删除已被删除的音符: ID=${noteId}, 索引=${index}`);
                }
            }
            // 🔧 修复：对于没有ID的音符，使用增强匹配算法
            else {
                console.log(`🔍 检查无ID音符: 索引=${index}`);

                // 🔧 临时修复：跳过休止符的删除检查，避免误删除
                const isRest = xmlNote.querySelector('rest') !== null;
                if (isRest) {
                    console.log(`⏭️ 跳过休止符删除检查: 索引=${index}`);
                } else {
                    const shouldDelete = this.shouldDeleteOriginalNoteWithoutId(xmlNote, notesListIds);
                    if (shouldDelete) {
                        xmlNotesToDelete.push({
                            element: xmlNote,
                            noteId: 'no-id',
                            index: index
                        });
                        console.log(`🗑️ 标记删除无ID的音符: 音高=${this.getNotePitchFromXML(xmlNote)}, 索引=${index}`);
                    }
                }
            }
        });

        // 3. 执行删除操作
        xmlNotesToDelete.forEach((noteInfo, deleteIndex) => {
            if (noteInfo.element.parentNode) {
                noteInfo.element.parentNode.removeChild(noteInfo.element);
                console.log(`🗑️ 删除第 ${deleteIndex + 1}/${xmlNotesToDelete.length} 个孤立的XML音符 (ID: ${noteInfo.noteId})`);
            }
        });

        console.log(`🗑️ 删除了 ${xmlNotesToDelete.length} 个孤立的XML音符`);
    }

    getDivisionsFromXML(xmlDoc) {
        try {
            // 从XML中获取divisions值
            const divisionsElement = xmlDoc.querySelector('divisions');
            if (divisionsElement) {
                const divisions = parseInt(divisionsElement.textContent);
                console.log('从XML获取到divisions值:', divisions);
                return divisions;
            }
            console.log('XML中未找到divisions元素，使用默认值480');
            return 480;
        } catch (error) {
            console.error('获取divisions失败:', error);
            return 480;
        }
    }

    /**
     * 调整小节中的backup时值以保持时值平衡
     * @param {Element} measure 小节元素
     * @param {number} addedDuration 新添加音符的时值
     */
    adjustMeasureBackupDurations(measure, addedDuration) {
        try {
            // 查找小节中的最后一个backup元素
            const backupElements = measure.querySelectorAll('backup');
            if (backupElements.length > 0) {
                const lastBackup = backupElements[backupElements.length - 1];
                const durationElement = lastBackup.querySelector('duration');
                if (durationElement) {
                    const currentDuration = parseInt(durationElement.textContent);
                    const newDuration = currentDuration + addedDuration;
                    durationElement.textContent = newDuration.toString();
                    console.log(`🔧 调整backup时值: ${currentDuration} → ${newDuration} (添加了${addedDuration})`);
                }
            }
        } catch (error) {
            console.error('调整backup时值失败:', error);
        }
    }

    /**
     * 格式化XML字符串，确保正确的换行和缩进
     * @param {string} xmlString 原始XML字符串
     * @returns {string} 格式化后的XML字符串
     */
    formatXML(xmlString) {
        try {
            // 简单的XML格式化：确保note元素有正确的换行
            let formatted = xmlString.replace(
                /<note([^>]*data-added-by-editor[^>]*)>([^<]*(?:<[^/][^>]*>[^<]*<\/[^>]*>[^<]*)*)<\/note>/g,
                '\n      <note$1>$2</note>'
            );

            console.log('🔧 XML格式化完成');
            return formatted;
        } catch (error) {
            console.error('XML格式化失败:', error);
            return xmlString;
        }
    }

    /**
     * 🔧 统一的XML文档管理 - 获取当前工作XML文档
     * @param {boolean} skipLockCheck 是否跳过锁检查（用于安全操作内部调用）
     * @returns {Document} XML文档对象
     */
    getWorkingXmlDoc(skipLockCheck = false) {
        if (!skipLockCheck && this.xmlOperationLock) {
            console.warn('⚠️ XML操作正在进行中，等待完成...');
            return null;
        }

        // 如果已有工作文档且是最新的，直接返回
        if (this.workingXmlDoc) {
            return this.workingXmlDoc;
        }

        // 创建新的工作文档
        const parser = new DOMParser();
        this.workingXmlDoc = parser.parseFromString(this.currentXML, 'application/xml');

        // 验证解析结果
        const parseError = this.workingXmlDoc.getElementsByTagName('parsererror');
        if (parseError.length > 0) {
            console.error('❌ XML解析失败:', parseError[0].textContent);
            this.workingXmlDoc = null;
            return null;
        }

        console.log('✅ 创建新的工作XML文档');
        return this.workingXmlDoc;
    }

    /**
     * 🔧 统一的XML文档管理 - 提交XML文档更改
     * @param {Document} xmlDoc XML文档对象
     * @param {boolean} skipLockCheck 是否跳过锁检查（用于安全操作内部调用）
     * @returns {boolean} 是否成功
     */
    commitXmlChanges(xmlDoc = null, skipLockCheck = false) {
        if (!skipLockCheck && this.xmlOperationLock) {
            console.warn('⚠️ XML操作正在进行中，无法提交更改');
            return false;
        }

        const docToCommit = xmlDoc || this.workingXmlDoc;
        if (!docToCommit) {
            console.error('❌ 没有可提交的XML文档');
            return false;
        }

        try {
            // 序列化XML
            const serializer = new XMLSerializer();
            const newXML = serializer.serializeToString(docToCommit);

            // 验证XML格式
            if (this.validateXMLFormat(newXML)) {
                this.currentXML = newXML;

                // 同步musicData.xmlDoc
                if (this.musicData) {
                    this.musicData.xmlDoc = docToCommit;
                }

                console.log('✅ XML更改已提交');
                return true;
            } else {
                console.error('❌ 生成的XML格式无效，拒绝提交');
                return false;
            }
        } catch (error) {
            console.error('❌ XML提交失败:', error);
            return false;
        }
    }

    /**
     * 🔧 统一的XML文档管理 - 清理工作文档
     */
    clearWorkingXmlDoc() {
        this.workingXmlDoc = null;
        console.log('🧹 工作XML文档已清理');
    }

    /**
     * 🎯 增强音符匹配算法 - 生成音符特征签名
     * @param {Object} noteData 音符数据对象
     * @returns {string} 音符特征签名
     */
    generateNoteSignature(noteData) {
        if (!noteData) return '';

        // 处理休止符
        if (noteData.isRest) {
            return `REST_${noteData.duration}_${noteData.staff}_${noteData.measure}_${Math.round(noteData.defaultX)}`;
        }

        // 处理普通音符
        const pitch = noteData.pitch || '';
        const octave = noteData.octave || 0;
        const duration = noteData.duration || 4;
        const staff = noteData.staff || 1;
        const measure = noteData.measure || 1;
        const voice = noteData.voice || 1;
        const defaultX = Math.round(noteData.defaultX || 0);
        const dotted = noteData.dotted ? 'DOT' : '';

        return `${pitch}${octave}_${duration}${dotted}_S${staff}_M${measure}_V${voice}_X${defaultX}`;
    }

    /**
     * 🎯 增强音符匹配算法 - 从XML元素生成特征签名
     * @param {Element} xmlNote XML音符元素
     * @returns {string} 音符特征签名
     */
    generateXMLNoteSignature(xmlNote) {
        if (!xmlNote) return '';

        // 检查是否是休止符
        const isRest = xmlNote.querySelector('rest') !== null;
        const staff = xmlNote.querySelector('staff')?.textContent || '1';
        const measure = xmlNote.closest('measure')?.getAttribute('number') || '1';
        const voice = xmlNote.querySelector('voice')?.textContent || '1';
        const defaultX = Math.round(parseFloat(xmlNote.getAttribute('default-x') || '0'));

        if (isRest) {
            const duration = this.getDurationFromXMLNote(xmlNote);
            return `REST_${duration}_${staff}_${measure}_${defaultX}`;
        }

        // 处理普通音符
        const pitch = this.getNotePitchFromXML(xmlNote);
        const duration = this.getDurationFromXMLNote(xmlNote);
        const dotted = xmlNote.querySelector('dot') ? 'DOT' : '';

        return `${pitch}_${duration}${dotted}_S${staff}_M${measure}_V${voice}_X${defaultX}`;
    }

    /**
     * 🎯 增强音符匹配算法 - 通过特征签名查找音符
     * @param {Document} xmlDoc XML文档
     * @param {Object} noteData 音符数据
     * @returns {Element|null} 匹配的XML音符元素
     */
    findNoteBySignature(xmlDoc, noteData) {
        const targetSignature = this.generateNoteSignature(noteData);
        console.log(`🔍 查找音符签名: ${targetSignature}`);

        // 遍历所有XML音符，寻找匹配的签名
        const allNotes = xmlDoc.querySelectorAll('note');
        for (const xmlNote of allNotes) {
            const xmlSignature = this.generateXMLNoteSignature(xmlNote);
            if (xmlSignature === targetSignature) {
                console.log(`✅ 找到匹配音符: ${xmlSignature}`);
                return xmlNote;
            }
        }

        console.log(`❌ 未找到匹配音符: ${targetSignature}`);
        return null;
    }

    /**
     * 🎯 增强音符匹配算法 - 模糊匹配（用于容错）
     * @param {Document} xmlDoc XML文档
     * @param {Object} noteData 音符数据
     * @returns {Element|null} 最佳匹配的XML音符元素
     */
    findNoteByFuzzyMatch(xmlDoc, noteData) {
        console.log(`🔍 开始模糊匹配音符: ${noteData.pitch}${noteData.octave}`);

        const allNotes = xmlDoc.querySelectorAll('note');
        let bestMatch = null;
        let bestScore = 0;

        for (const xmlNote of allNotes) {
            const score = this.calculateNoteMatchScore(xmlNote, noteData);
            if (score > bestScore && score >= 0.7) { // 至少70%匹配度
                bestScore = score;
                bestMatch = xmlNote;
            }
        }

        if (bestMatch) {
            console.log(`✅ 模糊匹配成功，匹配度: ${(bestScore * 100).toFixed(1)}%`);
        } else {
            console.log(`❌ 模糊匹配失败，无足够匹配度的音符`);
        }

        return bestMatch;
    }

    /**
     * 🎯 计算音符匹配分数
     * @param {Element} xmlNote XML音符元素
     * @param {Object} noteData 音符数据
     * @returns {number} 匹配分数 (0-1)
     */
    calculateNoteMatchScore(xmlNote, noteData) {
        let score = 0;
        let totalWeight = 0;

        // 音高匹配 (权重: 0.3)
        const xmlPitch = this.getNotePitchFromXML(xmlNote);
        const notePitch = noteData.isRest ? 'REST' : `${noteData.pitch}${noteData.octave}`;
        if (xmlPitch === notePitch) {
            score += 0.3;
        }
        totalWeight += 0.3;

        // 时值匹配 (权重: 0.2)
        const xmlDuration = this.getDurationFromXMLNote(xmlNote);
        if (xmlDuration === noteData.duration) {
            score += 0.2;
        }
        totalWeight += 0.2;

        // 谱表匹配 (权重: 0.2)
        const xmlStaff = parseInt(xmlNote.querySelector('staff')?.textContent || '1');
        if (xmlStaff === noteData.staff) {
            score += 0.2;
        }
        totalWeight += 0.2;

        // 小节匹配 (权重: 0.2)
        const xmlMeasure = parseInt(xmlNote.closest('measure')?.getAttribute('number') || '1');
        if (xmlMeasure === noteData.measure) {
            score += 0.2;
        }
        totalWeight += 0.2;

        // 位置匹配 (权重: 0.1)
        const xmlDefaultX = parseFloat(xmlNote.getAttribute('default-x') || '0');
        const positionDiff = Math.abs(xmlDefaultX - noteData.defaultX);
        if (positionDiff < 5) { // 5个单位内认为位置匹配
            score += 0.1;
        }
        totalWeight += 0.1;

        return totalWeight > 0 ? score / totalWeight : 0;
    }

    /**
     * 🎯 从XML音符元素获取时值
     * @param {Element} xmlNote XML音符元素
     * @returns {number} 时值
     */
    getDurationFromXMLNote(xmlNote) {
        const typeElement = xmlNote.querySelector('type');
        if (!typeElement) return 4; // 默认四分音符

        const type = typeElement.textContent;
        const durationMap = {
            'whole': 1,
            'half': 2,
            'quarter': 4,
            'eighth': 8,
            'sixteenth': 16,  // 🔧 兼容：支持旧格式
            '16th': 16,       // 🔧 标准：MusicXML 4.1标准格式
            '32nd': 32,
            '64th': 64,
            '128th': 128,
            '256th': 256
        };

        return durationMap[type] || 4;
    }

    /**
     * 🔍 数据完整性验证 - 验证notesList与XML的一致性
     * @returns {Object} 验证结果
     */
    validateDataIntegrity() {
        console.log('🔍 开始数据完整性验证...');

        const issues = [];
        const warnings = [];
        let xmlNoteCount = 0;
        let notesListCount = this.musicData?.notesList?.length || 0;

        try {
            // 1. 验证基本数据结构
            if (!this.musicData) {
                issues.push('musicData未初始化');
                return { success: false, issues, warnings };
            }

            if (!this.currentXML) {
                issues.push('currentXML未初始化');
                return { success: false, issues, warnings };
            }

            // 2. 解析XML并统计音符
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(this.currentXML, 'application/xml');
            const parseError = xmlDoc.getElementsByTagName('parsererror');

            if (parseError.length > 0) {
                issues.push('XML解析错误: ' + parseError[0].textContent);
                return { success: false, issues, warnings };
            }

            const allXMLNotes = xmlDoc.querySelectorAll('note');
            xmlNoteCount = allXMLNotes.length;

            // 3. 验证音符数量一致性
            if (xmlNoteCount !== notesListCount) {
                warnings.push(`音符数量不一致: XML=${xmlNoteCount}, notesList=${notesListCount}`);
            }

            // 4. 简单的重复ID检查
            const seenIds = new Set();
            let duplicateCount = 0;

            for (const note of this.musicData.notesList) {
                if (seenIds.has(note.id)) {
                    duplicateCount++;
                } else {
                    seenIds.add(note.id);
                }
            }

            if (duplicateCount > 0) {
                issues.push('发现重复音符ID');
            }

            const success = issues.length === 0;
            return { success, issues, warnings };

        } catch (error) {
            console.error('数据完整性验证失败:', error);
            issues.push('验证过程出错: ' + error.message);
            return { success: false, issues, warnings };
        }
    }



    /**
     * 🔄 事务性操作 - 创建数据快照
     * @returns {Object} 数据快照
     */
    createDataSnapshot() {
        try {
            return {
                timestamp: Date.now(),
                currentXML: this.currentXML,
                musicData: JSON.parse(JSON.stringify(this.musicData || {})),
                connectionData: this.serializeConnectionData(),
                hasUnsavedChanges: this.hasUnsavedChanges,
                workingXmlDoc: this.workingXmlDoc ? this.workingXmlDoc.cloneNode(true) : null
            };
        } catch (error) {
            console.error('创建数据快照失败:', error);
            return null;
        }
    }

    /**
     * 🔄 事务性操作 - 恢复数据快照
     * @param {Object} snapshot 数据快照
     */
    restoreDataSnapshot(snapshot) {
        if (!snapshot) {
            console.error('无效的数据快照');
            return false;
        }

        try {
            console.log(`🔄 恢复数据快照 (${new Date(snapshot.timestamp).toLocaleTimeString()})`);

            this.currentXML = snapshot.currentXML;
            this.musicData = JSON.parse(JSON.stringify(snapshot.musicData));
            this.deserializeConnectionData(snapshot.connectionData);
            this.hasUnsavedChanges = snapshot.hasUnsavedChanges;
            this.workingXmlDoc = snapshot.workingXmlDoc ? snapshot.workingXmlDoc.cloneNode(true) : null;

            // 重新同步musicData.xmlDoc
            if (this.currentXML && this.musicData) {
                const parser = new DOMParser();
                this.musicData.xmlDoc = parser.parseFromString(this.currentXML, 'text/xml');
            }

            console.log('✅ 数据快照恢复成功');
            return true;
        } catch (error) {
            console.error('恢复数据快照失败:', error);
            return false;
        }
    }

    /**
     * 🔄 事务性操作 - 执行事务性XML操作
     * @param {Array} operations 操作数组，每个操作是一个返回Promise的函数
     * @param {string} transactionName 事务名称
     * @returns {Promise<boolean>} 操作是否成功
     */
    async executeTransaction(operations, transactionName = '未命名事务') {
        console.log(`🔄 开始执行事务: ${transactionName}`);

        // 创建事务前快照
        const snapshot = this.createDataSnapshot();
        if (!snapshot) {
            console.error('❌ 无法创建事务快照，事务中止');
            return false;
        }

        // 获取事务锁
        if (!this.acquireXmlLock(`Transaction:${transactionName}`)) {
            console.error('❌ 无法获取事务锁，事务中止');
            return false;
        }

        try {
            // 执行数据完整性验证
            const preValidation = this.validateDataIntegrity();
            if (!preValidation.success) {
                console.warn('⚠️ 事务前数据完整性验证失败:', preValidation.issues);
                // 可以选择继续或中止，这里选择继续但记录警告
            }

            // 依次执行所有操作
            for (let i = 0; i < operations.length; i++) {
                const operation = operations[i];
                console.log(`🔄 执行事务操作 ${i + 1}/${operations.length}`);

                try {
                    await operation();
                } catch (operationError) {
                    console.error(`❌ 事务操作 ${i + 1} 失败:`, operationError);
                    throw new Error(`操作 ${i + 1} 失败: ${operationError.message}`);
                }
            }

            // 执行事务后数据完整性验证
            const postValidation = this.validateDataIntegrity();
            if (!postValidation.success) {
                console.error('❌ 事务后数据完整性验证失败:', postValidation.issues);
                throw new Error('事务后数据完整性验证失败');
            }

            // 提交工作XML文档
            if (this.workingXmlDoc) {
                this.commitXmlChanges(null, true); // 跳过锁检查，因为我们已经在事务内部
            }

            console.log(`✅ 事务执行成功: ${transactionName}`);
            return true;

        } catch (error) {
            console.error(`❌ 事务执行失败: ${transactionName}`, error);

            // 回滚到事务前状态
            console.log('🔄 正在回滚事务...');
            const rollbackSuccess = this.restoreDataSnapshot(snapshot);

            if (rollbackSuccess) {
                console.log('✅ 事务回滚成功');
            } else {
                console.error('❌ 事务回滚失败，数据可能处于不一致状态');
            }

            return false;

        } finally {
            // 释放事务锁
            this.releaseXmlLock(`Transaction:${transactionName}`);
        }
    }

    /**
     * 🔄 事务性操作 - 批量音符操作
     * @param {Array} noteOperations 音符操作数组
     * @param {string} operationName 操作名称
     * @returns {Promise<boolean>} 操作是否成功
     */
    async executeBatchNoteOperation(noteOperations, operationName = '批量音符操作') {
        const operations = noteOperations.map(noteOp => async () => {
            switch (noteOp.type) {
                case 'add':
                    return this.addNoteToXML(noteOp.noteData);
                case 'update':
                    noteOp.noteData.isModified = true;
                    return this.updateXMLWithNoteChanges();
                case 'delete':
                    this.musicData.notesList = this.musicData.notesList.filter(n => n.id !== noteOp.noteId);
                    return this.updateXMLWithNoteChanges();
                case 'beam':
                    return this.addBeamToXML(noteOp.noteIds, noteOp.beamNumber);
                default:
                    throw new Error(`未知的音符操作类型: ${noteOp.type}`);
            }
        });

        return this.executeTransaction(operations, operationName);
    }











    /**
     * 🔧 XML操作锁管理 - 获取锁
     * @param {string} operation 操作名称
     * @returns {boolean} 是否成功获取锁
     */
    acquireXmlLock(operation = 'unknown') {
        if (this.xmlOperationLock) {
            console.warn(`⚠️ XML操作锁已被占用，无法执行操作: ${operation}`);
            return false;
        }

        this.xmlOperationLock = true;
        console.log(`🔒 获取XML操作锁: ${operation}`);
        return true;
    }

    /**
     * 🔧 XML操作锁管理 - 释放锁
     * @param {string} operation 操作名称
     */
    releaseXmlLock(operation = 'unknown') {
        this.xmlOperationLock = false;
        console.log(`🔓 释放XML操作锁: ${operation}`);
    }

    /**
     * 🔧 安全的XML操作包装器
     * @param {string} operation 操作名称
     * @param {Function} xmlOperation XML操作函数
     * @returns {Promise<any>} 操作结果
     */
    async safeXmlOperation(operation, xmlOperation) {
        if (!this.acquireXmlLock(operation)) {
            throw new Error(`无法获取XML操作锁: ${operation}`);
        }

        try {
            const result = await xmlOperation();
            return result;
        } catch (error) {
            console.error(`❌ XML操作失败 [${operation}]:`, error);
            throw error;
        } finally {
            this.releaseXmlLock(operation);
        }
    }

    validateXMLFormat(xmlString) {
        try {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xmlString, 'application/xml');

            // 检查是否有解析错误
            const parseError = xmlDoc.getElementsByTagName('parsererror');
            if (parseError.length > 0) {
                console.error('XML解析错误:', parseError[0].textContent);
                return false;
            }

            // 检查基本的MusicXML结构
            const scorePartwise = xmlDoc.querySelector('score-partwise');
            if (!scorePartwise) {
                console.error('缺少score-partwise根元素');
                return false;
            }

            const partList = xmlDoc.querySelector('part-list');
            if (!partList) {
                console.error('缺少part-list元素');
                return false;
            }

            console.log('XML格式验证通过');
            return true;
        } catch (error) {
            console.error('XML验证失败:', error);
            return false;
        }
    }

    showPreview() {
        console.log('切换到预览模式');
        this.currentMode = 'preview';

        // 更新按钮状态
        document.getElementById('show-preview').className = 'btn btn-primary';
        document.getElementById('show-editor').className = 'btn btn-outline-primary';
        document.getElementById('mode-title').textContent = '乐谱预览';

        // 显示预览区域，隐藏编辑区域
        document.getElementById('preview-area').classList.remove('d-none');
        document.getElementById('editor-area').classList.add('d-none');

        // 如果有修改，重新生成预览
        this.generatePreview();
    }

    showEditor() {
        console.log('切换到编辑模式');
        this.currentMode = 'editor';

        // 更新按钮状态
        document.getElementById('show-preview').className = 'btn btn-outline-primary';
        document.getElementById('show-editor').className = 'btn btn-primary';
        document.getElementById('mode-title').textContent = '音符编辑';

        // 显示编辑区域，隐藏预览区域
        document.getElementById('preview-area').classList.add('d-none');
        document.getElementById('editor-area').classList.remove('d-none');

        // 根据当前编辑模式显示对应区域
        if (this.editMode === 'visual') {
            this.showVisualEdit();
        } else {
            this.showXMLEdit();
        }

        // 确保音符列表已加载
        if (!this.musicData || !this.musicData.notesList) {
            this.loadNotesList();
        }
    }

    showVisualEdit() {
        console.log('切换到可视化编辑');
        this.editMode = 'visual';

        // 更新编辑方式按钮状态
        const visualBtn = document.getElementById('visual-edit-mode');
        const xmlBtn = document.getElementById('xml-edit-mode');
        if (visualBtn && xmlBtn) {
            visualBtn.className = 'btn btn-primary active';
            xmlBtn.className = 'btn btn-outline-primary';
        }

        // 显示可视化编辑区域，隐藏XML编辑区域
        const visualArea = document.getElementById('visual-edit-area');
        const xmlArea = document.getElementById('xml-edit-area');
        if (visualArea && xmlArea) {
            visualArea.classList.remove('d-none');
            xmlArea.classList.add('d-none');
        }

        // 确保音符列表已加载
        if (!this.musicData || !this.musicData.notesList) {
            this.loadNotesList();
        }

        // 如果还没有保存初始状态，现在保存
        if (!this.initialState) {
            this.saveInitialState();
        }
    }

    showXMLEdit() {
        console.log('切换到XML编辑');
        this.editMode = 'xml';

        // 更新编辑方式按钮状态
        const visualBtn = document.getElementById('visual-edit-mode');
        const xmlBtn = document.getElementById('xml-edit-mode');
        if (visualBtn && xmlBtn) {
            visualBtn.className = 'btn btn-outline-primary';
            xmlBtn.className = 'btn btn-primary active';
        }

        // 显示XML编辑区域，隐藏可视化编辑区域
        const visualArea = document.getElementById('visual-edit-area');
        const xmlArea = document.getElementById('xml-edit-area');
        if (visualArea && xmlArea) {
            visualArea.classList.add('d-none');
            xmlArea.classList.remove('d-none');
        }

        // 加载XML内容到编辑器
        this.loadXMLToEditor();
    }

    loadXMLToEditor() {
        const xmlEditor = document.getElementById('xml-editor');
        if (xmlEditor && this.currentXML) {
            xmlEditor.value = this.currentXML;
            console.log('XML内容已加载到编辑器');
        }
    }

    formatXML() {
        const xmlEditor = document.getElementById('xml-editor');
        if (!xmlEditor) return;

        try {
            const xmlContent = xmlEditor.value;
            const formattedXML = this.formatXMLString(xmlContent);
            xmlEditor.value = formattedXML;
            this.showMessage('XML格式化完成', 'success');
        } catch (error) {
            console.error('XML格式化失败:', error);
            this.showMessage('XML格式化失败: ' + error.message, 'danger');
        }
    }

    validateXML() {
        const xmlEditor = document.getElementById('xml-editor');
        if (!xmlEditor) return;

        try {
            const xmlContent = xmlEditor.value;
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xmlContent, 'application/xml');

            const parseError = xmlDoc.getElementsByTagName('parsererror');
            if (parseError.length > 0) {
                throw new Error('XML格式错误');
            }

            this.showMessage('XML验证通过', 'success');
        } catch (error) {
            console.error('XML验证失败:', error);
            this.showMessage('XML验证失败: ' + error.message, 'danger');
        }
    }

    saveXMLChanges() {
        const xmlEditor = document.getElementById('xml-editor');
        if (!xmlEditor) return;

        try {
            const xmlContent = xmlEditor.value;

            // 验证XML格式
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xmlContent, 'application/xml');
            const parseError = xmlDoc.getElementsByTagName('parsererror');
            if (parseError.length > 0) {
                throw new Error('XML格式错误，请先修正后再保存');
            }

            // 保存修改
            this.currentXML = xmlContent;

            // 重新解析音乐数据
            this.musicData = this.parseXMLData(xmlContent);
            this.updateUI();

            this.showMessage('XML修改已保存，正在重新生成预览...', 'info');

            // 自动切换到预览模式并重新生成预览
            setTimeout(() => {
                this.showPreview();
                // 强制重新生成预览
                this.generatePreview(true);
            }, 1500);

        } catch (error) {
            console.error('保存XML失败:', error);
            this.showMessage('保存失败: ' + error.message, 'danger');
        }
    }

    backToUpload() {
        if (confirm('确定要返回上传页面吗？当前的修改将丢失。')) {
            window.location.href = '/upload';
        }
    }

    async previewChanges() {
        await this.generatePreview();
    }

    async proceedToFingering() {
        try {
            const response = await fetch('/api/save-xml-and-generate-fingering', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_id: this.taskId,
                    xml_content: this.currentXML
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                window.location.href = `/fingering-result?task_id=${this.taskId}`;
            } else {
                throw new Error(data.error || '处理失败');
            }
        } catch (error) {
            console.error('处理失败:', error);
            alert('处理失败: ' + error.message);
        }
    }

    handlePreviewClick(event) {
        // 在预览图上点击时的处理
        const rect = event.target.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // 计算相对位置（百分比）
        const relativeX = (x / rect.width) * 100;
        const relativeY = (y / rect.height) * 100;

        // 显示点击位置的提示
        this.showClickHint(event.clientX, event.clientY, relativeX, relativeY);
    }

    showClickHint(screenX, screenY, relativeX, relativeY) {
        // 创建临时提示框
        const hint = document.createElement('div');
        hint.className = 'position-fixed bg-primary text-white p-2 rounded shadow';
        hint.style.cssText = `
            left: ${screenX + 10}px;
            top: ${screenY - 30}px;
            z-index: 9999;
            font-size: 12px;
            pointer-events: none;
        `;
        hint.innerHTML = `
            <div>点击位置: ${relativeX.toFixed(1)}%, ${relativeY.toFixed(1)}%</div>
            <div><small>未来版本将支持直接编辑</small></div>
        `;

        document.body.appendChild(hint);

        // 2秒后移除提示
        setTimeout(() => {
            if (hint.parentNode) {
                hint.parentNode.removeChild(hint);
            }
        }, 2000);

        // 同时显示系统消息
        this.showMessage('点击预览图功能正在开发中，请使用右侧编辑面板', 'info');
    }

    formatXMLString(xml) {
        try {
            // 使用更安全的格式化方法，避免破坏XML内容
            return this.safeXMLFormat(xml);

        } catch (error) {
            // 如果格式化失败，返回原始XML
            console.warn('XML格式化失败:', error);
            return xml;
        }
    }

    safeXMLFormat(xml) {
        // 更安全的XML格式化，保护标签内容不被破坏
        let formatted = '';
        let indent = 0;
        const tab = '  ';

        // 移除多余的空白，但保护标签内的内容
        let cleaned = xml.trim();

        // 使用正则表达式匹配XML标签和内容
        const xmlRegex = /(<[^>]+>)|([^<]+)/g;
        const tokens = [];
        let match;

        while ((match = xmlRegex.exec(cleaned)) !== null) {
            if (match[1]) {
                // 这是一个XML标签
                tokens.push({ type: 'tag', content: match[1] });
            } else if (match[2] && match[2].trim()) {
                // 这是标签间的文本内容
                tokens.push({ type: 'text', content: match[2].trim() });
            }
        }

        for (let i = 0; i < tokens.length; i++) {
            const token = tokens[i];

            if (token.type === 'tag') {
                const tag = token.content;

                if (tag.match(/^<\/\w/)) {
                    // 结束标签
                    indent = Math.max(0, indent - 1);
                    // 检查前一个token是否是文本，如果是则不换行
                    if (i > 0 && tokens[i-1].type === 'text') {
                        formatted += tag;
                    } else {
                        if (formatted && !formatted.endsWith('\n')) {
                            formatted += '\n';
                        }
                        formatted += tab.repeat(indent) + tag;
                    }
                } else if (tag.match(/^<\w[^>]*[^\/]>$/)) {
                    // 开始标签（非自闭合）
                    if (formatted && !formatted.endsWith('\n')) {
                        formatted += '\n';
                    }
                    formatted += tab.repeat(indent) + tag;

                    // 检查下一个token是否是文本
                    if (i + 1 < tokens.length && tokens[i + 1].type === 'text') {
                        // 如果下一个是文本，不换行
                        indent++;
                    } else {
                        // 如果下一个不是文本，换行并增加缩进
                        indent++;
                    }
                } else if (tag.match(/^<\w[^>]*\/>$/)) {
                    // 自闭合标签
                    if (formatted && !formatted.endsWith('\n')) {
                        formatted += '\n';
                    }
                    formatted += tab.repeat(indent) + tag;
                } else if (tag.match(/^<[?!]/)) {
                    // XML声明或注释
                    if (formatted) {
                        formatted += '\n';
                    }
                    formatted += tag;
                }
            } else if (token.type === 'text') {
                // 文本内容，直接添加不换行
                formatted += token.content;
            }
        }

        return formatted.trim();
    }

    updateSaveButtonState() {
        const saveBtn = document.getElementById('save-visual-changes');
        const undoBtn = document.getElementById('undo-all-changes');
        const saveUndoRow = document.getElementById('save-undo-buttons-row');
        const noChangesRow = document.getElementById('no-changes-row');
        const autoFixBtn = document.getElementById('auto-fix');
        const autoFixCol = document.getElementById('auto-fix-col');
        const autoFixColModified = document.getElementById('auto-fix-col-modified');

        const hasChanges = this.hasUnsavedChanges && this.initialState;

        // 控制两个按钮行的显示/隐藏，并移动智能修复按钮
        if (hasChanges) {
            // 有修改：显示三个按钮行，隐藏单个智能修复按钮行
            if (saveUndoRow) saveUndoRow.style.display = 'flex';
            if (noChangesRow) noChangesRow.style.display = 'none';

            // 将智能修复按钮移动到三按钮行
            if (autoFixBtn && autoFixColModified) {
                autoFixColModified.appendChild(autoFixBtn);
                autoFixBtn.className = 'btn btn-info btn-sm w-100'; // 确保样式正确
            }
        } else {
            // 无修改：显示单个智能修复按钮行，隐藏三个按钮行
            if (saveUndoRow) saveUndoRow.style.display = 'none';
            if (noChangesRow) noChangesRow.style.display = 'flex';

            // 将智能修复按钮移动回单按钮行
            if (autoFixBtn && autoFixCol) {
                autoFixCol.appendChild(autoFixBtn);
                autoFixBtn.className = 'btn btn-info btn-sm w-100'; // 确保样式正确
            }
        }

        // 更新保存按钮状态
        if (saveBtn) {
            if (hasChanges) {
                saveBtn.className = 'btn btn-warning btn-sm w-100';
                saveBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>有修改 - 保存';
                saveBtn.disabled = false;
            } else {
                saveBtn.disabled = true;
            }
        }

        // 更新撤销按钮状态
        if (undoBtn) {
            if (hasChanges) {
                undoBtn.disabled = false;
            } else {
                undoBtn.disabled = true;
            }
        }
    }

    /**
     * 保存初始状态（进入可视化编辑器时的状态）
     */
    saveInitialState() {
        console.log('💾 保存初始状态');

        // 确保所有数据都已初始化
        if (!this.musicData) {
            console.log('⚠️ musicData 未初始化，跳过保存初始状态');
            return;
        }

        try {
            // 🔧 确保使用最新的XML内容
            let xmlToSave = this.currentXML || '';

            // 🔧 如果有工作XML文档，先提交更改
            if (this.workingXmlDoc && !this.xmlOperationLock) {
                console.log('🔧 保存初始状态前提交工作XML文档');
                this.commitXmlChanges();
                xmlToSave = this.currentXML;
            }

            this.initialState = {
                xml: xmlToSave,
                musicData: JSON.parse(JSON.stringify(this.musicData || {})), // 深拷贝
                connectionData: this.serializeConnectionData()
            };
            console.log('✅ 初始状态已保存');
        } catch (error) {
            console.error('保存初始状态失败:', error);
            console.log('当前数据状态:', {
                musicData: this.musicData,
                connectionData: this.connectionData
            });
        }
    }

    /**
     * 序列化连线数据（Map转换为普通对象）
     */
    serializeConnectionData() {
        try {
            return {
                ties: Object.fromEntries(this.connectionData.ties || new Map()),
                slurs: Object.fromEntries(this.connectionData.slurs || new Map()),
                beams: Object.fromEntries(this.connectionData.beams || new Map()),
                noteConnections: Object.fromEntries(this.connectionData.noteConnections || new Map())
            };
        } catch (error) {
            console.error('序列化连线数据失败:', error);
            return { ties: {}, slurs: {}, beams: {}, noteConnections: {} };
        }
    }

    /**
     * 反序列化连线数据（普通对象转换为Map）
     */
    deserializeConnectionData(serializedData) {
        try {
            this.connectionData = {
                ties: new Map(Object.entries(serializedData.ties || {})),
                slurs: new Map(Object.entries(serializedData.slurs || {})),
                beams: new Map(Object.entries(serializedData.beams || {})),
                noteConnections: new Map(Object.entries(serializedData.noteConnections || {}))
            };
        } catch (error) {
            console.error('反序列化连线数据失败:', error);
            this.initializeConnectionData();
        }
    }

    /**
     * 撤销所有修改，恢复到初始状态
     */
    undoAllChanges() {
        if (!this.initialState) {
            this.showMessage('没有可撤销的修改', 'warning');
            return;
        }

        if (!confirm('确定要撤销所有修改吗？这将恢复到刚进入编辑器时的状态，所有未保存的修改都将丢失。')) {
            return;
        }

        console.log('🔄 撤销所有修改，恢复初始状态');

        try {
            // 🔧 确保XML操作锁被释放
            this.xmlOperationLock = false;

            // 🔧 清理工作XML文档
            this.clearWorkingXmlDoc();

            // 恢复XML
            this.currentXML = this.initialState.xml || '';

            // 恢复音乐数据
            this.musicData = JSON.parse(JSON.stringify(this.initialState.musicData || {}));

            // 🔧 重新同步musicData.xmlDoc
            if (this.currentXML && this.musicData) {
                const parser = new DOMParser();
                this.musicData.xmlDoc = parser.parseFromString(this.currentXML, 'text/xml');
            }

            // 重新解析XML中的连线信息
            if (this.currentXML) {
                try {
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(this.currentXML, 'text/xml');
                    this.parseConnectionsFromXML(xmlDoc, this.musicData.notesList);
                    console.log('✅ 撤销后重新解析连线信息完成');
                } catch (error) {
                    console.error('重新解析连线信息失败，使用备份数据:', error);
                    // 如果解析失败，使用备份的连线数据
                    this.deserializeConnectionData(this.initialState.connectionData || {});
                }
            } else {
                // 如果没有XML，直接恢复备份的连线数据
                this.deserializeConnectionData(this.initialState.connectionData || {});
            }

            // 清除未保存标记
            this.hasUnsavedChanges = false;

            // 清除待处理数据
            this.pendingSingleNoteData = null;
            this.pendingChordData = null;
            this.currentEditingChord = null;

            // 关闭所有连线模式
            this.tieMode = false;
            this.slurMode = false;
            this.beamMode = false;
            this.selectedNotesForTie = [];
            this.selectedNotesForSlur = [];
            this.selectedNotesForBeam = [];

            // 更新UI
            this.updateUI();
            this.loadNotesList();
            this.updateSaveButtonState();
            this.updateTieButtonState();
            this.updateSlurButtonState();
            this.updateBeamButtonState();

            // 刷新音符显示以确保连线信息正确显示
            this.refreshNotesDisplay();

            this.showMessage('已撤销所有修改，恢复到初始状态', 'success');

        } catch (error) {
            console.error('撤销修改失败:', error);
            this.showMessage('撤销修改失败: ' + error.message, 'danger');
        }
    }

    addNoteToXML(noteData) {
        try {
            console.log(`🎵 添加单个音符到XML: 小节${noteData.measure}, 谱表${noteData.staff}, 音符${noteData.pitch}${noteData.octave}`, noteData);

            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(this.currentXML, 'application/xml');

            // 找到对应的小节和谱表
            const parts = xmlDoc.querySelectorAll('part');
            if (parts.length === 0) {
                console.error('未找到part元素');
                return;
            }

            // 通常第一个part就是钢琴部分
            const part = parts[0];
            const measures = part.querySelectorAll('measure');

            if (measures.length < noteData.measure) {
                console.error(`小节${noteData.measure}不存在，当前只有${measures.length}个小节`);
                return;
            }

            const measure = measures[noteData.measure - 1]; // 小节从1开始计数，数组从0开始

            // 创建note元素并添加标识属性
            const noteElement = this.createNoteElement(xmlDoc, noteData);
            noteElement.setAttribute('data-added-by-editor', 'true'); // 标记为编辑器添加的音符
            noteElement.setAttribute('data-note-id', noteData.id.toString()); // 添加音符ID用于后续识别

            // 将音符插入到小节中（插入到适当位置，按声部和谱表排序）
            this.insertNoteInOrder(xmlDoc, measure, noteElement, noteData);

            // 暂时禁用backup时值调整，先确保基本功能正常
            // const xmlDuration = Math.round(divisions * (4 / noteData.duration));
            // this.adjustMeasureBackupDurations(measure, xmlDuration);

            // 更新XML
            const serializer = new XMLSerializer();
            const newXML = serializer.serializeToString(xmlDoc);

            // 验证XML格式
            if (this.validateXMLFormat(newXML)) {
                this.currentXML = newXML;
                console.log('✅ 单个音符已添加到XML，XML格式验证通过');

                // 🔧 重要修复：标记新添加的音符为已修改，确保在后续同步中不会丢失
                const noteInList = this.musicData.notesList.find(n => n.id === noteData.id);
                if (noteInList) {
                    noteInList.isModified = true;
                    console.log(`🔧 标记新添加的音符 ${noteData.id} 为已修改，防止同步时丢失`);
                } else {
                    console.warn(`⚠️ 在notesList中找不到新添加的音符 ${noteData.id}`);
                }

                // 调试：输出新生成的XML片段
                console.log('🔍 新生成的XML片段（前500字符）:');
                console.log(newXML.substring(0, 500));
            } else {
                console.error('❌ 生成的XML格式无效，回滚更改');
                console.log('🔍 无效的XML内容（前500字符）:');
                console.log(newXML.substring(0, 500));
                throw new Error('生成的XML格式无效');
            }

            // 标记有未保存的修改
            this.hasUnsavedChanges = true;
            this.updateSaveButtonState();

        } catch (error) {
            console.error('添加音符到XML失败:', error);
        }
    }

    insertNoteInOrder(xmlDoc, measure, noteElement, noteData) {
        console.log(`🔍 开始插入音符: ${noteData.pitch}${noteData.octave}, 小节${noteData.measure}, 谱表${noteData.staff}, defaultX=${noteData.defaultX}`);

        // 🔧 修复：直接基于defaultX位置插入，不依赖notesList
        // 获取该谱表的所有现有音符，按defaultX排序
        const allStaffNotesInXML = Array.from(measure.querySelectorAll('note')).filter(xmlNote => {
            const staffElement = xmlNote.querySelector('staff');
            return staffElement && parseInt(staffElement.textContent.trim()) === noteData.staff;
        });

        console.log(`🔍 该谱表现有音符数量: ${allStaffNotesInXML.length}`);

        // 按defaultX排序
        allStaffNotesInXML.sort((a, b) => {
            const aX = parseFloat(a.getAttribute('default-x') || '0');
            const bX = parseFloat(b.getAttribute('default-x') || '0');
            return aX - bX;
        });

        // 打印现有音符的位置信息
        allStaffNotesInXML.forEach((xmlNote, index) => {
            const xmlDefaultX = parseFloat(xmlNote.getAttribute('default-x') || '0');
            const pitchElement = xmlNote.querySelector('pitch');
            const pitch = pitchElement ? pitchElement.querySelector('step')?.textContent : 'REST';
            const octave = pitchElement ? pitchElement.querySelector('octave')?.textContent : '';
            console.log(`  现有音符${index}: ${pitch}${octave} (defaultX: ${xmlDefaultX})`);
        });

        // 找到第一个defaultX大于目标位置的音符
        let insertBeforeElement = null;
        const targetDefaultX = parseFloat(noteData.defaultX);
        console.log(`🔍 目标插入位置 defaultX: ${targetDefaultX}`);

        for (const xmlNote of allStaffNotesInXML) {
            const xmlDefaultX = parseFloat(xmlNote.getAttribute('default-x') || '0');
            if (xmlDefaultX > targetDefaultX) {
                insertBeforeElement = xmlNote;
                const pitchElement = xmlNote.querySelector('pitch');
                const pitch = pitchElement ? pitchElement.querySelector('step')?.textContent : 'REST';
                const octave = pitchElement ? pitchElement.querySelector('octave')?.textContent : '';
                console.log(`🔍 找到插入参考点: ${pitch}${octave} (defaultX: ${xmlDefaultX} > ${targetDefaultX})`);
                break;
            }
        }

        if (insertBeforeElement) {
            // 在找到的元素之前插入
            const indentText = xmlDoc.createTextNode('\n      ');
            insertBeforeElement.parentNode.insertBefore(indentText, insertBeforeElement);
            insertBeforeElement.parentNode.insertBefore(noteElement, insertBeforeElement);
            console.log(`✅ 音符已插入到指定位置 (defaultX: ${targetDefaultX})`);
        } else {
            // 没找到后续音符，插入到该谱表的末尾
            if (allStaffNotesInXML.length > 0) {
                // 在该谱表最后一个音符之后插入
                const lastNoteInStaff = allStaffNotesInXML[allStaffNotesInXML.length - 1];
                const indentText = xmlDoc.createTextNode('\n      ');
                lastNoteInStaff.parentNode.insertBefore(indentText, lastNoteInStaff.nextSibling);
                lastNoteInStaff.parentNode.insertBefore(noteElement, lastNoteInStaff.nextSibling);
                console.log(`✅ 音符已插入到该谱表末尾 (defaultX: ${targetDefaultX})`);
            } else {
                // 该谱表没有音符，添加到小节末尾
                const indentText = xmlDoc.createTextNode('\n      ');
                measure.appendChild(indentText);
                measure.appendChild(noteElement);
                console.log(`✅ 音符已添加到小节末尾（该谱表无现有音符）`);
            }
        }
    }

    async saveVisualChanges() {
        if (!this.hasUnsavedChanges) {
            this.showMessage('没有需要保存的修改', 'info');
            return;
        }

        this.showMessage('正在保存修改到服务器...', 'info');

        // 🔄 使用事务性操作来保存修改
        const saveSuccess = await this.executeTransaction([
            // 操作1: 数据完整性验证
            async () => {
                console.log('🔍 执行保存前数据完整性验证...');
                const validation = this.validateDataIntegrity();
                if (!validation.success) {
                    console.warn('⚠️ 数据完整性验证发现问题:', validation.issues);
                    // 可以选择继续或中止，这里选择记录警告但继续
                }
            },

            // 操作2: 同步XML
            async () => {
                console.log('🔄 同步XML数据...');
                await this.updateXMLWithNoteChangesInternal();
                console.log('✅ XML同步完成');
            }
        ], '保存可视化修改');

        if (!saveSuccess) {
            this.showMessage('保存失败：事务执行失败', 'danger');
            return;
        }

        try {
            // 2. 发送保存请求到服务器
            const response = await fetch('/api/save-visual-changes', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_id: this.taskId,
                    xml_content: this.currentXML
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            console.log('🔄 服务器保存响应:', result);

            if (!result.success) {
                console.error('❌ 服务器保存失败:', result.error);
                throw new Error(result.error || '保存失败');
            }

            this.showMessage('修改已保存，正在生成预览...', 'success');

            // 2. 重新解析音乐数据（以防有XML直接修改）
            this.musicData = this.parseXMLData(this.currentXML);
            this.updateUI();

            // 3. 清除未保存标记
            this.hasUnsavedChanges = false;
            this.updateSaveButtonState();

            // 3.5. 更新初始状态为当前保存的状态
            this.saveInitialState();

            // 4. 切换到预览模式并重新生成预览
            setTimeout(() => {
                this.showPreview();
                // 强制重新生成预览
                this.generatePreview(true);
            }, 1000);

        } catch (error) {
            console.error('保存修改失败:', error);
            this.showMessage('保存失败: ' + error.message, 'danger');
        }
    }

    showMessage(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }

    // ==================== 连线编辑功能 ====================

    /**
     * 切换连音线编辑模式
     */
    toggleTieMode() {
        this.tieMode = !this.tieMode;
        this.slurMode = false; // 关闭连奏线模式
        this.beamMode = false; // 关闭连音符模式

        // 清空选择
        this.selectedNotesForTie = [];
        this.selectedNotesForSlur = [];
        this.selectedNotesForBeam = [];

        // 更新按钮状态
        this.updateTieButtonState();

        if (this.tieMode) {
            this.showMessage('连音线模式已开启，请依次点击要连接的音符（相同音高）', 'info');
        } else {
            this.showMessage('连音线模式已关闭', 'secondary');
        }

        console.log('连音线模式:', this.tieMode);
    }

    /**
     * 切换连奏线编辑模式
     */
    toggleSlurMode() {
        this.slurMode = !this.slurMode;
        this.tieMode = false; // 关闭连音线模式
        this.beamMode = false; // 关闭连音符模式

        // 清空选择
        this.selectedNotesForTie = [];
        this.selectedNotesForSlur = [];
        this.selectedNotesForBeam = [];

        // 更新按钮状态
        this.updateSlurButtonState();

        if (this.slurMode) {
            this.showMessage('连奏线模式已开启，请依次点击要连接的音符', 'info');
        } else {
            this.showMessage('连奏线模式已关闭', 'secondary');
        }

        console.log('连奏线模式:', this.slurMode);
    }

    /**
     * 切换连音符编辑模式
     */
    toggleBeamMode() {
        this.beamMode = !this.beamMode;
        this.tieMode = false; // 关闭连音线模式
        this.slurMode = false; // 关闭连奏线模式

        // 清空选择
        this.selectedNotesForTie = [];
        this.selectedNotesForSlur = [];
        this.selectedNotesForBeam = [];

        // 更新按钮状态
        this.updateBeamButtonState();

        if (this.beamMode) {
            this.showMessage('连音符模式已开启，请依次点击要连接的八分音符、十六分音符或和弦', 'info');
        } else {
            this.showMessage('连音符模式已关闭', 'secondary');
        }

        console.log('连音符模式:', this.beamMode);
    }

    // ==================== 连线数据管理方法 ====================

    /**
     * 初始化连线数据结构
     */
    initializeConnectionData() {
        this.connectionData = {
            ties: new Map(),
            slurs: new Map(),
            beams: new Map(),
            noteConnections: new Map()
        };
    }

    /**
     * 添加连音线数据
     */
    addTieConnection(startNoteId, endNoteId) {
        // 添加连音线映射
        this.connectionData.ties.set(startNoteId, {partnerId: endNoteId, type: 'start'});
        this.connectionData.ties.set(endNoteId, {partnerId: startNoteId, type: 'stop'});

        // 更新音符连接信息
        this.addNoteConnection(startNoteId, 'ties', {partnerId: endNoteId, type: 'start'});
        this.addNoteConnection(endNoteId, 'ties', {partnerId: startNoteId, type: 'stop'});

        console.log('🔗 添加连音线数据:', startNoteId, '→', endNoteId);
    }

    /**
     * 添加连奏线数据
     */
    addSlurConnection(noteIds, slurNumber) {
        const slurId = `slur_${slurNumber}`;
        this.connectionData.slurs.set(slurId, {noteIds: noteIds, number: slurNumber});

        // 更新每个音符的连接信息
        noteIds.forEach((noteId, index) => {
            let type;
            if (index === 0) type = 'start';
            else if (index === noteIds.length - 1) type = 'stop';
            else type = 'continue';

            this.addNoteConnection(noteId, 'slurs', {slurId, number: slurNumber, type, position: index + 1, total: noteIds.length});
        });

        console.log('🌊 添加连奏线数据:', slurId, noteIds);
    }

    /**
     * 添加连音符数据
     */
    addBeamConnection(noteIds, beamNumber) {
        const beamId = `beam_${beamNumber}`;
        this.connectionData.beams.set(beamId, {noteIds: noteIds, number: beamNumber});

        // 获取所有唯一的音符ID - 这些音符都应该显示连音符标记
        const uniqueNoteIds = [...new Set(noteIds)];

        // 为每个唯一的音符添加连接信息
        uniqueNoteIds.forEach((noteId, index) => {
            this.addNoteConnection(noteId, 'beams', {
                beamId,
                number: beamNumber,
                type: 'participant', // 简化类型，表示参与连音符
                position: index + 1,
                total: uniqueNoteIds.length
            });
        });

        console.log('📊 添加连音符数据:', beamId, '原始序列:', noteIds, '参与音符:', uniqueNoteIds);
    }

    /**
     * 添加音符连接信息
     */
    addNoteConnection(noteId, connectionType, connectionInfo) {
        if (!this.connectionData.noteConnections.has(noteId)) {
            this.connectionData.noteConnections.set(noteId, {ties: [], slurs: [], beams: []});
        }

        const noteConnections = this.connectionData.noteConnections.get(noteId);
        noteConnections[connectionType].push(connectionInfo);
    }

    /**
     * 获取音符的连线信息
     */
    getNoteConnections(noteId) {
        return this.connectionData.noteConnections.get(noteId) || {ties: [], slurs: [], beams: []};
    }

    /**
     * 清除所有连线数据
     */
    clearAllConnectionData() {
        this.connectionData.ties.clear();
        this.connectionData.slurs.clear();
        this.connectionData.beams.clear();
        this.connectionData.noteConnections.clear();
        console.log('🧹 已清除所有连线数据');
    }

    /**
     * 删除指定音符的连音线连接
     */
    removeTieConnections(noteId) {
        const connections = this.connectionData.noteConnections.get(noteId);
        if (!connections) return;

        // 删除该音符的所有连音线连接
        connections.ties.forEach(tie => {
            // 同时删除伙伴音符的连接
            const partnerConnections = this.connectionData.noteConnections.get(tie.partnerId);
            if (partnerConnections) {
                partnerConnections.ties = partnerConnections.ties.filter(t => t.partnerId !== noteId);
            }
        });

        // 清空该音符的连音线连接
        connections.ties = [];
    }

    /**
     * 删除指定音符的连奏线连接
     */
    removeSlurConnections(noteId) {
        const connections = this.connectionData.noteConnections.get(noteId);
        if (!connections) return;

        // 删除该音符的所有连奏线连接
        connections.slurs.forEach(slur => {
            // 从全局连奏线映射中删除
            const slurNotes = this.connectionData.slurConnections.get(slur.number);
            if (slurNotes) {
                const updatedNotes = slurNotes.filter(id => id !== noteId);
                if (updatedNotes.length < 2) {
                    // 如果连奏线只剩下一个音符，删除整个连奏线
                    this.connectionData.slurConnections.delete(slur.number);
                    // 清除其他音符的相关连接
                    updatedNotes.forEach(id => {
                        const otherConnections = this.connectionData.noteConnections.get(id);
                        if (otherConnections) {
                            otherConnections.slurs = otherConnections.slurs.filter(s => s.number !== slur.number);
                        }
                    });
                } else {
                    this.connectionData.slurConnections.set(slur.number, updatedNotes);
                    // 更新其他音符的位置信息
                    updatedNotes.forEach((id, index) => {
                        const otherConnections = this.connectionData.noteConnections.get(id);
                        if (otherConnections) {
                            const slurConnection = otherConnections.slurs.find(s => s.number === slur.number);
                            if (slurConnection) {
                                slurConnection.position = index + 1;
                                slurConnection.total = updatedNotes.length;
                            }
                        }
                    });
                }
            }
        });

        // 清空该音符的连奏线连接
        connections.slurs = [];
    }

    /**
     * 删除指定音符的连音符连接
     */
    removeBeamConnections(noteId) {
        const connections = this.connectionData.noteConnections.get(noteId);
        if (!connections) return;

        // 删除该音符的所有连音符连接
        connections.beams.forEach(beam => {
            // 从全局连音符映射中删除
            const beamNotes = this.connectionData.beams.get(beam.number);
            if (beamNotes) {
                const updatedNotes = beamNotes.filter(id => id !== noteId);
                if (updatedNotes.length < 2) {
                    // 如果连音符只剩下一个音符，删除整个连音符
                    this.connectionData.beams.delete(beam.number);
                    // 清除其他音符的相关连接
                    updatedNotes.forEach(id => {
                        const otherConnections = this.connectionData.noteConnections.get(id);
                        if (otherConnections) {
                            otherConnections.beams = otherConnections.beams.filter(b => b.number !== beam.number);
                        }
                    });
                } else {
                    this.connectionData.beams.set(beam.number, updatedNotes);
                    // 更新其他音符的位置信息
                    updatedNotes.forEach((id, index) => {
                        const otherConnections = this.connectionData.noteConnections.get(id);
                        if (otherConnections) {
                            const beamConnection = otherConnections.beams.find(b => b.number === beam.number);
                            if (beamConnection) {
                                beamConnection.position = index + 1;
                                beamConnection.total = updatedNotes.length;
                            }
                        }
                    });
                }
            }
        });

        // 清空该音符的连音符连接
        connections.beams = [];
    }

    /**
     * 从XML解析连线信息
     */
    parseConnectionsFromXML(xmlDoc, notesList) {
        try {
            console.log('🔍 开始从XML解析连线信息...');

            // 清除现有连线数据
            this.clearAllConnectionData();

            // 解析连音线 (ties)
            this.parseTiesFromXML(xmlDoc, notesList);

            // 解析连奏线 (slurs)
            this.parseSlursFromXML(xmlDoc, notesList);

            // 解析连音符 (beams)
            this.parseBeamsFromXML(xmlDoc, notesList);

            // 输出连线解析结果统计
            const tieCount = this.connectionData.ties.size;
            const slurCount = this.connectionData.slurs.size;
            const beamCount = this.connectionData.beams.size;
            const noteConnectionCount = this.connectionData.noteConnections.size;

            console.log('✅ 连线信息解析完成，统计结果:');
            console.log(`   连音线: ${tieCount}个`);
            console.log(`   连奏线: ${slurCount}个`);
            console.log(`   连音符: ${beamCount}个`);
            console.log(`   有连线的音符: ${noteConnectionCount}个`);

            if (noteConnectionCount > 0) {
                console.log('🔗 连线详情:');
                this.connectionData.noteConnections.forEach((connections, noteId) => {
                    if (connections.ties.length > 0 || connections.slurs.length > 0 || connections.beams.length > 0) {
                        console.log(`   音符${noteId}: 连音线${connections.ties.length}个, 连奏线${connections.slurs.length}个, 连音符${connections.beams.length}个`);
                    }
                });
            }
        } catch (error) {
            console.error('解析连线信息失败:', error);
        }
    }

    /**
     * 从XML解析连音线信息
     */
    parseTiesFromXML(xmlDoc, notesList) {
        const ties = xmlDoc.querySelectorAll('tie');
        console.log(`🔍 XML中找到 ${ties.length} 个连音线元素`);

        if (ties.length === 0) {
            console.log('⚠️ XML中没有找到连音线元素，可能原因：');
            console.log('   1. 乐谱中确实没有连音线');
            console.log('   2. Audiveris识别时丢失了连音线信息');
            console.log('   3. XML结构与预期不符');
            return;
        }

        // 首先收集所有连音线信息，按照在XML中的顺序
        const tieElements = [];
        ties.forEach((tie, index) => {
            const type = tie.getAttribute('type');
            const noteElement = tie.closest('note');

            if (!noteElement) {
                console.warn(`❌ 连音线${index + 1}没有找到对应的note元素`);
                return;
            }

            // 获取更详细的位置信息用于精确匹配
            const measureElement = noteElement.closest('measure');
            const measureNumber = parseInt(measureElement?.getAttribute('number') || '1');
            const staff = parseInt(noteElement.querySelector('staff')?.textContent || '1');
            const voice = parseInt(noteElement.querySelector('voice')?.textContent || '1');
            const pitch = this.getNoteElementPitch(noteElement);

            // 获取default-x位置信息，这是更精确的位置标识
            const defaultX = parseFloat(noteElement.getAttribute('default-x') || '0');

            // 计算在小节内的位置（基于duration累计）
            let positionInMeasure = 0;
            const notesInMeasure = Array.from(measureElement.querySelectorAll('note'));
            const noteIndex = notesInMeasure.indexOf(noteElement);

            tieElements.push({
                index: index + 1,
                type,
                noteElement,
                measureNumber,
                staff,
                voice,
                pitch,
                positionInMeasure: noteIndex,
                defaultX: defaultX,
                pitchKey: `${pitch.step}${pitch.octave}`
            });
        });

        console.log(`📋 收集到${tieElements.length}个连音线元素，开始精确匹配...`);

        const tieMap = new Map(); // 用于匹配start和stop
        let successfulMatches = 0;
        let failedMatches = 0;

        tieElements.forEach(tieInfo => {
            console.log(`🔍 处理第${tieInfo.index}个连音线，类型: ${tieInfo.type}, 位置: 小节${tieInfo.measureNumber}, 谱表${tieInfo.staff}, 声部${tieInfo.voice}, 音高${tieInfo.pitchKey}`);

            // 使用改进的匹配算法
            const noteId = this.findNoteIdByElementImproved(tieInfo, notesList);
            if (!noteId) {
                console.warn(`❌ 连音线${tieInfo.index}无法匹配到音符ID`);
                failedMatches++;
                return;
            }

            console.log(`✅ 连音线${tieInfo.index}成功匹配到音符ID: ${noteId}`);

            if (tieInfo.type === 'start') {
                // 创建更精确的键值，包含位置信息
                const tieKey = `${tieInfo.pitchKey}_${tieInfo.measureNumber}_${tieInfo.staff}_${tieInfo.voice}`;
                tieMap.set(tieKey, {
                    noteId,
                    measureNumber: tieInfo.measureNumber,
                    positionInMeasure: tieInfo.positionInMeasure
                });
                console.log(`🎵 记录连音线开始: ${tieKey} -> ${noteId}`);
            } else if (tieInfo.type === 'stop') {
                // 寻找对应的开始音符，优先匹配相同声部和谱表的
                // 首先尝试同小节匹配
                const exactKey = `${tieInfo.pitchKey}_${tieInfo.measureNumber}_${tieInfo.staff}_${tieInfo.voice}`;
                let startInfo = tieMap.get(exactKey);

                if (!startInfo) {
                    // 如果同小节匹配失败，尝试匹配相同音高但可能在前一小节的
                    console.log(`🔍 同小节匹配失败，尝试跨小节匹配...`);
                    for (let [key, info] of tieMap.entries()) {
                        if (key.startsWith(tieInfo.pitchKey) &&
                            info.measureNumber <= tieInfo.measureNumber &&
                            key.includes(`_${tieInfo.staff}_${tieInfo.voice}`)) {
                            startInfo = info;
                            tieMap.delete(key);
                            console.log(`🔍 找到跨小节连音线匹配: ${key}`);
                            break;
                        }
                    }
                }

                // 如果还是没找到，尝试更宽松的匹配（只匹配音高和声部）
                if (!startInfo) {
                    console.log(`🔍 跨小节匹配也失败，尝试宽松匹配...`);
                    for (let [key, info] of tieMap.entries()) {
                        if (key.startsWith(tieInfo.pitchKey) &&
                            key.includes(`_${tieInfo.staff}_${tieInfo.voice}`)) {
                            startInfo = info;
                            tieMap.delete(key);
                            console.log(`🔍 找到宽松匹配: ${key}`);
                            break;
                        }
                    }
                }

                if (startInfo) {
                    // 避免自连接
                    if (startInfo.noteId !== noteId) {
                        this.addTieConnection(startInfo.noteId, noteId);
                        successfulMatches++;
                        console.log(`🔗 成功创建连音线: ${startInfo.noteId} -> ${noteId} (${tieInfo.pitchKey})`);
                    } else {
                        console.warn(`⚠️ 跳过自连接: ${noteId} -> ${noteId}`);
                        failedMatches++;
                    }
                    tieMap.delete(exactKey);
                } else {
                    console.warn(`❌ 连音线结束音符${noteId}找不到对应的开始音符 (${tieInfo.pitchKey})`);
                    failedMatches++;
                }
            }
        });

        console.log(`📊 连音线解析结果: 成功${successfulMatches}个, 失败${failedMatches}个`);

        // 检查是否有未完成的连音线
        if (tieMap.size > 0) {
            console.warn(`⚠️ 有${tieMap.size}个连音线开始音符没有找到对应的结束音符:`);
            tieMap.forEach((info, key) => {
                console.warn(`   ${key}: ${info.noteId}`);
            });
        }
    }

    /**
     * 从XML解析连奏线信息
     */
    parseSlursFromXML(xmlDoc, notesList) {
        const slurs = xmlDoc.querySelectorAll('slur');
        console.log(`🌊 XML中找到 ${slurs.length} 个连奏线元素`);

        if (slurs.length === 0) {
            console.log('⚠️ XML中没有找到连奏线元素');
            return;
        }

        const slurMap = new Map(); // uniqueKey -> {notes: [], type: 'start'|'stop', number: number}
        let slurCounter = 1; // 用于生成唯一的连奏线编号

        slurs.forEach((slur, index) => {
            const type = slur.getAttribute('type');
            const number = parseInt(slur.getAttribute('number') || '1');
            const noteElement = slur.closest('note');

            console.log(`🌊 处理第${index + 1}个连奏线，类型: ${type}, 编号: ${number}`);

            if (!noteElement) {
                console.warn(`❌ 连奏线${index + 1}没有找到对应的note元素`);
                return;
            }

            // 使用改进的音符匹配算法
            const noteId = this.findNoteIdByElementForSlur(noteElement, notesList);
            if (!noteId) {
                console.warn(`❌ 连奏线${index + 1}无法匹配到音符ID`);
                return;
            }

            console.log(`✅ 连奏线${index + 1}成功匹配到音符ID: ${noteId}`);

            if (type === 'start') {
                // 为新的连奏线创建唯一键
                const uniqueKey = `slur_${number}_${index}`;
                slurMap.set(uniqueKey, {
                    notes: [noteId],
                    type: 'start',
                    number: number,
                    actualSlurNumber: slurCounter
                });
                console.log(`🌊 记录连奏线开始: 编号${number} (实际#${slurCounter}) -> ${noteId}`);
            } else if (type === 'stop') {
                // 查找匹配的开始连奏线
                let matchedKey = null;
                for (const [key, data] of slurMap.entries()) {
                    if (data.number === number && data.type === 'start') {
                        matchedKey = key;
                        break;
                    }
                }

                if (matchedKey) {
                    const slurData = slurMap.get(matchedKey);
                    slurData.notes.push(noteId);

                    // 连奏线完成，添加连接
                    console.log(`🌊 连奏线完成: 编号${number} (实际#${slurData.actualSlurNumber}), 连接音符: [${slurData.notes.join(' -> ')}]`);
                    this.addSlurConnection(slurData.notes, slurData.actualSlurNumber);
                    slurMap.delete(matchedKey);
                    slurCounter++;
                } else {
                    console.warn(`❌ 找不到编号${number}的连奏线开始标记`);
                }
            }
        });

        // 检查是否有未完成的连奏线
        if (slurMap.size > 0) {
            console.warn(`⚠️ 有${slurMap.size}个连奏线没有找到结束标记:`);
            slurMap.forEach((data, number) => {
                console.warn(`   编号${number}: [${data.notes.join(', ')}]`);
            });
        }
    }

    /**
     * 从XML解析连音符信息
     */
    parseBeamsFromXML(xmlDoc, notesList) {
        // 处理所有beam元素，不限制number属性
        // 根据MusicXML标准，beam的连接关系由begin/continue/end决定，而不是number
        const allBeams = xmlDoc.querySelectorAll('beam');
        const notesWithBeams = [];

        // 收集所有包含beam的note元素，避免重复
        const processedNotes = new Set();
        allBeams.forEach(beam => {
            const noteElement = beam.closest('note');
            if (noteElement && !processedNotes.has(noteElement)) {
                notesWithBeams.push(noteElement);
                processedNotes.add(noteElement);
            }
        });

        // 只处理beam number="1"，这是主要的连音符层级
        // 高层级的beam（2, 3等）只是用于更细分的连音符，不需要单独创建连音符组
        const beamInfos = [];

        // 收集所有beam number="1"的信息
        notesWithBeams.forEach(noteElement => {
            const beam = noteElement.querySelector('beam[number="1"]');
            if (beam) {
                const type = beam.textContent;
                beamInfos.push({
                    noteElement,
                    type,
                    number: 1
                });
            }
        });

        console.log('🎵 开始解析连音符，找到', notesWithBeams.length, '个有beam的音符');
        console.log('🎵 处理beam number="1"，包含', beamInfos.length, '个beam元素');

        const beamSequences = []; // 存储连音符序列
        let currentSequence = null;

        // 按顺序处理beam
        beamInfos.forEach((beamInfo, index) => {
            const { noteElement, type } = beamInfo;

            // 获取音符特征用于生成唯一键
            const pitch = this.getNoteElementPitch(noteElement);
            const measure = parseInt(noteElement.closest('measure')?.getAttribute('number') || '1');
            const staff = parseInt(noteElement.querySelector('staff')?.textContent || '1');
            const voice = parseInt(noteElement.querySelector('voice')?.textContent || '1');

            // 直接通过XML元素的位置找到对应的noteId
            const allNotes = xmlDoc.querySelectorAll('note');
            let xmlNoteIndex = -1;
            for (let i = 0; i < allNotes.length; i++) {
                if (allNotes[i] === noteElement) {
                    xmlNoteIndex = i;
                    break;
                }
            }

            if (xmlNoteIndex === -1) {
                console.warn('⚠️ 无法找到音符在XML中的位置');
                return;
            }

            // 使用XML索引直接对应notesList中的音符
            if (xmlNoteIndex >= notesList.length) {
                console.warn(`⚠️ XML索引${xmlNoteIndex}超出notesList范围${notesList.length}`);
                return;
            }

            const noteId = notesList[xmlNoteIndex].id;
            console.log(`🎵 处理beam: ${pitch.step}${pitch.octave} 小节${measure} ${type} -> noteId: ${noteId}`);

            // 验证音符匹配是否正确
            const noteData = notesList[xmlNoteIndex];
            if (noteData.pitch !== pitch.step || noteData.octave !== pitch.octave) {
                console.warn(`⚠️ 音符匹配不一致: XML=${pitch.step}${pitch.octave}, notesList=${noteData.pitch}${noteData.octave}`);
                return;
            }

            if (noteId === null || noteId === undefined) {
                console.warn('⚠️ 无法找到对应的音符ID');
                return;
            }

            // 根据beam类型处理序列
            if (type === 'begin') {
                // 如果已经有序列在进行中，说明这是和弦中的多个begin
                if (currentSequence) {
                    // 将新的noteId添加到当前序列中
                    currentSequence.noteIds.push(noteId);
                    currentSequence.types.push(type);
                } else {
                    // 开始新的连音符序列
                    currentSequence = {
                        noteIds: [noteId],
                        types: [type],
                        beamNumber: 1
                    };
                }
            } else if (type === 'continue' && currentSequence) {
                // 继续当前序列
                currentSequence.noteIds.push(noteId);
                currentSequence.types.push(type);
            } else if (type === 'end' && currentSequence) {
                // 添加end音符到当前序列
                currentSequence.noteIds.push(noteId);
                currentSequence.types.push(type);

                // 检查下一个音符是否也是同一个和弦中的end音符
                let shouldEndSequence = true;
                const currentDefaultX = noteElement.getAttribute('default-x');

                // 查找下一个有相同beam层级的音符
                for (let i = index + 1; i < beamInfos.length; i++) {
                    const nextBeamInfo = beamInfos[i];
                    const nextDefaultX = nextBeamInfo.noteElement.getAttribute('default-x');

                    // 如果下一个音符在同一个位置（同一个和弦）
                    if (nextDefaultX === currentDefaultX && nextBeamInfo.type === 'end') {
                        shouldEndSequence = false; // 还有同和弦的end音符，不要结束序列
                        break;
                    } else if (nextDefaultX !== currentDefaultX) {
                        // 不同位置的音符，停止检查
                        break;
                    }
                }

                if (shouldEndSequence) {
                    // 完成序列，添加到列表中
                    if (currentSequence.noteIds.length >= 2) {
                        beamSequences.push(currentSequence);
                    }
                    currentSequence = null;
                }
            }
        });

        // 调试：显示所有beam序列
        console.log('🎵 所有beam序列:', beamSequences.map(seq => ({
            noteIds: seq.noteIds,
            types: seq.types,
            beamNumber: seq.beamNumber
        })));

        // 创建连音符连接 - 确保编号从1开始
        beamSequences.forEach((sequence, index) => {
            const beamNumber = index + 1;
            console.log(`🎵 处理beam序列 ${beamNumber}:`, sequence.noteIds, sequence.types);
            // 不要去重，保持原始序列中的所有音符
            const allNoteIds = sequence.noteIds;
            this.addBeamConnection(allNoteIds, beamNumber);
            console.log('✅ 创建连音符组:', beamNumber, '音符IDs:', allNoteIds);
        });

        console.log(`🎵 总共创建了 ${beamSequences.length} 个连音符组`);
    }

    /**
     * 根据XML元素查找对应的音符ID（带索引支持）
     */
    findNoteIdByElementWithIndex(noteElement, notesList, matchIndex = 0) {
        // 首先尝试通过data-note-id属性
        const dataId = noteElement.getAttribute('data-note-id');
        if (dataId) return dataId;

        // 通过音符特征匹配
        const pitch = this.getNoteElementPitch(noteElement);
        const measure = parseInt(noteElement.closest('measure')?.getAttribute('number') || '1');
        const staff = parseInt(noteElement.querySelector('staff')?.textContent || '1');
        const voice = parseInt(noteElement.querySelector('voice')?.textContent || '1');

        // 找到所有匹配的音符
        const matchingNotes = notesList.filter(note =>
            note.pitch === pitch.step &&
            note.octave === pitch.octave &&
            note.measure === measure &&
            note.staff === staff &&
            note.voice === voice
        );

        if (matchingNotes.length === 0) {
            console.log(`❌ 未找到匹配音符: ${pitch.step}${pitch.octave} 小节${measure}`);
            return null;
        } else if (matchIndex < matchingNotes.length) {
            const selectedNote = matchingNotes[matchIndex];
            console.log(`✅ 找到匹配音符[${matchIndex}]: ${pitch.step}${pitch.octave} -> ID=${selectedNote.id}`);
            return selectedNote.id;
        } else {
            console.log(`⚠️ 索引${matchIndex}超出范围，使用第一个匹配音符: ID=${matchingNotes[0].id}`);
            return matchingNotes[0].id;
        }
    }

    /**
     * 根据XML元素查找对应的音符ID
     */
    findNoteIdByElement(noteElement, notesList, xmlNoteIndex = null) {
        // 首先尝试通过data-note-id属性
        const dataId = noteElement.getAttribute('data-note-id');
        if (dataId) {
            console.log(`✅ 通过data-note-id找到音符: ${dataId}`);
            return dataId;
        }

        // 通过音符特征匹配
        const pitch = this.getNoteElementPitch(noteElement);
        const measure = parseInt(noteElement.closest('measure')?.getAttribute('number') || '1');
        const staff = parseInt(noteElement.querySelector('staff')?.textContent || '1');
        const voice = parseInt(noteElement.querySelector('voice')?.textContent || '1');

        // 调试：显示匹配条件
        console.log(`🔍 查找音符: ${pitch.step}${pitch.octave} 小节${measure} 谱表${staff} 声部${voice} (XML索引: ${xmlNoteIndex})`);
        console.log(`🔍 匹配条件: pitch='${pitch.step}' octave=${pitch.octave} measure=${measure} staff=${staff} voice=${voice}`);

        // 找到所有匹配的音符
        const matchingNotes = notesList.filter(note =>
            note.pitch === pitch.step &&
            note.octave === pitch.octave &&
            note.measure === measure &&
            note.staff === staff &&
            note.voice === voice
        );

        if (matchingNotes.length === 0) {
            console.log(`❌ 未找到匹配音符，查看前5个音符:`, notesList.slice(0, 5).map(n => ({
                pitch: n.pitch, octave: n.octave, measure: n.measure, staff: n.staff, voice: n.voice
            })));
            return null;
        } else if (matchingNotes.length === 1) {
            console.log(`✅ 找到唯一匹配音符: ID=${matchingNotes[0].id}`);
            return matchingNotes[0].id;
        } else {
            // 多个匹配音符，使用XML索引来区分
            console.log(`🔍 找到${matchingNotes.length}个匹配音符:`, matchingNotes.map(n => `ID=${n.id}`));

            if (xmlNoteIndex !== null && xmlNoteIndex < matchingNotes.length) {
                const selectedNote = matchingNotes[xmlNoteIndex];
                console.log(`✅ 根据XML索引${xmlNoteIndex}选择音符: ID=${selectedNote.id}`);
                return selectedNote.id;
            } else {
                // 如果没有XML索引或索引超出范围，返回第一个匹配的音符
                console.log(`⚠️ 使用第一个匹配音符: ID=${matchingNotes[0].id}`);
                return matchingNotes[0].id;
            }
        }
    }

    /**
     * 专门用于连奏线的音符匹配函数
     */
    findNoteIdByElementForSlur(noteElement, notesList) {
        // 首先尝试通过data-note-id属性
        const dataId = noteElement.getAttribute('data-note-id');
        if (dataId) {
            console.log(`✅ 通过data-note-id找到连奏线音符: ${dataId}`);
            return dataId;
        }

        // 获取音符信息
        const pitch = this.getNoteElementPitch(noteElement);
        const measure = parseInt(noteElement.closest('measure')?.getAttribute('number') || '1');
        const staff = parseInt(noteElement.querySelector('staff')?.textContent || '1');
        const voice = parseInt(noteElement.querySelector('voice')?.textContent || '1');
        const isChordNote = !!noteElement.querySelector('chord');

        // 获取XML中的时值信息
        const xmlDuration = parseInt(noteElement.querySelector('duration')?.textContent || '0');
        const xmlType = noteElement.querySelector('type')?.textContent || '';

        console.log(`🌊 查找连奏线音符: ${pitch.step}${pitch.octave} 小节${measure} 谱表${staff} 声部${voice} ${isChordNote ? '(和弦音符)' : '(单音符)'} 时值:${xmlType}(${xmlDuration})`);

        // 找到所有匹配的音符
        const matchingNotes = notesList.filter(note =>
            note.pitch === pitch.step &&
            note.octave === pitch.octave &&
            note.measure === measure &&
            note.staff === staff &&
            note.voice === voice
        );

        console.log(`🌊 找到${matchingNotes.length}个匹配的音符`);

        if (matchingNotes.length === 0) {
            console.warn(`❌ 没有找到匹配的连奏线音符`);
            return null;
        } else if (matchingNotes.length === 1) {
            console.log(`✅ 找到唯一匹配的连奏线音符: ID=${matchingNotes[0].id}`);
            return matchingNotes[0].id;
        } else {
            // 多个匹配时的处理策略
            console.log(`🌊 多个匹配音符，需要进一步筛选:`);
            matchingNotes.forEach((note, index) => {
                console.log(`   ${index}: ID=${note.id}, isChord=${note.isChord}, duration=${note.duration}`);
            });

            // 首先尝试按时值匹配
            let candidateNotes = matchingNotes;

            // 将XML时值类型转换为数字（用于匹配）
            const xmlDurationValue = this.getNoteDurationValue(xmlType);
            if (xmlDurationValue > 0) {
                const durationMatches = matchingNotes.filter(note => note.duration === xmlDurationValue);
                if (durationMatches.length > 0) {
                    console.log(`🌊 按时值筛选后找到${durationMatches.length}个匹配音符`);
                    candidateNotes = durationMatches;
                }
            }

            if (isChordNote) {
                // 如果XML中是和弦音符，优先选择和弦中的音符（isChord=true）
                const chordNotes = candidateNotes.filter(note => note.isChord === true);
                if (chordNotes.length > 0) {
                    console.log(`✅ 选择和弦音符: ID=${chordNotes[0].id}`);
                    return chordNotes[0].id;
                }
            } else {
                // 如果XML中不是和弦音符，优先选择主音符（isChord=false）
                const mainNotes = candidateNotes.filter(note => note.isChord === false);
                if (mainNotes.length > 0) {
                    console.log(`✅ 选择主音符: ID=${mainNotes[0].id}`);
                    return mainNotes[0].id;
                }
            }

            // 如果上述策略都失败，选择第一个候选音符
            console.log(`⚠️ 使用第一个候选音符: ID=${candidateNotes[0].id}`);
            return candidateNotes[0].id;
        }
    }

    /**
     * 将音符类型转换为时值数字
     */
    getNoteDurationValue(noteType) {
        const durationMap = {
            'whole': 1,
            'half': 2,
            'quarter': 4,
            'eighth': 8,
            '16th': 16,
            '32nd': 32,
            '64th': 64
        };
        return durationMap[noteType] || 0;
    }

    /**
     * 改进的音符匹配函数，用于连音线解析
     */
    findNoteIdByElementImproved(tieInfo, notesList) {
        // 首先尝试精确匹配：音高、小节、谱表、声部都相同
        const exactMatches = notesList.filter(note =>
            note.pitch === tieInfo.pitch.step &&
            note.octave === tieInfo.pitch.octave &&
            note.measure === tieInfo.measureNumber &&
            note.staff === tieInfo.staff &&
            note.voice === tieInfo.voice
        );

        console.log(`🔍 精确匹配结果: 找到${exactMatches.length}个候选音符，default-x=${tieInfo.defaultX}`);

        if (exactMatches.length === 0) {
            console.warn(`❌ 没有找到精确匹配的音符`);
            return null;
        } else if (exactMatches.length === 1) {
            console.log(`✅ 找到唯一精确匹配: ID=${exactMatches[0].id}`);
            return exactMatches[0].id;
        } else {
            // 多个匹配时，使用default-x位置信息进行精确匹配
            console.log(`🔍 多个精确匹配，使用default-x位置信息筹选: 目标default-x=${tieInfo.defaultX}`);

            // 按照在小节中的顺序排序
            exactMatches.sort((a, b) => {
                return a.id - b.id; // 按ID排序，通常ID反映了在XML中的顺序
            });

            // 使用更简单的方法：直接通过XML中的note元素来找到对应的noteId
            const measureElement = tieInfo.noteElement.closest('measure');
            const allNotesInMeasure = Array.from(measureElement.querySelectorAll('note'));
            const xmlNoteIndex = allNotesInMeasure.indexOf(tieInfo.noteElement);

            console.log(`🔍 XML中的音符索引: ${xmlNoteIndex}, 总音符数: ${allNotesInMeasure.length}`);

            // 找到小节中第一个音符的ID
            const firstNoteInMeasure = notesList.find(note => note.measure === tieInfo.measureNumber);
            if (firstNoteInMeasure) {
                const targetNoteId = firstNoteInMeasure.id + xmlNoteIndex;

                // 验证这个ID是否在候选音符中
                const matchingNote = exactMatches.find(note => note.id === targetNoteId);
                if (matchingNote) {
                    console.log(`✅ 根据XML索引${xmlNoteIndex}精确匹配到音符: ID=${targetNoteId}`);
                    return targetNoteId;
                }
            }

            // 如果精确匹配失败，回退到简单的索引匹配
            const selectedNote = exactMatches[0];
            console.log(`⚠️ 精确匹配失败，选择第一个候选音符: ID=${selectedNote.id}`);
            return selectedNote.id;
        }
    }

    /**
     * 获取音符元素的音高信息
     */
    getNoteElementPitch(noteElement) {
        const pitchElement = noteElement.querySelector('pitch');
        if (!pitchElement) {
            return {step: 'Rest', octave: 0}; // 休止符
        }

        const step = pitchElement.querySelector('step')?.textContent || 'C';
        const octave = parseInt(pitchElement.querySelector('octave')?.textContent || '4');
        const alter = parseInt(pitchElement.querySelector('alter')?.textContent || '0');

        let pitchName = step;
        if (alter > 0) pitchName += '#'.repeat(alter);
        if (alter < 0) pitchName += 'b'.repeat(Math.abs(alter));

        return {step: pitchName, octave: octave};
    }

    /**
     * 生成和弦连线状态标识
     */
    generateChordConnectionBadges(chordNotes) {
        if (!chordNotes || chordNotes.length === 0) return '';

        // 收集和弦中所有音符的连接信息
        const allConnections = {
            ties: new Set(),
            slurs: new Set(),
            beams: new Set()
        };

        chordNotes.forEach(note => {
            const connections = this.getNoteConnections(note.id);
            connections.ties.forEach(tie => allConnections.ties.add(tie.partnerId));
            connections.slurs.forEach(slur => allConnections.slurs.add(slur.number));
            connections.beams.forEach(beam => allConnections.beams.add(beam.number));
        });

        let badges = '';

        // 连音线标识
        if (allConnections.ties.size > 0) {
            badges += '<span class="badge bg-success ms-1" style="font-size: 0.7em;">连音线</span>';
        }

        // 连奏线标识
        if (allConnections.slurs.size > 0) {
            const slurCount = allConnections.slurs.size;
            badges += `<span class="badge bg-info ms-1" style="font-size: 0.7em;">连奏线${slurCount > 1 ? `×${slurCount}` : ''}</span>`;
        }

        // 连音符标识
        if (allConnections.beams.size > 0) {
            const beamCount = allConnections.beams.size;
            badges += `<span class="badge bg-secondary ms-1" style="font-size: 0.7em;">连音符${beamCount > 1 ? `×${beamCount}` : ''}</span>`;
        }

        return badges;
    }

    /**
     * 生成音符连线状态标识
     */
    generateConnectionBadges(noteId) {
        const connections = this.getNoteConnections(noteId);
        let badges = '';

        // 调试：输出连线信息
        if (connections.ties.length > 0 || connections.slurs.length > 0 || connections.beams.length > 0) {
            console.log(`🏷️ 音符${noteId}的连线信息:`, {
                ties: connections.ties.length,
                slurs: connections.slurs.length,
                beams: connections.beams.length
            });
        }

        // 连音线标识
        if (connections.ties.length > 0) {
            badges += '<span class="badge bg-success ms-1" style="font-size: 0.7em;">连音线</span>';
        }

        // 连奏线标识
        if (connections.slurs.length > 0) {
            const slurCount = connections.slurs.length;
            badges += `<span class="badge bg-info ms-1" style="font-size: 0.7em;">连奏线${slurCount > 1 ? `×${slurCount}` : ''}</span>`;
        }

        // 连音符标识
        if (connections.beams.length > 0) {
            const beamCount = connections.beams.length;
            badges += `<span class="badge bg-secondary ms-1" style="font-size: 0.7em;">连音符${beamCount > 1 ? `×${beamCount}` : ''}</span>`;
        }

        return badges;
    }

    /**
     * 生成连线删除按钮
     */
    generateConnectionDeleteButtons(noteId) {
        const connections = this.getNoteConnections(noteId);
        let buttons = '';

        // 连音线删除按钮
        if (connections.ties.length > 0) {
            buttons += `<button class="btn btn-outline-success btn-sm px-1" onclick="visualEditor.removeNoteTies('${noteId}')" title="删除连音线" style="font-size: 0.7em;">
                            <i class="fas fa-unlink"></i>
                        </button>`;
        }

        // 连奏线删除按钮
        if (connections.slurs.length > 0) {
            buttons += `<button class="btn btn-outline-info btn-sm px-1" onclick="visualEditor.removeNoteSlurs('${noteId}')" title="删除连奏线" style="font-size: 0.7em;">
                            <i class="fas fa-wave-square"></i>
                        </button>`;
        }

        // 连音符删除按钮
        if (connections.beams.length > 0) {
            buttons += `<button class="btn btn-outline-secondary btn-sm px-1" onclick="visualEditor.removeNoteBeams('${noteId}')" title="删除连音符" style="font-size: 0.7em;">
                            <i class="fas fa-cut"></i>
                        </button>`;
        }

        return buttons;
    }

    /**
     * 生成和弦连线删除按钮
     */
    generateChordConnectionDeleteButtons(chordNotes) {
        if (!chordNotes || chordNotes.length === 0) return '';

        // 收集和弦中所有音符的连接信息
        const allConnections = {
            ties: new Set(),
            slurs: new Set(),
            beams: new Set()
        };

        // 收集所有连线信息和对应的音符ID
        const tieNoteIds = [];
        const slurNoteIds = [];
        const beamNoteIds = [];

        chordNotes.forEach(note => {
            const connections = this.getNoteConnections(note.id);

            if (connections.ties.length > 0) {
                connections.ties.forEach(tie => allConnections.ties.add(tie.partnerId));
                tieNoteIds.push(note.id);
            }

            if (connections.slurs.length > 0) {
                connections.slurs.forEach(slur => allConnections.slurs.add(slur.number));
                slurNoteIds.push(note.id);
            }

            if (connections.beams.length > 0) {
                connections.beams.forEach(beam => allConnections.beams.add(beam.number));
                beamNoteIds.push(note.id);
            }
        });

        let buttons = '';

        // 连音线删除按钮
        if (allConnections.ties.size > 0) {
            const noteIdsStr = tieNoteIds.join(',');
            buttons += `<button class="btn btn-outline-success btn-sm px-1" onclick="visualEditor.removeChordTies('${noteIdsStr}')" title="删除和弦连音线" style="font-size: 0.7em;">
                            <i class="fas fa-unlink"></i>
                        </button>`;
        }

        // 连奏线删除按钮
        if (allConnections.slurs.size > 0) {
            const noteIdsStr = slurNoteIds.join(',');
            buttons += `<button class="btn btn-outline-info btn-sm px-1" onclick="visualEditor.removeChordSlurs('${noteIdsStr}')" title="删除和弦连奏线" style="font-size: 0.7em;">
                            <i class="fas fa-wave-square"></i>
                        </button>`;
        }

        // 连音符删除按钮
        if (allConnections.beams.size > 0) {
            const noteIdsStr = beamNoteIds.join(',');
            buttons += `<button class="btn btn-outline-secondary btn-sm px-1" onclick="visualEditor.removeChordBeams('${noteIdsStr}')" title="删除和弦连音符" style="font-size: 0.7em;">
                            <i class="fas fa-cut"></i>
                        </button>`;
        }

        return buttons;
    }

    /**
     * 删除和弦连音线
     */
    removeChordTies(noteIdsStr) {
        const noteIds = noteIdsStr.split(',').map(id => parseInt(id));
        console.log('🔗 删除和弦连音线:', noteIds);

        noteIds.forEach(noteId => {
            this.removeNoteTies(noteId);
        });
    }

    /**
     * 删除和弦连奏线
     */
    removeChordSlurs(noteIdsStr) {
        const noteIds = noteIdsStr.split(',').map(id => parseInt(id));
        console.log('🌊 删除和弦连奏线:', noteIds);

        noteIds.forEach(noteId => {
            this.removeNoteSlurs(noteId);
        });
    }

    /**
     * 删除和弦连音符
     */
    removeChordBeams(noteIdsStr) {
        const noteIds = noteIdsStr.split(',').map(id => parseInt(id));
        console.log('📊 删除和弦连音符:', noteIds);

        noteIds.forEach(noteId => {
            this.removeNoteBeams(noteId);
        });
    }

    /**
     * 获取音符类型的显示名称
     */
    getNoteTypeDisplayName(noteType) {
        const typeNames = {
            1: '全音符',
            2: '二分音符',
            4: '四分音符',
            8: '八分音符',
            16: '十六分音符',
            32: '三十二分音符'
        };
        return typeNames[noteType] || `${noteType}分音符`;
    }

    /**
     * 生成音符悬浮提示信息
     */
    generateNoteTooltip(note) {
        const connections = this.getNoteConnections(note.id);
        let tooltip = `${note.pitch}${note.octave} ${this.getNoteTypeDisplayName(note.duration)}`;

        // 基本信息
        tooltip += `\n小节${note.measure} | 谱表${note.staff} | 声部${note.voice}`;
        if (note.duration) {
            tooltip += ` | 时值${note.duration}`;
        }

        // 连线信息
        const connectionInfo = [];

        // 连音线信息
        if (connections.ties.length > 0) {
            connections.ties.forEach(tie => {
                const partnerNote = this.musicData.notesList.find(n => n.id === tie.partnerId);
                if (partnerNote) {
                    const partnerPosition = this.getNotePositionInMeasure(partnerNote);
                    connectionInfo.push(`🔗 连音线：连接 ${partnerNote.pitch}${partnerNote.octave}(小节${partnerNote.measure}|位置${partnerPosition})`);
                }
            });
        }

        // 连奏线信息
        if (connections.slurs.length > 0) {
            connections.slurs.forEach(slur => {
                const slurData = this.connectionData.slurs.get(slur.slurId);
                if (slurData) {
                    const connectedNotes = this.getConnectedNotesInfo(slurData.noteIds, note.id, 'slur');
                    connectionInfo.push(`🌊 连奏线：连接 ${connectedNotes}`);
                } else {
                    connectionInfo.push(`🌊 连奏线：第${slur.position}/${slur.total}个音符 (${slur.type})`);
                }
            });
        }

        // 连音符信息
        if (connections.beams.length > 0) {
            connections.beams.forEach(beam => {
                const beamData = this.connectionData.beams.get(beam.beamId);
                if (beamData) {
                    const connectedNotes = this.getConnectedNotesInfo(beamData.noteIds, note.id);
                    connectionInfo.push(`📊 连音符：连接 ${connectedNotes}`);
                } else {
                    connectionInfo.push(`📊 连音符：第${beam.position}/${beam.total}个音符 (${beam.type})`);
                }
            });
        }

        if (connectionInfo.length > 0) {
            tooltip += '\n\n连线状态:\n' + connectionInfo.join('\n');
        }

        return tooltip;
    }

    /**
     * 生成和弦悬浮提示信息
     */
    generateChordTooltip(chordNotes) {
        if (!chordNotes || chordNotes.length === 0) return '';

        // 获取和弦基本信息（使用第一个音符的信息）
        const firstNote = chordNotes[0];
        const noteNames = chordNotes.map(note => `${note.pitch}${note.octave}`).join('+');
        let tooltip = `和弦: ${noteNames} ${this.getNoteTypeDisplayName(firstNote.duration)}`;

        // 基本信息
        tooltip += `\n小节${firstNote.measure} | 谱表${firstNote.staff} | 声部${firstNote.voice}`;
        if (firstNote.duration) {
            tooltip += ` | 时值${firstNote.duration}`;
        }

        // 收集和弦整体的连线信息（去重）
        const connectionInfo = [];
        const processedConnections = new Set(); // 用于去重

        chordNotes.forEach((note) => {
            const connections = this.getNoteConnections(note.id);

            // 连音线信息 - 和弦级别处理，显示整个和弦的连接
            if (connections.ties.length > 0) {
                connections.ties.forEach(tie => {
                    const partnerNote = this.musicData.notesList.find(n => n.id === tie.partnerId);
                    if (partnerNote) {
                        const tieKey = `tie_${tie.partnerId}_${tie.type}`;
                        if (!processedConnections.has(tieKey)) {
                            processedConnections.add(tieKey);

                            // 查找伙伴音符所在位置的所有音符（和弦）
                            const partnerChordNotes = this.musicData.notesList.filter(n =>
                                n.measure === partnerNote.measure &&
                                n.staff === partnerNote.staff &&
                                n.voice === partnerNote.voice &&
                                Math.abs(n.defaultX - partnerNote.defaultX) < 5
                            ).sort((a, b) => a.pitch.localeCompare(b.pitch) || a.octave - b.octave);

                            // 检查当前和弦是否有多个音符连接到伙伴位置
                            const currentChordTieCount = chordNotes.filter(chordNote => {
                                const chordConnections = this.getNoteConnections(chordNote.id);
                                return chordConnections.ties.some(chordTie =>
                                    partnerChordNotes.some(partnerChordNote =>
                                        partnerChordNote.id === chordTie.partnerId
                                    )
                                );
                            }).length;

                            if (partnerChordNotes.length > 1 && currentChordTieCount > 1) {
                                // 显示和弦级别的连接
                                const partnerChordDisplay = partnerChordNotes.map(n => `${n.pitch}${n.octave}`).join('+');
                                const partnerPosition = this.getNotePositionInMeasure(partnerNote);
                                connectionInfo.push(`🔗 连音线：连接 ${partnerChordDisplay}(小节${partnerNote.measure}|位置${partnerPosition})`);

                                // 标记所有相关的tie为已处理，避免重复显示
                                partnerChordNotes.forEach(partnerChordNote => {
                                    processedConnections.add(`tie_${partnerChordNote.id}_${tie.type}`);
                                });
                            } else {
                                // 显示单音符级别的连接
                                const notePrefix = `${note.pitch}${note.octave}`;
                                const partnerPosition = this.getNotePositionInMeasure(partnerNote);
                                connectionInfo.push(`🔗 ${notePrefix}连音线：连接 ${partnerNote.pitch}${partnerNote.octave}(小节${partnerNote.measure}|位置${partnerPosition})`);
                            }
                        }
                    }
                });
            }

            // 连奏线信息 - 和弦级别处理，只显示一次
            if (connections.slurs.length > 0) {
                connections.slurs.forEach(slur => {
                    const slurKey = `slur_${slur.slurId}`;
                    if (!processedConnections.has(slurKey)) {
                        processedConnections.add(slurKey);
                        const slurData = this.connectionData.slurs.get(slur.slurId);
                        if (slurData) {
                            // 使用和弦中任意一个音符的ID来获取连接信息
                            const connectedNotes = this.getConnectedNotesInfo(slurData.noteIds, note.id);
                            connectionInfo.push(`🌊 连奏线：连接 ${connectedNotes}`);
                        } else {
                            connectionInfo.push(`🌊 连奏线：第${slur.position}/${slur.total}个音符 (${slur.type})`);
                        }
                    }
                });
            }

            // 连音符信息 - 和弦级别处理，只显示一次
            if (connections.beams.length > 0) {
                connections.beams.forEach(beam => {
                    const beamKey = `beam_${beam.beamId}`;
                    if (!processedConnections.has(beamKey)) {
                        processedConnections.add(beamKey);
                        const beamData = this.connectionData.beams.get(beam.beamId);
                        if (beamData) {
                            // 使用和弦中任意一个音符的ID来获取连接信息
                            const connectedNotes = this.getConnectedNotesInfo(beamData.noteIds, note.id);
                            connectionInfo.push(`📊 连音符：连接 ${connectedNotes}`);
                        } else {
                            connectionInfo.push(`📊 连音符：第${beam.position}/${beam.total}个音符 (${beam.type})`);
                        }
                    }
                });
            }
        });

        if (connectionInfo.length > 0) {
            tooltip += '\n\n连线状态:\n' + connectionInfo.join('\n');
        }

        return tooltip;
    }

    /**
     * 获取音符在小节中的位置编号
     */
    getNotePositionInMeasure(note) {
        try {
            // 获取同一小节、同一谱表的所有音符（不限制声部）
            const measureNotes = this.musicData.notesList.filter(n =>
                n.measure === note.measure &&
                n.staff === note.staff
            );

            // 按defaultX位置排序
            measureNotes.sort((a, b) => a.defaultX - b.defaultX);

            // 按位置分组，将同一位置的音符识别为一个组（和弦或单音符）
            const positionGroups = [];
            let currentGroup = [];
            let lastX = -1;

            measureNotes.forEach(n => {
                if (lastX === -1 || Math.abs(n.defaultX - lastX) < 5) {
                    // 同一位置的音符
                    currentGroup.push(n);
                } else {
                    // 新位置，保存当前组并开始新组
                    if (currentGroup.length > 0) {
                        positionGroups.push(currentGroup);
                    }
                    currentGroup = [n];
                }
                lastX = n.defaultX;
            });

            // 添加最后一组
            if (currentGroup.length > 0) {
                positionGroups.push(currentGroup);
            }

            // 查找目标音符所在的组
            for (let i = 0; i < positionGroups.length; i++) {
                const group = positionGroups[i];
                if (group.some(n => n.id === note.id)) {
                    return i + 1; // 位置编号从1开始
                }
            }

            return 1; // 默认返回1
        } catch (error) {
            console.error('获取音符位置失败:', error);
            return 1;
        }
    }

    /**
     * 获取连接音符的详细信息（用于连音符和连奏线提示）
     */
    getConnectedNotesInfo(noteIds, currentNoteId) {
        try {
            // 获取当前音符信息，用于识别当前和弦
            const currentNote = this.musicData.notesList.find(n => n.id === currentNoteId);
            if (!currentNote) {
                return '当前音符信息未找到';
            }

            // 找到当前和弦中的所有音符ID
            const currentChordNoteIds = this.musicData.notesList
                .filter(n =>
                    n.measure === currentNote.measure &&
                    n.staff === currentNote.staff &&
                    n.voice === currentNote.voice &&
                    Math.abs(n.defaultX - currentNote.defaultX) < 5
                )
                .map(n => n.id);

            // 过滤掉当前和弦中的所有音符，只显示其他连接的音符
            const otherNoteIds = noteIds.filter(id => !currentChordNoteIds.includes(id));



            if (otherNoteIds.length === 0) {
                return '无其他连接音符';
            }

            // 按位置分组，将同一位置的音符识别为和弦
            const positionGroups = new Map();

            // 使用Set来避免重复处理同一个位置组
            const processedGroups = new Set();

            otherNoteIds.forEach(noteId => {
                const note = this.musicData.notesList.find(n => n.id === noteId);
                if (!note) return;

                // 使用小节、谱表、声部、位置作为分组键，使用相同的容差逻辑
                const groupKey = `${note.measure}_${note.staff}_${note.voice}_${Math.round(note.defaultX / 5) * 5}`;

                // 如果这个位置组已经处理过，跳过
                if (processedGroups.has(groupKey)) {
                    return;
                }

                processedGroups.add(groupKey);

                // 查找同一位置的所有音符来形成完整的和弦
                const chordNotes = this.musicData.notesList.filter(n =>
                    n.measure === note.measure &&
                    n.staff === note.staff &&
                    n.voice === note.voice &&
                    Math.abs(n.defaultX - note.defaultX) < 5
                );
                positionGroups.set(groupKey, chordNotes);
            });

            // 为每个位置组生成显示信息，并按时间顺序排序
            const groupInfos = [];
            const sortedGroups = Array.from(positionGroups.entries()).sort((a, b) => {
                // 按defaultX位置排序（时间顺序）
                const aFirstNote = a[1][0];
                const bFirstNote = b[1][0];
                return aFirstNote.defaultX - bFirstNote.defaultX;
            });

            sortedGroups.forEach(([groupKey, notes]) => {
                if (notes.length === 1) {
                    // 单音符
                    const note = notes[0];
                    const pitchDisplay = note.isRest ? '休止符' : `${note.pitch}${note.octave}`;
                    const position = this.getNotePositionInMeasure(note);
                    groupInfos.push({
                        display: `${pitchDisplay}(小节${note.measure}|位置${position})`,
                        defaultX: note.defaultX,
                        measure: note.measure
                    });
                } else {
                    // 和弦 - 按音高排序
                    const sortedNotes = notes.sort((a, b) => {
                        const pitchOrder = {'C': 0, 'D': 1, 'E': 2, 'F': 3, 'G': 4, 'A': 5, 'B': 6};
                        const aPitch = pitchOrder[a.pitch] + a.octave * 7;
                        const bPitch = pitchOrder[b.pitch] + b.octave * 7;
                        return aPitch - bPitch;
                    });

                    const chordDisplay = sortedNotes.map(n => `${n.pitch}${n.octave}`).join('+');
                    const position = this.getNotePositionInMeasure(notes[0]);
                    groupInfos.push({
                        display: `${chordDisplay}(小节${notes[0].measure}|位置${position})`,
                        defaultX: notes[0].defaultX,
                        measure: notes[0].measure
                    });
                }
            });

            // 去重：移除重复的显示信息
            const uniqueDisplays = [];
            const seenDisplays = new Set();

            groupInfos.forEach(info => {
                if (!seenDisplays.has(info.display)) {
                    seenDisplays.add(info.display);
                    uniqueDisplays.push(info.display);
                }
            });

            // 如果只有一个组，直接返回
            if (uniqueDisplays.length === 1) {
                return uniqueDisplays[0];
            }

            // 如果有多个组，用箭头连接显示顺序
            return uniqueDisplays.join(' → ');

        } catch (error) {
            console.error('获取连接音符信息失败:', error);
            return '连接信息获取失败';
        }
    }

    /**
     * 刷新音符列表显示
     */
    refreshNotesDisplay() {
        try {
            // 重新加载和显示音符列表
            this.loadNotesList();
            console.log('🔄 音符列表显示已刷新');
        } catch (error) {
            console.error('刷新音符列表显示失败:', error);
        }
    }

    /**
     * 更新连音线按钮状态
     */
    updateTieButtonState() {
        const tieButton = document.getElementById('add-tie-mode');
        const slurButton = document.getElementById('add-slur-mode');

        if (tieButton) {
            if (this.tieMode) {
                tieButton.className = 'btn btn-success btn-sm w-100';
                tieButton.innerHTML = '<i class="fas fa-link me-1"></i>连音线 (活动)';
            } else {
                tieButton.className = 'btn btn-outline-success btn-sm w-100';
                tieButton.innerHTML = '<i class="fas fa-link me-1"></i>连音线';
            }
        }

        if (slurButton) {
            slurButton.className = 'btn btn-outline-info btn-sm w-100';
            slurButton.innerHTML = '<i class="fas fa-bezier-curve me-1"></i>连奏线';
        }
    }

    /**
     * 更新连奏线按钮状态
     */
    updateSlurButtonState() {
        const tieButton = document.getElementById('add-tie-mode');
        const slurButton = document.getElementById('add-slur-mode');
        const finishButton = document.getElementById('finish-slur');

        if (slurButton) {
            if (this.slurMode) {
                slurButton.className = 'btn btn-info btn-sm w-100';
                slurButton.innerHTML = '<i class="fas fa-bezier-curve me-1"></i>连奏线 (活动)';
            } else {
                slurButton.className = 'btn btn-outline-info btn-sm w-100';
                slurButton.innerHTML = '<i class="fas fa-bezier-curve me-1"></i>连奏线';
            }
        }

        if (tieButton) {
            tieButton.className = 'btn btn-outline-success btn-sm w-100';
            tieButton.innerHTML = '<i class="fas fa-link me-1"></i>连音线';
        }

        // 显示/隐藏完成连奏线按钮
        if (finishButton) {
            if (this.slurMode && this.selectedNotesForSlur.length >= 2) {
                finishButton.style.display = 'block';
            } else {
                finishButton.style.display = 'none';
            }
        }
    }

    /**
     * 更新连音符按钮状态
     */
    updateBeamButtonState() {
        const tieButton = document.getElementById('add-tie-mode');
        const slurButton = document.getElementById('add-slur-mode');
        const beamButton = document.getElementById('add-beam-mode');
        const finishBeamButton = document.getElementById('finish-beam');

        if (beamButton) {
            if (this.beamMode) {
                beamButton.className = 'btn btn-secondary btn-sm w-100';
                beamButton.innerHTML = '<i class="fas fa-grip-lines me-1"></i>连音符 (活动)';
            } else {
                beamButton.className = 'btn btn-outline-secondary btn-sm w-100';
                beamButton.innerHTML = '<i class="fas fa-grip-lines me-1"></i>连音符';
            }
        }

        if (tieButton) {
            tieButton.className = 'btn btn-outline-success btn-sm w-100';
            tieButton.innerHTML = '<i class="fas fa-link me-1"></i>连音线';
        }

        if (slurButton) {
            slurButton.className = 'btn btn-outline-info btn-sm w-100';
            slurButton.innerHTML = '<i class="fas fa-bezier-curve me-1"></i>连奏线';
        }

        // 显示/隐藏完成连音符按钮
        if (finishBeamButton) {
            if (this.beamMode && this.selectedNotesForBeam.length >= 2) {
                finishBeamButton.style.display = 'block';
            } else {
                finishBeamButton.style.display = 'none';
            }
        }
    }

    /**
     * 处理音符点击（用于连线）
     */
    handleNoteClickForTie(noteId) {
        if (!this.tieMode && !this.slurMode && !this.beamMode) {
            return false; // 不在连线模式下，不处理
        }

        const note = this.musicData.notesList.find(n => n.id === noteId);
        if (!note) {
            console.error('未找到音符:', noteId);
            return false;
        }

        if (this.tieMode) {
            return this.handleTieSelection(note);
        } else if (this.slurMode) {
            return this.handleSlurSelection(note);
        } else if (this.beamMode) {
            return this.handleBeamSelection(note);
        }

        return false;
    }

    /**
     * 处理连音线选择
     */
    handleTieSelection(note) {
        // 检查是否已经选择了这个音符
        if (this.selectedNotesForTie.find(n => n.id === note.id)) {
            this.showMessage('该音符已经被选择', 'warning');
            return true;
        }

        // 如果已经有选择的音符，检查音高是否相同
        if (this.selectedNotesForTie.length > 0) {
            const firstNote = this.selectedNotesForTie[0];
            if (firstNote.pitch !== note.pitch || firstNote.octave !== note.octave) {
                this.showMessage('连音线只能连接相同音高的音符', 'warning');
                return true;
            }
        }

        // 添加到选择列表
        this.selectedNotesForTie.push(note);
        console.log('选择音符用于连音线:', note.pitch + note.octave, '当前选择数量:', this.selectedNotesForTie.length);

        // 如果选择了两个音符，创建连音线
        if (this.selectedNotesForTie.length === 2) {
            this.createTie(this.selectedNotesForTie[0], this.selectedNotesForTie[1]);
            this.selectedNotesForTie = []; // 清空选择
        } else {
            this.showMessage(`已选择 ${this.selectedNotesForTie.length} 个音符，请选择第二个音符`, 'info');
        }

        return true;
    }

    /**
     * 处理连奏线选择
     */
    handleSlurSelection(note) {
        // 检查是否已经选择了这个音符
        if (this.selectedNotesForSlur.find(n => n.id === note.id)) {
            this.showMessage('该音符已经被选择', 'warning');
            return true;
        }

        // 添加到选择列表
        this.selectedNotesForSlur.push(note);
        console.log('选择音符用于连奏线:', note.pitch + note.octave, '当前选择数量:', this.selectedNotesForSlur.length);

        // 如果选择了两个或更多音符，可以创建连奏线
        if (this.selectedNotesForSlur.length >= 2) {
            this.showMessage(`已选择 ${this.selectedNotesForSlur.length} 个音符，点击"完成连奏线"按钮或继续选择音符`, 'info');
        } else {
            this.showMessage(`已选择 ${this.selectedNotesForSlur.length} 个音符，请继续选择音符`, 'info');
        }

        // 更新按钮状态
        this.updateSlurButtonState();

        return true;
    }

    /**
     * 处理连音符选择
     */
    handleBeamSelection(note) {
        // 检查音符类型是否适合连音符（八分音符、十六分音符等）
        if (!this.isBeamableNote(note)) {
            this.showMessage('连音符只能连接八分音符、十六分音符或更短的音符', 'warning');
            return true;
        }

        // 检查是否已经选择了这个音符
        if (this.selectedNotesForBeam.find(n => n.id === note.id)) {
            this.showMessage('该音符已经被选择', 'warning');
            return true;
        }

        // 添加到选择列表
        this.selectedNotesForBeam.push(note);

        // 确定显示名称（单音符或和弦）
        const displayName = note.isChord ?
            this.getChordDisplayName(note) :
            `${note.pitch}${note.octave}`;

        console.log('选择音符用于连音符:', displayName, '当前选择数量:', this.selectedNotesForBeam.length);

        // 如果选择了两个或更多音符，可以创建连音符
        if (this.selectedNotesForBeam.length >= 2) {
            this.showMessage(`已选择 ${this.selectedNotesForBeam.length} 个音符/和弦，点击"完成连音符"按钮或继续选择`, 'info');
        } else {
            this.showMessage(`已选择 ${this.selectedNotesForBeam.length} 个音符/和弦，请继续选择音符或和弦`, 'info');
        }

        // 更新按钮状态
        this.updateBeamButtonState();

        return true;
    }

    /**
     * 检查音符是否适合连音符
     */
    isBeamableNote(note) {
        // 连音符适用于八分音符(8)、十六分音符(16)、三十二分音符(32)等
        const beamableDurations = [8, 16, 32, 64, 128];

        // 首先检查duration属性（我们的音符对象使用duration而不是type）
        if (note.duration && beamableDurations.includes(note.duration)) {
            console.log('通过duration判断音符适合连音符:', note.pitch + note.octave, 'duration:', note.duration);
            return true;
        }

        // 如果type属性不存在或不匹配，检查XML中是否有beam元素
        if (this.musicData && this.musicData.xmlDoc) {
            const noteElement = this.findXMLNoteElement(note.id);
            console.log('查找XML音符元素结果:', {
                noteId: note.id,
                found: !!noteElement,
                pitch: note.pitch + note.octave
            });

            if (noteElement) {
                const beamElements = noteElement.querySelectorAll('beam');
                const typeElement = noteElement.querySelector('type');

                console.log('XML音符元素详情:', {
                    beamCount: beamElements.length,
                    type: typeElement ? typeElement.textContent.trim() : 'none',
                    xmlContent: noteElement.outerHTML.substring(0, 200) + '...'
                });

                if (beamElements.length > 0) {
                    console.log('通过XML beam元素判断音符适合连音符:', note.pitch + note.octave);
                    return true;
                }

                // 检查音符时值，八分音符及更短的音符通常适合连音符
                if (typeElement) {
                    const xmlType = typeElement.textContent.trim();
                    const xmlBeamableTypes = ['eighth', '16th', '32nd', '64th', '128th'];
                    if (xmlBeamableTypes.includes(xmlType)) {
                        console.log('通过XML type元素判断音符适合连音符:', note.pitch + note.octave, 'type:', xmlType);
                        return true;
                    }
                }
            } else {
                console.log('未找到XML音符元素，音符ID:', note.id);
            }
        }

        // 添加调试信息
        console.log('音符不适合连音符:', {
            id: note.id,
            pitch: note.pitch + note.octave,
            type: note.type,
            isChord: note.isChord
        });

        return false;
    }

    /**
     * 创建连音线
     */
    createTie(startNote, endNote) {
        console.log('创建连音线:', startNote.pitch + startNote.octave, '->', endNote.pitch + endNote.octave);

        try {
            // 在MusicXML中添加连音线
            this.addTieToXML(startNote, endNote);

            // 更新连线数据结构
            this.addTieConnection(startNote.id, endNote.id);

            // 标记有未保存的修改
            this.hasUnsavedChanges = true;
            this.updateSaveButtonState(); // 更新保存按钮状态

            this.showMessage(`已创建连音线: ${startNote.pitch}${startNote.octave} → ${endNote.pitch}${endNote.octave}`, 'success');

            // 关闭连音线模式
            this.tieMode = false;
            this.selectedNotesForTie = [];
            this.updateTieButtonState();

            // 刷新音符列表显示
            this.refreshNotesDisplay();

        } catch (error) {
            console.error('创建连音线失败:', error);
            this.showMessage('创建连音线失败: ' + error.message, 'danger');
        }
    }

    /**
     * 创建连奏线
     */
    createSlur(noteList) {
        if (noteList.length < 2) {
            this.showMessage('连奏线至少需要两个音符', 'warning');
            return;
        }

        console.log('创建连奏线，音符数量:', noteList.length);

        try {
            // 获取连奏线编号
            const slurNumber = this.getNextSlurNumber();

            // 在MusicXML中添加连奏线
            this.addSlurToXML(noteList);

            // 更新连线数据结构
            const noteIds = noteList.map(n => n.id);
            this.addSlurConnection(noteIds, slurNumber);

            // 标记有未保存的修改
            this.hasUnsavedChanges = true;
            this.updateSaveButtonState(); // 更新保存按钮状态

            const noteNames = noteList.map(n => n.pitch + n.octave).join(' → ');
            this.showMessage(`已创建连奏线: ${noteNames}`, 'success');

            // 关闭连奏线模式
            this.slurMode = false;
            this.selectedNotesForSlur = [];
            this.updateSlurButtonState();

            // 刷新音符列表显示
            this.refreshNotesDisplay();

        } catch (error) {
            console.error('创建连奏线失败:', error);
            this.showMessage('创建连奏线失败: ' + error.message, 'danger');
        }
    }

    /**
     * 在MusicXML中添加连音线
     */
    addTieToXML(startNote, endNote) {
        if (!this.musicData || !this.musicData.xmlDoc) {
            throw new Error('XML文档未加载');
        }

        const xmlDoc = this.musicData.xmlDoc;

        // 查找开始音符的XML元素
        const startNoteElement = this.findXMLNoteElement(startNote.id);
        if (!startNoteElement) {
            throw new Error('未找到开始音符的XML元素');
        }

        // 查找结束音符的XML元素
        const endNoteElement = this.findXMLNoteElement(endNote.id);
        if (!endNoteElement) {
            throw new Error('未找到结束音符的XML元素');
        }

        // 为开始音符添加tie元素
        const startTie = xmlDoc.createElement('tie');
        startTie.setAttribute('type', 'start');
        startNoteElement.appendChild(startTie);

        // 为开始音符添加notations/tied元素
        this.addTiedNotation(startNoteElement, 'start');

        // 为结束音符添加tie元素
        const endTie = xmlDoc.createElement('tie');
        endTie.setAttribute('type', 'stop');
        endNoteElement.appendChild(endTie);

        // 为结束音符添加notations/tied元素
        this.addTiedNotation(endNoteElement, 'stop');

        // 更新当前XML
        this.currentXML = new XMLSerializer().serializeToString(xmlDoc);

        console.log('连音线已添加到XML');
    }

    /**
     * 在MusicXML中添加连奏线
     */
    addSlurToXML(noteList) {
        if (!this.musicData || !this.musicData.xmlDoc) {
            throw new Error('XML文档未加载');
        }

        const xmlDoc = this.musicData.xmlDoc;
        const slurNumber = this.getNextSlurNumber();

        noteList.forEach((note, index) => {
            const noteElement = this.findXMLNoteElement(note.id);
            if (!noteElement) {
                console.warn('未找到音符的XML元素:', note.id);
                return;
            }

            let slurType;
            if (index === 0) {
                slurType = 'start';
            } else if (index === noteList.length - 1) {
                slurType = 'stop';
            } else {
                slurType = 'continue';
            }

            this.addSlurNotation(noteElement, slurType, slurNumber);
        });

        // 更新当前XML
        this.currentXML = new XMLSerializer().serializeToString(xmlDoc);

        console.log('连奏线已添加到XML');
    }

    /**
     * 添加tied notation元素
     */
    addTiedNotation(noteElement, type) {
        const xmlDoc = this.musicData.xmlDoc;

        // 查找或创建notations元素
        let notations = noteElement.querySelector('notations');
        if (!notations) {
            notations = xmlDoc.createElement('notations');
            noteElement.appendChild(notations);
        }

        // 创建tied元素
        const tied = xmlDoc.createElement('tied');
        tied.setAttribute('type', type);
        notations.appendChild(tied);
    }

    /**
     * 添加slur notation元素
     */
    addSlurNotation(noteElement, type, number) {
        const xmlDoc = this.musicData.xmlDoc;

        // 查找或创建notations元素
        let notations = noteElement.querySelector('notations');
        if (!notations) {
            notations = xmlDoc.createElement('notations');
            noteElement.appendChild(notations);
        }

        // 创建slur元素
        const slur = xmlDoc.createElement('slur');
        slur.setAttribute('type', type);
        slur.setAttribute('number', number.toString());
        notations.appendChild(slur);
    }

    /**
     * 获取下一个连奏线编号
     */
    getNextSlurNumber() {
        if (!this.musicData || !this.musicData.xmlDoc) {
            return 1;
        }

        // 查找现有的连奏线编号
        const existingSlurs = this.musicData.xmlDoc.querySelectorAll('slur[number]');
        let maxNumber = 0;

        existingSlurs.forEach(slur => {
            const number = parseInt(slur.getAttribute('number'));
            if (number > maxNumber) {
                maxNumber = number;
            }
        });

        return maxNumber + 1;
    }

    /**
     * 从XML删除连音线
     */
    removeTieFromXML(startNoteId, endNoteId) {
        if (!this.musicData || !this.musicData.xmlDoc) {
            throw new Error('XML文档未加载');
        }

        const xmlDoc = this.musicData.xmlDoc;

        // 查找并删除开始音符的tie元素
        const startNoteElement = this.findXMLNoteElement(startNoteId);
        if (startNoteElement) {
            const startTies = startNoteElement.querySelectorAll('tie[type="start"]');
            startTies.forEach(tie => tie.parentNode.removeChild(tie));

            const startTieds = startNoteElement.querySelectorAll('notations tied[type="start"]');
            startTieds.forEach(tied => tied.parentNode.removeChild(tied));
        }

        // 查找并删除结束音符的tie元素
        const endNoteElement = this.findXMLNoteElement(endNoteId);
        if (endNoteElement) {
            const endTies = endNoteElement.querySelectorAll('tie[type="stop"]');
            endTies.forEach(tie => tie.parentNode.removeChild(tie));

            const endTieds = endNoteElement.querySelectorAll('notations tied[type="stop"]');
            endTieds.forEach(tied => tied.parentNode.removeChild(tied));
        }

        // 清理空的notations元素
        [startNoteElement, endNoteElement].forEach(noteElement => {
            if (noteElement) {
                const notations = noteElement.querySelector('notations');
                if (notations && notations.children.length === 0) {
                    notations.parentNode.removeChild(notations);
                }
            }
        });

        // 更新当前XML
        this.currentXML = new XMLSerializer().serializeToString(xmlDoc);
    }

    /**
     * 从XML删除连奏线
     */
    removeSlurFromXML(slurNumber) {
        if (!this.musicData || !this.musicData.xmlDoc) {
            throw new Error('XML文档未加载');
        }

        const xmlDoc = this.musicData.xmlDoc;

        // 查找并删除所有该编号的slur元素
        const slurs = xmlDoc.querySelectorAll(`slur[number="${slurNumber}"]`);
        slurs.forEach(slur => {
            const notations = slur.parentNode;
            notations.removeChild(slur);

            // 如果notations元素为空，删除它
            if (notations.children.length === 0) {
                notations.parentNode.removeChild(notations);
            }
        });

        // 更新当前XML
        this.currentXML = new XMLSerializer().serializeToString(xmlDoc);
    }

    /**
     * 从XML删除连音符
     */
    removeBeamFromXML(beamNumber) {
        if (!this.musicData || !this.musicData.xmlDoc) {
            throw new Error('XML文档未加载');
        }

        const xmlDoc = this.musicData.xmlDoc;

        // 查找并删除所有该编号的beam元素
        const beams = xmlDoc.querySelectorAll(`beam[number="${beamNumber}"]`);
        beams.forEach(beam => {
            beam.parentNode.removeChild(beam);
        });

        // 更新当前XML
        this.currentXML = new XMLSerializer().serializeToString(xmlDoc);
    }

    /**
     * 删除指定音符的连音线
     */
    removeNoteTies(noteId) {
        const connections = this.getNoteConnections(noteId);
        if (connections.ties.length === 0) {
            this.showMessage('该音符没有连音线', 'info');
            return;
        }

        // 显示确认对话框
        const note = this.musicData.notesList.find(n => n.id === noteId);
        const noteName = note ? `${note.pitch}${note.octave}` : '该音符';

        if (!confirm(`确定要删除 ${noteName} 的所有连音线吗？\n\n这将删除 ${connections.ties.length} 个连音线连接。`)) {
            return;
        }

        try {
            // 删除XML中的连音线元素
            connections.ties.forEach(tie => {
                this.removeTieFromXML(noteId, tie.partnerId);
            });

            // 更新连线数据结构
            this.removeTieConnections(noteId);

            // 标记有未保存的修改
            this.hasUnsavedChanges = true;
            this.updateSaveButtonState(); // 更新保存按钮状态

            // 刷新显示
            this.refreshNotesDisplay();

            this.showMessage(`已删除 ${noteName} 的连音线`, 'success');

        } catch (error) {
            console.error('删除连音线失败:', error);
            this.showMessage('删除连音线失败: ' + error.message, 'danger');
        }
    }

    /**
     * 删除指定音符的连奏线
     */
    removeNoteSlurs(noteId) {
        const connections = this.getNoteConnections(noteId);
        if (connections.slurs.length === 0) {
            this.showMessage('该音符没有连奏线', 'info');
            return;
        }

        // 显示确认对话框
        const note = this.musicData.notesList.find(n => n.id === noteId);
        const noteName = note ? `${note.pitch}${note.octave}` : '该音符';

        if (!confirm(`确定要删除 ${noteName} 的所有连奏线吗？\n\n这将删除 ${connections.slurs.length} 个连奏线连接。`)) {
            return;
        }

        try {
            // 删除XML中的连奏线元素
            connections.slurs.forEach(slur => {
                this.removeSlurFromXML(slur.number);
            });

            // 更新连线数据结构
            this.removeSlurConnections(noteId);

            // 标记有未保存的修改
            this.hasUnsavedChanges = true;
            this.updateSaveButtonState(); // 更新保存按钮状态

            // 刷新显示
            this.refreshNotesDisplay();

            this.showMessage(`已删除 ${noteName} 的连奏线`, 'success');

        } catch (error) {
            console.error('删除连奏线失败:', error);
            this.showMessage('删除连奏线失败: ' + error.message, 'danger');
        }
    }

    /**
     * 删除指定音符的连音符
     */
    removeNoteBeams(noteId) {
        const connections = this.getNoteConnections(noteId);
        if (connections.beams.length === 0) {
            this.showMessage('该音符没有连音符', 'info');
            return;
        }

        // 显示确认对话框
        const note = this.musicData.notesList.find(n => n.id === noteId);
        const noteName = note ? `${note.pitch}${note.octave}` : '该音符';

        if (!confirm(`确定要删除 ${noteName} 的所有连音符吗？\n\n这将删除 ${connections.beams.length} 个连音符连接。`)) {
            return;
        }

        try {
            // 删除XML中的连音符元素
            connections.beams.forEach(beam => {
                this.removeBeamFromXML(beam.number);
            });

            // 更新连线数据结构
            this.removeBeamConnections(noteId);

            // 标记有未保存的修改
            this.hasUnsavedChanges = true;
            this.updateSaveButtonState(); // 更新保存按钮状态

            // 刷新显示
            this.refreshNotesDisplay();

            this.showMessage(`已删除 ${noteName} 的连音符`, 'success');

        } catch (error) {
            console.error('删除连音符失败:', error);
            this.showMessage('删除连音符失败: ' + error.message, 'danger');
        }
    }



    /**
     * 根据音符ID查找XML中的note元素
     */
    findXMLNoteElement(noteId) {
        if (!this.musicData) {
            return null;
        }

        // 🔧 关键修复：使用工作文档，确保与addBeamToXML使用同一个文档
        const currentXmlDoc = this.workingXmlDoc || (() => {
            const xmlParser = new DOMParser();
            return xmlParser.parseFromString(this.currentXML, 'application/xml');
        })();

        // 首先尝试通过data-note-id属性查找
        let noteElement = currentXmlDoc.querySelector(`note[data-note-id="${noteId}"]`);
        if (noteElement) {
            console.log(`🔍 通过data-note-id找到音符: ${noteId}`);
            return noteElement;
        }

        // 如果没找到，尝试通过音符数据匹配
        const note = this.musicData.notesList.find(n => n.id === noteId);
        if (!note) {
            console.log('未在notesList中找到音符ID:', noteId);
            return null;
        }

        console.log('查找XML元素的音符数据:', {
            id: note.id,
            pitch: note.pitch + note.octave,
            measure: note.measure,
            staff: note.staff,
            voice: note.voice,
            defaultX: note.defaultX,
            isChord: note.isChord
        });

        // 查找对应小节
        const measures = currentXmlDoc.querySelectorAll('measure');
        for (const measure of measures) {
            const measureNumber = measure.getAttribute('number');
            if (parseInt(measureNumber) === note.measure) {
                // 在该小节中查找匹配的音符
                const notes = measure.querySelectorAll('note');
                for (const xmlNote of notes) {
                    if (this.isMatchingNote(xmlNote, note)) {
                        return xmlNote;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 检查XML音符是否匹配数据音符
     */
    isMatchingNote(xmlNote, noteData) {
        const pitch = xmlNote.querySelector('pitch');
        const staff = xmlNote.querySelector('staff');
        const voice = xmlNote.querySelector('voice');

        if (!pitch || !staff || !voice) {
            return false;
        }

        const step = pitch.querySelector('step')?.textContent;
        const octave = parseInt(pitch.querySelector('octave')?.textContent);
        const staffNum = parseInt(staff.textContent);
        const voiceNum = parseInt(voice.textContent);

        return step === noteData.pitch &&
               octave === noteData.octave &&
               staffNum === noteData.staff &&
               voiceNum === noteData.voice;
    }

    /**
     * 处理音符卡片点击
     */
    handleNoteCardClick(noteId) {
        // 如果在连线模式下，处理连线选择
        if (this.tieMode || this.slurMode || this.beamMode) {
            const handled = this.handleNoteClickForTie(noteId);
            if (handled) {
                return; // 连线模式下处理完毕，不执行其他操作
            }
        }

        // 普通模式下，可以添加其他点击行为
        console.log('音符卡片点击:', noteId);
    }

    /**
     * 处理和弦卡片点击
     */
    handleChordCardClick(chordId) {
        // 如果在连线模式下，处理连线选择
        if (this.tieMode || this.slurMode || this.beamMode) {
            const handled = this.handleChordClickForConnection(chordId);
            if (handled) {
                return; // 连线模式下处理完毕，不执行其他操作
            }
        }

        // 普通模式下，不执行任何操作（避免意外触发编辑）
        // 用户应该使用明确的编辑按钮来编辑和弦
        console.log('和弦卡片点击（普通模式）:', chordId);
    }

    /**
     * 处理和弦点击用于连线
     */
    handleChordClickForConnection(chordId) {
        console.log('🎵 处理和弦点击用于连线, chordId:', chordId);

        // 根据chordId找到和弦中的所有音符
        const chordNotes = this.findChordNotes(chordId);
        console.log('🔍 找到的和弦音符:', chordNotes);

        if (!chordNotes || chordNotes.length === 0) {
            console.error('未找到和弦音符:', chordId);
            return false;
        }

        // 优先选择和弦的主音符（isChord: false）作为代表
        const mainNote = chordNotes.find(note => !note.isChord);
        const representativeNote = mainNote || chordNotes[0];

        console.log('🎯 和弦音符分析:', {
            总数: chordNotes.length,
            主音符: mainNote ? `${mainNote.pitch}${mainNote.octave}(ID:${mainNote.id})` : '未找到',
            选择的代表: `${representativeNote.pitch}${representativeNote.octave}(ID:${representativeNote.id})`
        });
        console.log('🎯 最终选择的代表音符:', {
            id: representativeNote.id,
            pitch: representativeNote.pitch + representativeNote.octave,
            duration: representativeNote.duration,
            isChord: representativeNote.isChord
        });

        if (this.tieMode) {
            return this.handleTieSelection(representativeNote);
        } else if (this.slurMode) {
            return this.handleSlurSelection(representativeNote);
        } else if (this.beamMode) {
            return this.handleBeamSelection(representativeNote);
        }

        return false;
    }

    /**
     * 根据和弦ID查找和弦中的所有音符
     */
    findChordNotes(chordId) {
        console.log('🔍 查找和弦音符, chordId:', chordId);

        // 首先尝试从notesList中找到匹配的音符
        const allNotes = this.musicData.notesList;
        console.log('📝 总音符数量:', allNotes.length);

        // 如果chordId包含音符ID信息，提取它
        let targetNoteId = null;
        if (chordId.includes('_')) {
            const parts = chordId.split('_');
            targetNoteId = parseInt(parts[parts.length - 1]); // 获取最后一部分作为音符ID
            console.log('🎯 提取的目标音符ID:', targetNoteId);
        }

        // 首先找到目标音符
        const targetNote = allNotes.find(note => note.id === targetNoteId);
        if (!targetNote) {
            console.log('❌ 未找到目标音符ID:', targetNoteId);
            return [];
        }

        console.log('🎯 找到目标音符:', {
            id: targetNote.id,
            pitch: targetNote.pitch + targetNote.octave,
            measure: targetNote.measure,
            staff: targetNote.staff,
            defaultX: targetNote.defaultX,
            isChord: targetNote.isChord
        });

        // 查找与目标音符在同一位置的所有音符（包括和弦主音符和从音符）
        const tolerance = 2; // 位置容差
        const chordNotes = allNotes.filter(note =>
            note.measure === targetNote.measure &&
            note.staff === targetNote.staff &&
            Math.abs(note.defaultX - targetNote.defaultX) < tolerance
        );

        console.log('🔍 同位置所有音符:', chordNotes.map(n => `${n.pitch}${n.octave}(ID:${n.id}, isChord:${n.isChord})`));

        // 如果找到多个音符，说明是和弦，返回所有音符
        // 如果只找到一个音符，说明是单音符，也返回
        const finalChordNotes = chordNotes.length > 1 ? chordNotes : [targetNote];

        console.log('🎵 找到的同位置和弦音符:', chordNotes.map(n => `${n.pitch}${n.octave}(ID:${n.id})`));

        console.log('🎵 最终返回的和弦音符:', finalChordNotes.map(n => `${n.pitch}${n.octave}(ID:${n.id})`));
        return finalChordNotes;
    }

    /**
     * 获取和弦的显示名称
     */
    getChordDisplayName(note) {
        // 查找与此音符位置相同的其他音符
        const samePositionNotes = this.musicData.notesList.filter(n =>
            n.measure === note.measure &&
            n.staff === note.staff &&
            Math.abs(n.defaultX - note.defaultX) < 2 // 位置容差
        );

        if (samePositionNotes.length > 1) {
            // 按音高排序
            const sortedNotes = samePositionNotes.sort((a, b) => {
                const pitchOrder = { 'C': 0, 'D': 1, 'E': 2, 'F': 3, 'G': 4, 'A': 5, 'B': 6 };
                const aPitch = pitchOrder[a.pitch] + a.octave * 7;
                const bPitch = pitchOrder[b.pitch] + b.octave * 7;
                return aPitch - bPitch;
            });

            return sortedNotes.map(n => `${n.pitch}${n.octave}`).join('+');
        }

        return `${note.pitch}${note.octave}`;
    }

    /**
     * 完成连奏线创建（右键菜单或按钮触发）
     */
    finishSlurCreation() {
        if (this.selectedNotesForSlur.length >= 2) {
            this.createSlur(this.selectedNotesForSlur);
        } else {
            this.showMessage('连奏线至少需要两个音符', 'warning');
        }
    }

    /**
     * 创建连音符
     */
    async createBeam(noteList) {
        if (noteList.length < 2) {
            this.showMessage('连音符至少需要两个音符', 'warning');
            return;
        }

        console.log('创建连音符，音符数量:', noteList.length);

        try {
            // 获取连音符编号（基于音符时值）
            const beamNumber = this.getBeamNumber(noteList);

            // 在MusicXML中添加连音符（使用新的异步方法）
            await this.addBeamToXML(noteList);

            // 更新连线数据结构
            const noteIds = noteList.map(n => n.id);
            this.addBeamConnection(noteIds, beamNumber);

            // 标记有未保存的修改
            this.hasUnsavedChanges = true;
            this.updateSaveButtonState(); // 更新保存按钮状态

            const noteNames = noteList.map(n => n.pitch + n.octave).join(' → ');
            this.showMessage(`已创建连音符: ${noteNames}`, 'success');

            // 关闭连音符模式
            this.beamMode = false;
            this.selectedNotesForBeam = [];
            this.updateBeamButtonState();

            // 刷新音符列表显示
            this.refreshNotesDisplay();

        } catch (error) {
            console.error('创建连音符失败:', error);
            this.showMessage('创建连音符失败: ' + error.message, 'danger');
        }
    }

    /**
     * 完成连音符创建
     */
    finishBeamCreation() {
        if (this.selectedNotesForBeam.length >= 2) {
            this.createBeam(this.selectedNotesForBeam);
        } else {
            this.showMessage('连音符至少需要两个音符', 'warning');
        }
    }

    /**
     * 在MusicXML中添加连音符
     */
    async addBeamToXML(noteList) {
        if (!this.musicData) {
            throw new Error('音乐数据未加载');
        }

        return await this.safeXmlOperation('addBeamToXML', async () => {
            // 🔧 使用统一的XML文档管理
            const xmlDoc = this.getWorkingXmlDoc(true); // 跳过锁检查，因为我们已经在安全操作内部
            if (!xmlDoc) {
                throw new Error('无法获取工作XML文档');
            }

            const beamNumber = this.getBeamNumber(noteList);

            // 🔍 调试：显示noteList的详细信息
            console.log('🔍 addBeamToXML - noteList详情:');
            noteList.forEach((note, index) => {
                console.log(`  ${index}: ${note.pitch}${note.octave} (ID:${note.id}, isChord:${note.isChord}, defaultX:${note.defaultX})`);
            });

            noteList.forEach((note, index) => {
            let beamType;
            if (index === 0) {
                beamType = 'begin';
            } else if (index === noteList.length - 1) {
                beamType = 'end';
            } else {
                beamType = 'continue';
            }

            console.log(`🔍 处理音符 ${index}: ${note.pitch}${note.octave} → beamType=${beamType}`);

            // 检查这个音符是否是和弦的一部分
            const chordNotes = this.findChordNotesByPosition(note);

            if (chordNotes && chordNotes.length > 1) {
                // 这是一个和弦，需要为和弦中的所有音符添加beam元素
                console.log(`🎵 为和弦添加beam元素: ${chordNotes.length}个音符, type=${beamType}, number=${beamNumber}`);

                chordNotes.forEach(chordNote => {
                    const noteElement = this.findXMLNoteElement(chordNote.id);
                    if (noteElement) {
                        this.addBeamElement(noteElement, beamType, beamNumber);
                        console.log(`✅ 为和弦音符添加beam: ${chordNote.pitch}${chordNote.octave}(ID:${chordNote.id}) type=${beamType}`);
                    } else {
                        console.warn(`❌ 未找到和弦音符的XML元素: ${chordNote.pitch}${chordNote.octave}(ID:${chordNote.id})`);
                    }
                });
            } else {
                // 这是单音符，按原来的方式处理
                const noteElement = this.findXMLNoteElement(note.id);
                if (noteElement) {
                    this.addBeamElement(noteElement, beamType, beamNumber);
                    console.log(`✅ 为单音符添加beam: ${note.pitch}${note.octave}(ID:${note.id}) type=${beamType}`);
                } else {
                    console.warn(`❌ 未找到单音符的XML元素: ${note.pitch}${note.octave}(ID:${note.id})`);
                }
            }
        });

        // 🔍 调试：检查XML序列化前的beam标记
        console.log('🔍 XML序列化前检查beam标记:');
        noteList.forEach((note, index) => {
            const noteElement = this.findXMLNoteElement(note.id);
            if (noteElement) {
                const beams = noteElement.querySelectorAll('beam');
                console.log(`  音符${index} ${note.pitch}${note.octave}: ${beams.length}个beam标记`);
                beams.forEach(beam => {
                    console.log(`    - number=${beam.getAttribute('number')}, type=${beam.textContent}`);
                });

                // 🔍 额外调试：显示整个音符元素的HTML结构
                console.log(`  🔍 音符${index}完整HTML:`, noteElement.outerHTML);
            } else {
                console.error(`❌ 找不到音符${index}: ${note.pitch}${note.octave} (ID: ${note.id})`);
            }
        });

        // 🔧 关键修复：在序列化前，强制刷新所有新添加音符的beam标记
        console.log('🔧 序列化前强制刷新beam标记...');
        noteList.forEach((note, index) => {
            const noteElement = this.findXMLNoteElement(note.id);
            if (noteElement && noteElement.hasAttribute('data-added-by-editor')) {
                console.log(`🔧 强制刷新新添加音符的beam标记: ${note.pitch}${note.octave}`);

                // 重新添加beam标记
                const beamNumber = this.getBeamNumber(noteList);
                let beamType;
                if (index === 0) {
                    beamType = 'begin';
                } else if (index === noteList.length - 1) {
                    beamType = 'end';
                } else {
                    beamType = 'continue';
                }

                // 强制重新添加beam元素
                this.addBeamElement(noteElement, beamType, beamNumber);
            }
            });

            // 🔧 使用统一的XML提交系统
            if (!this.commitXmlChanges(xmlDoc, true)) { // 跳过锁检查，因为我们已经在安全操作内部
                throw new Error('XML更改提交失败');
            }

            // 🔍 调试：检查XML序列化后的beam标记
            console.log('🔍 XML序列化后检查beam标记:');
            const serializedParser = new DOMParser();
            const serializedDoc = serializedParser.parseFromString(this.currentXML, 'text/xml');
            noteList.forEach((note, index) => {
                const noteElement = this.findXMLNoteElementInDoc(serializedDoc, note.id);
                if (noteElement) {
                    const beams = noteElement.querySelectorAll('beam');
                    console.log(`  音符${index} ${note.pitch}${note.octave}: ${beams.length}个beam标记`);
                    beams.forEach(beam => {
                        console.log(`    - number=${beam.getAttribute('number')}, type=${beam.textContent}`);
                    });
                } else {
                    console.error(`❌ 序列化后找不到音符: ${note.pitch}${note.octave} (ID: ${note.id})`);
                }
            });

            console.log('✅ 连音符已添加到XML');
            return true;
        });
    }

    /**
     * 在指定文档中查找XML音符元素（用于调试）
     */
    findXMLNoteElementInDoc(xmlDoc, noteId) {
        // 先尝试通过data-note-id查找
        let noteElement = xmlDoc.querySelector(`note[data-note-id="${noteId}"]`);
        if (noteElement) {
            return noteElement;
        }

        // 如果没找到，通过notesList索引查找
        const noteData = this.musicData.notesList.find(n => n.id == noteId);
        if (!noteData) {
            return null;
        }

        // 查找所有音符元素
        const allNotes = Array.from(xmlDoc.querySelectorAll('note'));

        // 通过位置和音高匹配
        for (let xmlNote of allNotes) {
            const pitch = xmlNote.querySelector('pitch');
            if (pitch) {
                const step = pitch.querySelector('step')?.textContent;
                const octave = pitch.querySelector('octave')?.textContent;
                const defaultX = xmlNote.getAttribute('default-x');

                if (step === noteData.pitch &&
                    octave === noteData.octave.toString() &&
                    Math.abs(parseFloat(defaultX) - noteData.defaultX) < 1) {
                    return xmlNote;
                }
            }
        }

        return null;
    }

    /**
     * 根据音符位置查找和弦中的所有音符
     */
    findChordNotesByPosition(note) {
        if (!note || !this.musicData || !this.musicData.notesList) {
            return [note];
        }

        // 查找与目标音符在同一位置的所有音符（包括和弦主音符和从音符）
        const tolerance = 2; // 位置容差
        const chordNotes = this.musicData.notesList.filter(n =>
            n.measure === note.measure &&
            n.staff === note.staff &&
            Math.abs(n.defaultX - note.defaultX) < tolerance
        );

        console.log(`🔍 查找位置相同的音符: ${note.pitch}${note.octave}(${note.defaultX}) -> 找到${chordNotes.length}个音符`);
        chordNotes.forEach(n => {
            console.log(`  - ${n.pitch}${n.octave}(ID:${n.id}, defaultX:${n.defaultX}, isChord:${n.isChord})`);
        });

        return chordNotes.length > 0 ? chordNotes : [note];
    }

    /**
     * 添加beam元素到音符
     */
    addBeamElement(noteElement, type, number) {
        const xmlDoc = this.musicData.xmlDoc;

        // 🔧 重要修复：只清除相同number的beam元素，保留其他beam
        const existingBeam = noteElement.querySelector(`beam[number="${number}"]`);
        if (existingBeam) {
            console.log(`🧹 清除已有beam元素: number=${existingBeam.getAttribute('number')}, type=${existingBeam.textContent}`);
            existingBeam.remove();
        }

        // 创建新的beam元素
        const beam = xmlDoc.createElement('beam');
        beam.setAttribute('number', number.toString());
        beam.textContent = type;

        // 🔧 关键修复：确保beam元素插入到正确位置，遵循MusicXML标准顺序
        // MusicXML标准顺序：pitch, duration, voice, type, dot, stem, beam, staff, notations
        const staff = noteElement.querySelector('staff');
        const notations = noteElement.querySelector('notations');

        if (notations) {
            // 如果有notations，插入在notations之前
            noteElement.insertBefore(beam, notations);
            console.log('🔧 beam元素插入在notations之前');
        } else if (staff) {
            // 如果有staff，插入在staff之前
            noteElement.insertBefore(beam, staff);
            console.log('🔧 beam元素插入在staff之前');
        } else {
            // 否则添加到末尾
            noteElement.appendChild(beam);
            console.log('🔧 beam元素添加到音符末尾');
        }

        // 🔧 额外验证：检查插入后的元素顺序
        const allChildren = Array.from(noteElement.children);
        const beamIndex = allChildren.findIndex(child => child.tagName === 'beam' && child.getAttribute('number') === number.toString());
        const staffIndex = allChildren.findIndex(child => child.tagName === 'staff');

        if (beamIndex !== -1 && staffIndex !== -1 && beamIndex < staffIndex) {
            console.log(`✅ beam元素顺序正确: beam(${beamIndex}) < staff(${staffIndex})`);
        } else if (beamIndex !== -1 && staffIndex === -1) {
            console.log(`✅ beam元素已添加，无staff元素`);
        } else {
            console.warn(`⚠️ beam元素顺序可能有问题: beam(${beamIndex}), staff(${staffIndex})`);
        }

        // 🔍 验证beam元素是否正确添加
        const verifyBeam = noteElement.querySelector(`beam[number="${number}"]`);
        if (verifyBeam && verifyBeam.textContent === type) {
            console.log(`✅ 添加新beam元素成功: number=${number}, type=${type}`);
            console.log(`🔍 验证beam元素: ${verifyBeam.outerHTML}`);
        } else {
            console.error(`❌ beam元素添加失败: number=${number}, type=${type}`);
        }
    }

    /**
     * 根据音符时值获取正确的beam编号
     * 基于MusicXML标准：number="1"对应八分音符beam，number="2"对应十六分音符beam等
     */
    getBeamNumber(noteList) {
        if (!noteList || noteList.length === 0) {
            return 1;
        }

        // 分析音符时值，确定需要的beam层级
        const durations = noteList.map(note => note.duration);
        const minDuration = Math.min(...durations);

        console.log('🎵 分析连音符时值:', {
            音符时值: durations,
            最小时值: minDuration,
            音符详情: noteList.map(n => `${n.pitch}${n.octave}(${n.duration})`)
        });

        // 根据MusicXML标准确定beam number
        // number="1": 八分音符级别 (duration >= 2)
        // number="2": 十六分音符级别 (duration = 1)
        // number="3": 三十二分音符级别 (duration = 0.5)
        // 但实际上，大多数情况下都使用number="1"作为主beam

        if (minDuration >= 2) {
            // 八分音符及以上，使用主beam
            console.log('🎵 使用主beam层级 (number="1"): 包含八分音符或更长时值');
            return 1;
        } else if (minDuration >= 1) {
            // 十六分音符，通常也使用主beam，除非需要特殊的副beam
            console.log('🎵 使用主beam层级 (number="1"): 十六分音符连音符');
            return 1;
        } else {
            // 三十二分音符及以下，可能需要副beam，但通常仍从主beam开始
            console.log('🎵 使用主beam层级 (number="1"): 三十二分音符及以下');
            return 1;
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM加载完成，开始初始化可视化编辑器');
    try {
        const editor = new VisualEditor();
        // 🔧 添加全局引用以便在模态框中使用
        window.visualEditor = editor;
        console.log('可视化编辑器创建成功');
    } catch (error) {
        console.error('创建可视化编辑器失败:', error);
        alert('初始化失败: ' + error.message);
    }
});
