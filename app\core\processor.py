"""
核心处理流程协调器
整合Audiveris、指法生成和MuseScore渲染
"""
import os
import logging
import uuid
from datetime import datetime
from pathlib import Path

from app.core.audiveris_engine import AudiverisEngine
from app.core.fingering_engine import FingeringEngine
from app.core.musescore_engine import MuseScoreEngine
from app.core.image_processor import ImageProcessor
from app.core.mxl_extractor import MXLExtractor

logger = logging.getLogger(__name__)

class PianoFingeringProcessor:
    """钢琴指法处理器主类"""

    def __init__(self, config=None):
        if config:
            # 使用传入的配置创建引擎
            self.audiveris = AudiverisEngine(config=config)
            self.fingering = FingeringEngine(config=config)
            self.musescore = MuseScoreEngine(config=config)
            self.image_processor = ImageProcessor(config=config)
            self.mxl_extractor = MXLExtractor()
        else:
            # 使用默认方式创建引擎（需要Flask应用上下文）
            self.audiveris = AudiverisEngine()
            self.fingering = FingeringEngine()
            self.musescore = MuseScoreEngine()
            self.image_processor = ImageProcessor()
            self.mxl_extractor = MXLExtractor()
        
    def process_image_ocr_only(self, image_path, options=None, progress_callback=None):
        """
        只进行OCR识别的处理流程，用于两步式处理的第一步

        Args:
            image_path (str): 输入图片路径
            options (dict): 处理选项
            progress_callback (callable): 进度回调函数

        Returns:
            dict: 处理结果（只包含OCR识别结果）
        """
        if options is None:
            options = {}

        if progress_callback is None:
            progress_callback = lambda step, progress, message: None

        # 生成唯一的处理ID
        import uuid
        process_id = str(uuid.uuid4())[:8]

        # 初始化结果结构
        result = {
            'success': False,
            'files': {},
            'steps': {},
            'errors': [],
            'process_id': process_id
        }

        try:
            logger.info(f"[{process_id}] 开始OCR识别处理: {image_path}")

            # 步骤1: 图像预处理
            logger.info(f"[{process_id}] 开始图像预处理")
            progress_callback('preprocessing', 10, '开始图像预处理...')
            result['steps']['preprocessing'] = {'status': 'running', 'start_time': datetime.now()}

            base_name = os.path.splitext(os.path.basename(image_path))[0]
            preprocessed_image = self.image_processor.preprocess_image(
                image_path,
                output_name=base_name,
                **options.get('preprocessing', {})
            )

            if not preprocessed_image['success']:
                result['errors'].append(f"图像预处理失败: {preprocessed_image['error']}")
                return result

            result['files']['preprocessed_image'] = preprocessed_image['output_path']
            result['steps']['preprocessing']['status'] = 'completed'
            result['steps']['preprocessing']['end_time'] = datetime.now()
            progress_callback('preprocessing', 25, '图像预处理完成')

            # 步骤2: Audiveris OCR识别
            logger.info(f"[{process_id}] 开始乐谱识别")
            progress_callback('ocr', 30, '开始乐谱识别...')
            result['steps']['ocr'] = {'status': 'running', 'start_time': datetime.now()}

            base_name = os.path.splitext(os.path.basename(image_path))[0]
            ocr_result = self.audiveris.process_image(
                preprocessed_image['output_path'],
                output_name=base_name
            )

            if not ocr_result['success']:
                result['errors'].append(f"乐谱识别失败: {ocr_result['error']}")
                return result

            result['files'].update(ocr_result['files'])

            # 传递文字集成结果
            if 'text_integration' in ocr_result:
                result['text_integration'] = ocr_result['text_integration']
                logger.info(f"[{process_id}] 文字集成结果已传递")

            result['steps']['ocr']['status'] = 'completed'
            result['steps']['ocr']['end_time'] = datetime.now()
            progress_callback('ocr', 50, '乐谱识别完成')

            # 步骤3: 准备XML文件
            logger.info(f"[{process_id}] 准备XML文件")
            progress_callback('prepare_xml', 58, '准备XML文件...')
            result['steps']['prepare_xml'] = {'status': 'running', 'start_time': datetime.now()}

            # 如果是MXL文件，需要解压
            if 'mxl' in ocr_result['files']:
                mxl_path = ocr_result['files']['mxl']
                extracted_xml = self.mxl_extractor.extract_mxl_to_xml(mxl_path)

                if extracted_xml['success']:
                    result['files']['extracted_xml'] = extracted_xml['xml_path']
                    logger.info(f"[{process_id}] MXL解压成功: {extracted_xml['xml_path']}")
                else:
                    logger.warning(f"[{process_id}] MXL解压失败: {extracted_xml['error']}")
                    # 尝试使用原始XML文件
                    if 'xml' in ocr_result['files']:
                        result['files']['extracted_xml'] = ocr_result['files']['xml']
            elif 'xml' in ocr_result['files']:
                result['files']['extracted_xml'] = ocr_result['files']['xml']

            result['steps']['prepare_xml']['status'] = 'completed'
            result['steps']['prepare_xml']['end_time'] = datetime.now()
            progress_callback('prepare_xml', 60, 'XML文件准备完成')

            # 步骤4: 生成预览图片
            logger.info(f"[{process_id}] 生成预览图片")
            progress_callback('preview', 90, '生成预览图片...')

            try:
                # 获取XML文件路径 - 优先使用增强版XML（包含文字集成信息）
                enhanced_xml = result['files'].get('enhanced_xml')
                extracted_xml = result['files'].get('extracted_xml')
                xml_for_preview = enhanced_xml or extracted_xml

                if xml_for_preview and os.path.exists(xml_for_preview):
                    # 生成预览文件名
                    task_id = base_name.split('_')[1] if '_' in base_name else base_name
                    preview_name = f"sync_{task_id}_preview"

                    # 使用MuseScore生成预览
                    preview_result = self.musescore.render_to_png(
                        xml_for_preview,
                        output_name=preview_name,
                        dpi=150
                    )

                    if preview_result['success']:
                        result['files']['preview'] = preview_result['output_path']
                        logger.info(f"[{process_id}] 预览生成成功: {preview_result['output_path']}")
                    else:
                        logger.warning(f"[{process_id}] 预览生成失败: {preview_result['error']}")
                else:
                    logger.warning(f"[{process_id}] 未找到可用的XML文件生成预览")
            except Exception as e:
                logger.warning(f"[{process_id}] 预览生成异常: {str(e)}")

            # OCR识别完成
            result['success'] = True
            result['total_time'] = (datetime.now() - result['steps']['preprocessing']['start_time']).total_seconds()

            progress_callback('preview', 100, 'OCR识别完成')
            logger.info(f"[{process_id}] OCR识别完成，耗时 {result['total_time']:.2f} 秒")

            return result

        except Exception as e:
            logger.error(f"[{process_id}] OCR处理异常: {str(e)}")
            result['errors'].append(f"OCR处理异常: {str(e)}")
            return result

    def process_image(self, image_path, options=None, progress_callback=None):
        """
        完整的图片处理流程：直接使用Audiveris + PianoPlayer
        跳过过度的XML修复，保持原始乐谱复杂性

        Args:
            image_path (str): 输入图片路径
            options (dict): 处理选项
            progress_callback (callable): 进度回调函数

        Returns:
            dict: 处理结果
        """
        if options is None:
            options = {}

        # 进度回调辅助函数
        def update_progress(step, progress, status):
            if progress_callback:
                progress_callback(step, progress, status)
            logger.info(f"进度更新: {step} - {status} ({progress}%)")

        # 生成唯一的处理ID
        process_id = str(uuid.uuid4())[:8]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = f"{timestamp}_{process_id}"

        result = {
            'process_id': process_id,
            'timestamp': timestamp,
            'base_name': base_name,
            'success': False,
            'steps': {},
            'files': {},
            'errors': [],
            'warnings': []
        }

        try:
            # 步骤1: 图像预处理
            logger.info(f"[{process_id}] 开始图像预处理")
            preprocessing_options = options.get('preprocessing', {})
            logger.info(f"[{process_id}] 预处理选项: {preprocessing_options}")
            update_progress('preprocessing', 10, '开始图像预处理...')
            result['steps']['preprocessing'] = {'status': 'running', 'start_time': datetime.now()}

            preprocessed_result = self.image_processor.preprocess_image(
                image_path,
                output_name=base_name,
                **preprocessing_options
            )

            if not preprocessed_result['success']:
                result['steps']['preprocessing']['status'] = 'failed'
                result['steps']['preprocessing']['error'] = preprocessed_result['error']
                result['errors'].append(f"图像预处理失败: {preprocessed_result['error']}")
                update_progress('preprocessing', 0, '图像预处理失败')
                return result

            result['steps']['preprocessing']['status'] = 'completed'
            result['steps']['preprocessing']['end_time'] = datetime.now()
            result['files']['preprocessed_image'] = preprocessed_result['output_path']
            update_progress('preprocessing', 25, '图像预处理完成')

            # 步骤2: 乐谱识别（使用极限模式以获得最佳识别效果）
            logger.info(f"[{process_id}] 开始乐谱识别")
            update_progress('ocr', 30, '开始乐谱识别...')
            result['steps']['ocr'] = {'status': 'running', 'start_time': datetime.now()}

            # 使用Audiveris进行乐谱识别
            ocr_result = self.audiveris.process_image(
                preprocessed_result['output_path'],
                output_name=base_name
            )
            
            if not ocr_result['success']:
                result['steps']['ocr']['status'] = 'failed'
                result['steps']['ocr']['error'] = ocr_result['error']
                result['errors'].append(f"乐谱识别失败: {ocr_result['error']}")
                return result
            
            result['steps']['ocr']['status'] = 'completed'
            result['steps']['ocr']['end_time'] = datetime.now()
            result['files'].update(ocr_result['files'])
            update_progress('ocr', 50, '乐谱识别完成')

            # 步骤3: 准备XML文件（跳过修复，保持原始复杂性）
            logger.info(f"[{process_id}] 准备XML文件进行指法生成")
            update_progress('prepare_xml', 58, '准备XML文件...')
            result['steps']['prepare_xml'] = {'status': 'running', 'start_time': datetime.now()}

            # 获取XML文件路径
            xml_file = None
            mxl_file = ocr_result['files'].get('mxl')

            if mxl_file:
                # 如果是MXL文件，只解压不修复
                logger.info(f"[{process_id}] 检测到MXL文件，解压: {mxl_file}")
                extract_result = self.mxl_extractor.extract_mxl_to_xml(mxl_file)
                if extract_result['success']:
                    xml_file = extract_result['xml_path']
                    result['files']['extracted_xml'] = xml_file
                    logger.info(f"[{process_id}] MXL解压成功: {xml_file}")
                else:
                    logger.error(f"[{process_id}] MXL解压失败: {extract_result['error']}")
                    result['steps']['prepare_xml']['status'] = 'failed'
                    result['steps']['prepare_xml']['error'] = extract_result['error']
                    result['errors'].append(f"MXL解压失败: {extract_result['error']}")
                    return result
            else:
                xml_file = ocr_result['files'].get('xml')

            if not xml_file or not os.path.exists(xml_file):
                result['steps']['prepare_xml']['status'] = 'failed'
                result['steps']['prepare_xml']['error'] = '未找到可用的XML文件'
                result['errors'].append('未找到可用的XML文件')
                return result

            result['steps']['prepare_xml']['status'] = 'completed'
            result['steps']['prepare_xml']['end_time'] = datetime.now()
            update_progress('prepare_xml', 60, 'XML文件准备完成')

            # 步骤4: PaddleOCR文字识别
            logger.info(f"[{process_id}] 开始PaddleOCR文字识别")
            update_progress('text_recognition', 70, '开始文字识别...')
            result['steps']['text_recognition'] = {'status': 'running', 'start_time': datetime.now()}

            try:
                # 使用PaddleOCR识别原始图像中的文本信息
                text_info = self._extract_text_with_paddleocr(image_path, process_id)
                result['text_info'] = text_info
                result['steps']['text_recognition']['status'] = 'completed'
                result['steps']['text_recognition']['end_time'] = datetime.now()
                update_progress('text_recognition', 80, '文字识别完成')
                logger.info(f"[{process_id}] PaddleOCR文字识别完成")
            except Exception as e:
                logger.warning(f"[{process_id}] PaddleOCR文字识别失败: {str(e)}")
                result['steps']['text_recognition']['status'] = 'failed'
                result['steps']['text_recognition']['error'] = str(e)
                result['text_info'] = {}
                update_progress('text_recognition', 80, '文字识别失败，继续处理...')

            # 步骤5: 文字集成到XML，生成enhanced.mxl
            logger.info(f"[{process_id}] 开始文字集成，生成enhanced.mxl")
            update_progress('text_integration', 85, '集成文字信息...')
            result['steps']['text_integration'] = {'status': 'running', 'start_time': datetime.now()}

            # 使用准备好的XML文件进行文字集成
            xml_for_integration = result['files'].get('extracted_xml') or ocr_result['files'].get('xml')

            if not xml_for_integration:
                result['steps']['text_integration']['status'] = 'failed'
                result['steps']['text_integration']['error'] = '未找到可用的XML文件进行文字集成'
                result['errors'].append('文字集成失败: 未找到可用的XML文件')
                return result

            try:
                # 简化文字集成：直接使用TextIntegrationEngine处理XML文件
                from app.core.text_integration import TextIntegrationEngine
                text_integration = TextIntegrationEngine()

                logger.info(f"[{process_id}] 开始文字集成处理")

                # 查找对应的图片文件
                base_name = os.path.splitext(os.path.basename(xml_for_integration))[0]
                temp_dir = os.path.join(os.path.dirname(os.path.dirname(xml_for_integration)), 'temp')
                image_path = os.path.join(temp_dir, f"{base_name}.png")

                if os.path.exists(image_path):
                    # 使用已识别的文本信息进行集成
                    integration_result = text_integration.integrate_text_with_existing_info(
                        musicxml_path=xml_for_integration,
                        text_info=result.get('text_info', {}),
                        output_path=None
                    )

                    if integration_result.get('success', False):
                        enhanced_xml_path = integration_result.get('output_file', xml_for_integration)
                        enhanced_mxl_path = self._xml_to_mxl(enhanced_xml_path, process_id)
                        result['files']['enhanced_xml'] = enhanced_xml_path
                        result['files']['enhanced_mxl'] = enhanced_mxl_path
                        logger.info(f"[{process_id}] 文字集成成功")
                    else:
                        # 集成失败，直接使用原XML
                        enhanced_mxl_path = self._xml_to_mxl(xml_for_integration, process_id)
                        result['files']['enhanced_mxl'] = enhanced_mxl_path
                        logger.warning(f"[{process_id}] 文字集成失败，使用原XML")
                else:
                    # 没有图片文件，直接使用原XML
                    enhanced_mxl_path = self._xml_to_mxl(xml_for_integration, process_id)
                    result['files']['enhanced_mxl'] = enhanced_mxl_path
                    logger.info(f"[{process_id}] 未找到图片文件，使用原XML")

                result['steps']['text_integration']['status'] = 'completed'
                result['steps']['text_integration']['end_time'] = datetime.now()
                update_progress('text_integration', 90, '文字集成完成')

            except Exception as e:
                logger.warning(f"[{process_id}] 文字集成异常: {str(e)}")
                result['steps']['text_integration']['status'] = 'failed'
                result['steps']['text_integration']['error'] = str(e)
                # 如果集成失败，直接使用原XML转换为MXL
                enhanced_mxl_path = self._xml_to_mxl(xml_for_integration, process_id)
                result['files']['enhanced_mxl'] = enhanced_mxl_path
                update_progress('text_integration', 90, '文字集成失败，使用原始XML')

            # 步骤6: 生成预览图片（使用enhanced.mxl）
            logger.info(f"[{process_id}] 开始生成预览图片")
            update_progress('preview_generation', 95, '生成预览图片...')
            result['steps']['preview_generation'] = {'status': 'running', 'start_time': datetime.now()}

            # 使用MuseScore渲染enhanced.mxl为PNG预览图
            png_result = self.musescore.render_to_png(
                enhanced_mxl_path,
                output_name=f"{base_name}_preview",
                dpi=options.get('output', {}).get('dpi', 300)
            )

            if not png_result['success']:
                result['steps']['preview_generation']['status'] = 'failed'
                result['steps']['preview_generation']['error'] = png_result['error']
                result['errors'].append(f"预览图生成失败: {png_result['error']}")
                logger.error(f"[{process_id}] 预览图生成失败: {png_result['error']}")
                return result

            result['files']['preview'] = png_result['output_path']
            logger.info(f"[{process_id}] 预览图生成成功: {png_result['output_path']}")

            result['steps']['preview_generation']['status'] = 'completed'
            result['steps']['preview_generation']['end_time'] = datetime.now()

            # 处理完成
            result['success'] = True
            result['total_time'] = (datetime.now() - result['steps']['preprocessing']['start_time']).total_seconds()

            update_progress('preview_generation', 100, '处理完成')
            logger.info(f"[{process_id}] 处理完成，耗时 {result['total_time']:.2f} 秒")
            
            return result

        except Exception as e:
            logger.error(f"[{process_id}] 处理异常: {str(e)}")
            result['errors'].append(f"处理异常: {str(e)}")

            # 确保所有步骤都有状态
            for step_name in ['preprocessing', 'ocr', 'prepare_xml', 'text_recognition', 'text_integration', 'preview_generation']:
                if step_name not in result['steps']:
                    result['steps'][step_name] = {
                        'status': 'failed',
                        'error': '未执行或执行失败'
                    }

            return result

    def _extract_text_with_paddleocr(self, image_path, process_id):
        """
        使用PaddleOCR从图像中提取文本信息
        """
        logger.info(f"[{process_id}] 开始PaddleOCR文字识别: {image_path}")

        try:
            # 使用真实的PaddleOCR进行文字识别
            logger.info(f"[{process_id}] 开始真实PaddleOCR文字识别: {image_path}")

            from app.core.text_recognition import TextRecognitionEngine

            text_engine = TextRecognitionEngine()
            ocr_result = text_engine.recognize_text(image_path)

            if not ocr_result['success']:
                logger.warning(f"[{process_id}] PaddleOCR识别失败: {ocr_result['error']}")
                # 如果PaddleOCR失败，使用基础信息
                return {
                    'title': '',
                    'subtitle': '',
                    'composer': '',
                    'arranger': '',
                    'original_artist': '',
                    'other_texts': []
                }

            # 格式化识别结果
            formatted_result = text_engine.format_recognition_result(ocr_result['texts'], image_path)

            # 转换为标准格式
            text_info = {
                'title': formatted_result.get('title', ''),
                'subtitle': formatted_result.get('subtitle', ''),
                'composer': formatted_result.get('composer', ''),
                'arranger': formatted_result.get('arranger', ''),  # 使用识别到的编曲信息
                'original_artist': formatted_result.get('lyricist', ''),  # 使用lyricist字段作为原唱
                'other_texts': formatted_result.get('other_texts', [])
            }

            # 解析composer字段中的详细信息（修复分割逻辑）
            composer_text = text_info.get('composer', '') or ''
            arranger_text = text_info.get('arranger', '') or ''

            # 优先使用已识别的编曲信息
            if arranger_text:
                text_info['arranger'] = arranger_text.strip()
                logger.info(f"[{process_id}] 使用已识别的编曲信息: {arranger_text}")
            elif composer_text and '编曲' in composer_text:
                # 如果composer字段包含编曲信息，说明分类有误，需要重新分配
                if '作曲' in composer_text and composer_text.find('作曲') < composer_text.find('编曲'):
                    # 包含作曲和编曲，按"编曲"分割
                    parts = composer_text.split('编曲', 1)
                    if len(parts) == 2:
                        text_info['composer'] = parts[0].replace('作曲', '').strip()
                        text_info['arranger'] = '编曲' + parts[1].strip()
                        logger.info(f"[{process_id}] 从composer字段分离编曲信息: {text_info['arranger']}")
                else:
                    # 只有编曲信息，移动到arranger字段
                    text_info['arranger'] = composer_text
                    text_info['composer'] = ''
                    logger.info(f"[{process_id}] 将composer字段移动到arranger: {composer_text}")

            # 清理composer字段
            if composer_text and '作曲' in composer_text and '编曲' not in composer_text:
                # 只保留作曲信息
                text_info['composer'] = composer_text.replace('作曲', '').strip()

            # 处理原唱信息（保留完整信息，不删除关键词）
            original_artist_text = text_info.get('original_artist', '') or ''
            if original_artist_text:
                # 保留完整的原唱信息，包括"原唱："前缀
                text_info['original_artist'] = original_artist_text.strip()

            logger.info(f"[{process_id}] PaddleOCR识别完成: 标题={text_info.get('title', '')}, 副标题={text_info.get('subtitle', '')}, 作曲={text_info.get('composer', '')}, 编曲={text_info.get('arranger', '')}, 原唱={text_info.get('original_artist', '')}")
            # 文字分类完成
            return text_info

        except Exception as e:
            logger.error(f"[{process_id}] PaddleOCR识别失败: {str(e)}")
            return {}

    def _integrate_text_to_mxl(self, xml_path, text_info, process_id):
        """
        将文字信息集成到XML中，生成enhanced.mxl文件
        策略：PaddleOCR识别结果覆盖Audiveris结果，如果识别失败则使用Audiveris结果但应用自定义样式
        """
        import os
        import xml.etree.ElementTree as ET

        logger.info(f"[{process_id}] 开始文字集成到MXL: {xml_path}")

        try:
            # 1. 查找对应的图片文件（在temp目录中）
            base_name = os.path.splitext(os.path.basename(xml_path))[0]

            # 图片文件在temp目录中，使用相同的base_name
            temp_dir = os.path.join(os.path.dirname(os.path.dirname(xml_path)), 'temp')
            image_path = os.path.join(temp_dir, f"{base_name}.png")

            logger.info(f"[{process_id}] 查找图片文件: {image_path}")

            if not os.path.exists(image_path):
                logger.warning(f"[{process_id}] 图片文件不存在: {image_path}")
                # 尝试其他可能的扩展名
                for ext in ['.jpg', '.jpeg']:
                    alt_path = os.path.join(temp_dir, f"{base_name}{ext}")
                    if os.path.exists(alt_path):
                        image_path = alt_path
                        logger.info(f"[{process_id}] 找到替代图片文件: {image_path}")
                        break
                else:
                    # 如果找不到图片文件，直接应用自定义样式到Audiveris结果
                    logger.warning(f"[{process_id}] 未找到图片文件，直接应用自定义样式到Audiveris结果")
                    return self._apply_custom_style_to_audiveris_xml(xml_path, process_id)

            # 2. 使用TextIntegrationEngine进行文字集成（使用已识别的文本信息，避免重复OCR）
            try:
                from app.core.text_integration import TextIntegrationEngine
                text_integration = TextIntegrationEngine()

                logger.info(f"[{process_id}] 使用TextIntegrationEngine处理文字集成")

                # 直接调用文字集成方法，使用已识别的文本信息，避免重复PaddleOCR识别
                integration_result = text_integration.integrate_text_with_existing_info(
                    musicxml_path=xml_path,
                    text_info=text_info,  # 使用已经识别的文本信息
                    output_path=None  # 直接覆盖原文件
                )

                if integration_result.get('success', False):
                    logger.info(f"[{process_id}] PaddleOCR识别成功，使用PaddleOCR结果")
                    enhanced_xml_path = integration_result.get('output_file', xml_path)
                    enhanced_mxl_path = self._xml_to_mxl(enhanced_xml_path, process_id)

                    return {
                        'enhanced_xml': enhanced_xml_path,
                        'enhanced_mxl': enhanced_mxl_path
                    }
                else:
                    logger.warning(f"[{process_id}] PaddleOCR识别失败: {integration_result.get('error', '未知错误')}")
                    # 回退到使用Audiveris结果但应用自定义样式
                    return self._apply_custom_style_to_audiveris_xml(xml_path, process_id)

            except Exception as e:
                logger.error(f"[{process_id}] TextIntegrationEngine处理异常: {str(e)}")
                # 回退到使用Audiveris结果但应用自定义样式
                return self._apply_custom_style_to_audiveris_xml(xml_path, process_id)

        except Exception as e:
            logger.error(f"[{process_id}] 文字集成失败: {str(e)}")
            # 如果失败，回退到使用Audiveris结果但应用自定义样式
            return self._apply_custom_style_to_audiveris_xml(xml_path, process_id)

    def _apply_custom_style_to_audiveris_xml(self, xml_path, process_id):
        """
        对Audiveris生成的XML应用自定义样式
        保持Audiveris识别的内容，但使用我们的标准样式
        """
        import xml.etree.ElementTree as ET
        import os

        logger.info(f"[{process_id}] 应用自定义样式到Audiveris XML: {xml_path}")

        try:
            # 读取XML文件
            tree = ET.parse(xml_path)
            root = tree.getroot()

            # 获取页面布局信息来确定正确的坐标
            page_layout = root.find('.//page-layout')
            if page_layout is not None:
                page_width_elem = page_layout.find('page-width')
                page_height_elem = page_layout.find('page-height')
                page_width = int(page_width_elem.text) if page_width_elem is not None else 800
                page_height = int(page_height_elem.text) if page_height_elem is not None else 1200
            else:
                page_width = 800
                page_height = 1200

            center_x = page_width // 2
            right_x = page_width - 50  # 右对齐，留50像素边距

            # 1. 保存现有的credit信息（用于分析）
            existing_credits = root.findall('.//credit')
            audiveris_copyright = None

            # 提取版权信息 - 从identification/rights元素中获取，但不删除
            identification = root.find('identification')
            if identification is not None:
                rights = identification.find('rights')
                if rights is not None and rights.text:
                    audiveris_copyright = rights.text.strip()
                    # 保留rights元素，不删除，让版权信息正常显示
                    logger.info(f"[{process_id}] 找到rights元素中的版权信息: {audiveris_copyright}")

            # 也检查credit元素中的版权信息（作为备用）
            if not audiveris_copyright:
                for credit in existing_credits:
                    credit_words = credit.find('credit-words')
                    if credit_words is not None and credit_words.text:
                        text = credit_words.text.strip()
                        y_pos = credit_words.get('default-y')
                        # 检查是否是版权信息（短文本且在底部）
                        if (len(text) <= 5 and y_pos and int(y_pos) < 200):
                            audiveris_copyright = text
                            break

            # 2. 保留版权信息的credit元素，只移除其他credit元素
            copyright_credits = []
            credits_to_remove = []

            for credit in existing_credits:
                credit_words = credit.find('credit-words')
                if credit_words is not None and credit_words.text:
                    text = credit_words.text.strip()
                    # 识别版权信息
                    is_copyright = (
                        (len(text) <= 5 and any(c.isalpha() for c in text)) or
                        any(keyword in text.lower() for keyword in ['©', 'copyright', '版权']) or
                        (len(text) <= 3 and text.isalpha())
                    )
                    is_title = (
                        len(text) > 5 and (' ' in text or any('\u4e00' <= c <= '\u9fff' for c in text))
                    )

                    if is_copyright and not is_title:
                        # 这是版权信息，保留
                        copyright_credits.append(credit)
                        logger.info(f"[{process_id}] 保留版权信息credit: '{text}'")
                    else:
                        # 这是其他信息，移除
                        credits_to_remove.append(credit)
                else:
                    # 空的credit元素，移除
                    credits_to_remove.append(credit)

            # 只移除非版权信息的credit元素
            for credit in credits_to_remove:
                root.remove(credit)

            # 然后移除可能嵌入在XML开头的credit元素（Audiveris有时会这样做）
            # 检查root的直接子元素，移除所有credit相关的内容
            elements_to_remove = []
            for child in root:
                if child.tag == 'credit':
                    elements_to_remove.append(child)
                elif child.tag in ['work', 'movement-title'] and child.tail:
                    # 检查是否有嵌入的credit信息
                    if 'credit' in child.tail:
                        child.tail = None

            for element in elements_to_remove:
                root.remove(element)

            # 特殊处理：移除可能嵌入在work元素后面的credit信息
            work = root.find('work')
            if work is not None and work.tail:
                # 清除work元素后面可能的credit信息
                work.tail = None

            logger.info(f"[{process_id}] 已移除 {len(credits_to_remove) + len(elements_to_remove)} 个非版权信息credit元素，保留了 {len(copyright_credits)} 个版权信息credit元素")

            # 3. 保留Audiveris的标题信息，不添加写死的信息
            # 如果需要添加标题信息，应该通过TextIntegrationEngine来处理
            logger.info(f"[{process_id}] 保留Audiveris原有的标题信息，不添加写死内容")

            # 4. 保留Audiveris的所有信息，不添加写死内容
            logger.info(f"[{process_id}] 保留Audiveris原有的所有信息")

            # 5. 保留Audiveris的所有信息，不修改任何内容
            logger.info(f"[{process_id}] 保留Audiveris原有的所有信息，不做任何修改")

            # 6. 保存为enhanced.xml
            enhanced_xml_path = xml_path.replace('.xml', '_enhanced.xml')
            tree.write(enhanced_xml_path, encoding='utf-8', xml_declaration=True)
            logger.info(f"[{process_id}] 保存自定义样式XML文件: {enhanced_xml_path}")

            # 7. 转换为MXL格式
            enhanced_mxl_path = self._xml_to_mxl(enhanced_xml_path, process_id)

            logger.info(f"[{process_id}] 自定义样式应用完成: enhanced_xml={enhanced_xml_path}, enhanced_mxl={enhanced_mxl_path}")

            return {
                'enhanced_xml': enhanced_xml_path,
                'enhanced_mxl': enhanced_mxl_path
            }

        except Exception as e:
            logger.error(f"[{process_id}] 应用自定义样式失败: {str(e)}")
            # 如果失败，返回原始XML转换的MXL
            enhanced_mxl_path = self._xml_to_mxl(xml_path, process_id)
            return {
                'enhanced_xml': xml_path,
                'enhanced_mxl': enhanced_mxl_path
            }

    def _xml_to_mxl(self, xml_path, process_id):
        """
        将XML文件转换为MXL格式
        """
        import zipfile
        import os

        logger.info(f"[{process_id}] 转换XML到MXL: {xml_path}")

        try:
            # 生成MXL文件路径
            mxl_path = xml_path.replace('.xml', '.mxl')

            # 创建MXL文件（实际上是一个ZIP文件）
            with zipfile.ZipFile(mxl_path, 'w', zipfile.ZIP_DEFLATED) as mxl_file:
                # 添加XML文件到MXL中
                mxl_file.write(xml_path, os.path.basename(xml_path))

                # 创建META-INF/container.xml
                container_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<container>
    <rootfiles>
        <rootfile full-path="{}" media-type="application/vnd.recordare.musicxml+xml"/>
    </rootfiles>
</container>'''.format(os.path.basename(xml_path))

                mxl_file.writestr('META-INF/container.xml', container_xml)

            logger.info(f"[{process_id}] XML转MXL完成: {mxl_path}")
            return mxl_path

        except Exception as e:
            logger.error(f"[{process_id}] XML转MXL失败: {str(e)}")
            return xml_path  # 返回原始XML文件






    
    def get_engine_status(self):
        """获取各引擎状态"""
        return {
            'audiveris': {
                'available': self.audiveris.is_available(),
                'version': self.audiveris.get_version(),
                'path': self.audiveris.audiveris_path
            },
            'musescore': {
                'available': self.musescore.is_available(),
                'version': self.musescore.get_version(),
                'path': self.musescore.musescore_path
            },
            'image_processor': {
                'available': True,
                'opencv_version': self.image_processor.get_opencv_version()
            }
        }
    
    def cleanup_temp_files(self, process_id):
        """清理临时文件"""
        try:
            from flask import current_app
            temp_folder = current_app.config.get('TEMP_FOLDER')
            
            if not temp_folder or not os.path.exists(temp_folder):
                return
            
            # 删除与process_id相关的临时文件
            for file in os.listdir(temp_folder):
                if process_id in file:
                    file_path = os.path.join(temp_folder, file)
                    try:
                        os.remove(file_path)
                        logger.info(f"删除临时文件: {file_path}")
                    except Exception as e:
                        logger.warning(f"删除临时文件失败 {file_path}: {str(e)}")
                        
        except Exception as e:
            logger.error(f"清理临时文件异常: {str(e)}")

    def _should_retry_with_complex_mode(self, ocr_result):
        """
        判断是否应该使用复杂模式重新识别

        Args:
            ocr_result: OCR识别结果

        Returns:
            bool: 是否需要重试
        """
        try:
            # 检查是否有MusicXML文件生成
            if not ocr_result.get('success', False):
                return True

            xml_path = ocr_result.get('xml_path')
            if not xml_path or not os.path.exists(xml_path):
                return True

            # 检查XML文件大小（太小可能识别不完整）
            file_size = os.path.getsize(xml_path)
            if file_size < 5000:  # 小于5KB可能识别不完整
                logger.info(f"XML文件过小 ({file_size} bytes)，可能识别不完整")
                return True

            # 检查XML内容是否包含足够的音符
            try:
                with open(xml_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 统计音符数量
                note_count = content.count('<note>')
                if note_count < 10:  # 音符数量太少
                    logger.info(f"识别到的音符数量过少 ({note_count})，可能识别不完整")
                    return True

                # 检查是否有节奏错误标记（通过日志文件）- 更严格的检测
                log_path = xml_path.replace('.xml', '.log')
                if os.path.exists(log_path):
                    with open(log_path, 'r', encoding='utf-8') as f:
                        log_content = f.read()

                        # 统计各种错误
                        excess_count = log_content.count('excess:')
                        rhythm_error_count = log_content.count('no correct rhythm')
                        timeoffset_error_count = log_content.count('No timeOffset')

                        total_errors = excess_count + rhythm_error_count + timeoffset_error_count

                        logger.info(f"节奏错误统计: excess={excess_count}, rhythm={rhythm_error_count}, timeoffset={timeoffset_error_count}, total={total_errors}")

                        # 如果有任何节奏错误，都尝试复杂模式
                        if total_errors > 0:
                            logger.info(f"检测到{total_errors}个节奏问题，建议使用复杂模式")
                            return True

            except Exception as e:
                logger.warning(f"检查XML内容时出错: {e}")
                return True

            return False

        except Exception as e:
            logger.error(f"判断是否需要复杂模式时出错: {e}")
            return True

    def _has_severe_rhythm_errors(self, ocr_result):
        """
        检测是否有严重的节奏错误

        Args:
            ocr_result: OCR识别结果

        Returns:
            bool: 是否有严重节奏错误
        """
        try:
            # 检查日志文件中的节奏错误
            if not ocr_result.get('success', False):
                return True

            xml_path = ocr_result.get('xml_path')
            if not xml_path:
                return True

            # 检查对应的日志文件
            log_path = xml_path.replace('.xml', '.log')
            if not os.path.exists(log_path):
                return False

            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    log_content = f.read()

                # 统计各种错误
                excess_errors = log_content.count('excess:')
                rhythm_errors = log_content.count('no correct rhythm')
                timeoffset_errors = log_content.count('No timeOffset')

                # 如果错误数量过多，认为是严重节奏错误
                total_errors = excess_errors + rhythm_errors + timeoffset_errors

                logger.info(f"节奏错误统计: excess={excess_errors}, rhythm={rhythm_errors}, timeoffset={timeoffset_errors}, total={total_errors}")

                # 降低阈值，更容易触发极限模式
                if total_errors > 5 or rhythm_errors > 2:
                    logger.info("检测到严重节奏错误，建议使用极限模式")
                    return True

            except Exception as e:
                logger.warning(f"读取日志文件失败: {e}")
                return False

            return False

        except Exception as e:
            logger.error(f"检测节奏错误时出错: {e}")
            return True

    # 注意：_add_visible_credits 和 _create_credit_element 函数已被移除
    # 现在使用 TextIntegrationEngine 来处理所有文字集成，避免重复添加credit元素
