"""
OMR识别配置管理
"""

class OMRConfig:
    """OMR识别配置类"""
    
    # 基础配置
    BASE_OPTIONS = [
        # 基础图像处理设置
        ('org.audiveris.omr.sheet.Scale.interline', '20'),  # 设置五线谱间距
        ('org.audiveris.omr.sheet.Picture.maxForegroundGrayLevel', '200'),  # 提高前景检测阈值
        ('org.audiveris.omr.sheet.Picture.binarizationMethod', 'GLOBAL'),  # 使用全局二值化
    ]
    
    # 标准模式配置 - 平衡准确性和识别率
    STANDARD_OPTIONS = [
        # 音符头识别优化
        ('org.audiveris.omr.glyph.classifier.BasicClassifier.minGrade', '0.3'),
        ('org.audiveris.omr.sheet.header.StaffHeader.minHeadGrade', '0.3'),
        ('org.audiveris.omr.sheet.beam.BeamGroup.maxSlopeGap', '0.5'),
        
        # 节奏识别优化
        ('org.audiveris.omr.sheet.rhythm.MeasureRhythm.maxVoiceExcess', '0.5'),
        ('org.audiveris.omr.sheet.rhythm.Voice.maxExcess', '0.5'),
        ('org.audiveris.omr.sheet.rhythm.Rhythm.useVoiceSeeds', 'true'),
        ('org.audiveris.omr.sheet.rhythm.Rhythm.maxDurationRatio', '8.0'),
        ('org.audiveris.omr.sheet.rhythm.Rhythm.minNoteDuration', '0.0625'),  # 64分音符
        
        # 复杂音符和装饰音处理
        ('org.audiveris.omr.sheet.stem.StemSeed.maxAdjacency', '3'),
        ('org.audiveris.omr.sheet.curve.SlurInter.maxSlope', '2.0'),
        ('org.audiveris.omr.sheet.symbol.SmallBeamInter.minLength', '10'),
        
        # 多声部处理优化
        ('org.audiveris.omr.sheet.rhythm.Voice.maxOverlap', '0.25'),
        ('org.audiveris.omr.sheet.rhythm.MeasureRhythm.maxTimeOffset', '0.25'),
        
        # 符号识别优化
        ('org.audiveris.omr.sheet.symbol.InterEnsemble.maxMemberGap', '20'),
        ('org.audiveris.omr.glyph.Glyph.minWeight', '50'),
    ]
    
    # 复杂模式配置 - 最大化识别率，适用于复杂乐谱
    COMPLEX_OPTIONS = [
        # 音符头识别优化（更宽松）
        ('org.audiveris.omr.glyph.classifier.BasicClassifier.minGrade', '0.15'),  # 进一步降低
        ('org.audiveris.omr.sheet.header.StaffHeader.minHeadGrade', '0.15'),
        ('org.audiveris.omr.sheet.beam.BeamGroup.maxSlopeGap', '1.5'),  # 增加

        # 节奏识别优化（极限宽松）
        ('org.audiveris.omr.sheet.rhythm.MeasureRhythm.maxVoiceExcess', '2.0'),  # 大幅增加
        ('org.audiveris.omr.sheet.rhythm.Voice.maxExcess', '2.0'),  # 大幅增加
        ('org.audiveris.omr.sheet.rhythm.Rhythm.useVoiceSeeds', 'true'),
        ('org.audiveris.omr.sheet.rhythm.Rhythm.maxDurationRatio', '32.0'),  # 大幅增加
        ('org.audiveris.omr.sheet.rhythm.Rhythm.minNoteDuration', '0.015625'),  # 256分音符
        ('org.audiveris.omr.sheet.rhythm.MeasureRhythm.maxTimeOffset', '1.0'),  # 大幅增加

        # 复杂音符和装饰音处理（极限宽松）
        ('org.audiveris.omr.sheet.stem.StemSeed.maxAdjacency', '8'),  # 大幅增加
        ('org.audiveris.omr.sheet.curve.SlurInter.maxSlope', '5.0'),  # 大幅增加
        ('org.audiveris.omr.sheet.symbol.SmallBeamInter.minLength', '3'),  # 进一步减少

        # 多声部处理优化（极限宽松）
        ('org.audiveris.omr.sheet.rhythm.Voice.maxOverlap', '1.0'),  # 大幅增加
        ('org.audiveris.omr.sheet.rhythm.Voice.maxVoiceCount', '12'),  # 允许更多声部

        # 符号识别优化（极限宽松）
        ('org.audiveris.omr.sheet.symbol.InterEnsemble.maxMemberGap', '50'),  # 大幅增加
        ('org.audiveris.omr.glyph.Glyph.minWeight', '20'),  # 进一步降低

        # 额外的复杂乐谱处理选项
        ('org.audiveris.omr.sheet.rhythm.Rhythm.allowPartialMeasures', 'true'),
        ('org.audiveris.omr.sheet.rhythm.Voice.allowIncompleteVoices', 'true'),
        ('org.audiveris.omr.sheet.rhythm.Rhythm.ignoreRhythmErrors', 'true'),  # 忽略节奏错误
        ('org.audiveris.omr.sheet.rhythm.MeasureRhythm.forceComplete', 'false'),  # 不强制完整小节
    ]
    
    # 保守模式配置 - 最大化准确性，适用于简单清晰的乐谱
    CONSERVATIVE_OPTIONS = [
        # 音符头识别优化（严格）
        ('org.audiveris.omr.glyph.classifier.BasicClassifier.minGrade', '0.6'),
        ('org.audiveris.omr.sheet.header.StaffHeader.minHeadGrade', '0.6'),
        ('org.audiveris.omr.sheet.beam.BeamGroup.maxSlopeGap', '0.3'),

        # 节奏识别优化（严格）
        ('org.audiveris.omr.sheet.rhythm.MeasureRhythm.maxVoiceExcess', '0.125'),
        ('org.audiveris.omr.sheet.rhythm.Voice.maxExcess', '0.125'),
        ('org.audiveris.omr.sheet.rhythm.Rhythm.useVoiceSeeds', 'true'),
        ('org.audiveris.omr.sheet.rhythm.Rhythm.maxDurationRatio', '4.0'),
        ('org.audiveris.omr.sheet.rhythm.Rhythm.minNoteDuration', '0.125'),  # 8分音符

        # 符号识别优化（严格）
        ('org.audiveris.omr.sheet.symbol.InterEnsemble.maxMemberGap', '15'),
        ('org.audiveris.omr.glyph.Glyph.minWeight', '80'),
    ]

    # 极限模式配置 - 专门针对极其复杂的乐谱，但保持可用性
    EXTREME_OPTIONS = [
        # 音符头识别优化（极限宽松但不过度）
        ('org.audiveris.omr.glyph.classifier.BasicClassifier.minGrade', '0.12'),  # 比复杂模式稍低
        ('org.audiveris.omr.sheet.header.StaffHeader.minHeadGrade', '0.12'),
        ('org.audiveris.omr.sheet.beam.BeamGroup.maxSlopeGap', '2.0'),

        # 节奏识别优化（极大宽松但保持稳定）
        ('org.audiveris.omr.sheet.rhythm.MeasureRhythm.maxVoiceExcess', '3.0'),  # 比复杂模式更宽松
        ('org.audiveris.omr.sheet.rhythm.Voice.maxExcess', '3.0'),
        ('org.audiveris.omr.sheet.rhythm.Rhythm.useVoiceSeeds', 'true'),  # 保持启用以维持稳定性
        ('org.audiveris.omr.sheet.rhythm.Rhythm.maxDurationRatio', '50.0'),  # 很大但不极端
        ('org.audiveris.omr.sheet.rhythm.Rhythm.minNoteDuration', '0.01'),  # 很小但不极端
        ('org.audiveris.omr.sheet.rhythm.MeasureRhythm.maxTimeOffset', '2.0'),  # 比复杂模式更宽松
        ('org.audiveris.omr.sheet.rhythm.MeasureRhythm.forceComplete', 'false'),

        # 允许不完整但不完全跳过检查
        ('org.audiveris.omr.sheet.rhythm.Rhythm.allowPartialMeasures', 'true'),
        ('org.audiveris.omr.sheet.rhythm.Voice.allowIncompleteVoices', 'true'),

        # 符号识别优化（宽松但合理）
        ('org.audiveris.omr.sheet.symbol.InterEnsemble.maxMemberGap', '80'),  # 比复杂模式更宽松
        ('org.audiveris.omr.glyph.Glyph.minWeight', '15'),  # 比复杂模式稍低
        ('org.audiveris.omr.sheet.stem.StemSeed.maxAdjacency', '12'),  # 比复杂模式更宽松
        ('org.audiveris.omr.sheet.curve.SlurInter.maxSlope', '8.0'),  # 比复杂模式更宽松

        # 多声部处理（宽松但合理）
        ('org.audiveris.omr.sheet.rhythm.Voice.maxOverlap', '2.0'),  # 比复杂模式更宽松
        ('org.audiveris.omr.sheet.rhythm.Voice.maxVoiceCount', '16'),  # 比复杂模式更多
    ]
    
    @classmethod
    def get_options(cls, mode='standard'):
        """
        获取指定模式的配置选项

        Args:
            mode: 模式名称 ('standard', 'complex', 'conservative', 'extreme')

        Returns:
            list: 配置选项列表
        """
        options = cls.BASE_OPTIONS.copy()

        if mode == 'complex':
            options.extend(cls.COMPLEX_OPTIONS)
        elif mode == 'conservative':
            options.extend(cls.CONSERVATIVE_OPTIONS)
        elif mode == 'extreme':
            options.extend(cls.EXTREME_OPTIONS)
        else:  # standard
            options.extend(cls.STANDARD_OPTIONS)

        return options
    
    @classmethod
    def build_command_options(cls, mode='standard'):
        """
        构建命令行选项
        
        Args:
            mode: 模式名称
            
        Returns:
            list: 命令行选项列表
        """
        options = cls.get_options(mode)
        cmd_options = []
        
        for key, value in options:
            cmd_options.extend(['-option', f'{key}={value}'])
            
        return cmd_options
    
    @classmethod
    def get_mode_description(cls, mode):
        """获取模式描述"""
        descriptions = {
            'standard': '标准模式 - 平衡准确性和识别率，适用于大多数乐谱',
            'complex': '复杂模式 - 最大化识别率，适用于复杂乐谱和装饰音较多的乐谱',
            'conservative': '保守模式 - 最大化准确性，适用于简单清晰的乐谱',
            'extreme': '极限模式 - 忽略大部分约束，专门处理极其复杂的乐谱'
        }
        return descriptions.get(mode, '未知模式')

# 预设配置
PRESET_CONFIGS = {
    'piano_classical': {
        'mode': 'standard',
        'description': '古典钢琴乐谱',
        'additional_options': [
            ('org.audiveris.omr.sheet.rhythm.Voice.maxVoiceCount', '4'),  # 最多4个声部
        ]
    },
    
    'piano_romantic': {
        'mode': 'complex',
        'description': '浪漫派钢琴乐谱（装饰音较多）',
        'additional_options': [
            ('org.audiveris.omr.sheet.rhythm.Voice.maxVoiceCount', '6'),  # 最多6个声部
            ('org.audiveris.omr.sheet.symbol.OrnamentInter.minGrade', '0.3'),  # 装饰音识别
        ]
    },
    
    'simple_melody': {
        'mode': 'conservative',
        'description': '简单旋律乐谱',
        'additional_options': [
            ('org.audiveris.omr.sheet.rhythm.Voice.maxVoiceCount', '2'),  # 最多2个声部
        ]
    }
}
