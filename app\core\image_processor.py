"""
图像预处理模块
使用OpenCV进行乐谱图像优化
"""
import cv2
import numpy as np
import os
import logging
from pathlib import Path
from flask import current_app

logger = logging.getLogger(__name__)

class ImageProcessor:
    """图像预处理器"""

    def __init__(self, config=None):
        if config:
            self.temp_folder = config.get('TEMP_FOLDER', 'temp')
        else:
            self.temp_folder = 'temp'
        
    def preprocess_image(self, image_path, output_name=None, **options):
        """
        预处理图像以优化OCR识别效果
        
        Args:
            image_path (str): 输入图像路径
            output_name (str): 输出文件名
            **options: 处理选项
            
        Returns:
            dict: 处理结果
        """
        try:
            # temp_folder在__init__中已设置
                
            # 转换为绝对路径并验证
            abs_image_path = os.path.abspath(image_path)
            logger.info(f"检查图像文件: {abs_image_path}")

            if not os.path.exists(abs_image_path):
                # 尝试相对路径
                if not os.path.exists(image_path):
                    logger.error(f"图像文件不存在: {image_path} (绝对路径: {abs_image_path})")
                    raise FileNotFoundError(f"输入图像不存在: {image_path}")
                else:
                    abs_image_path = image_path

            # 使用绝对路径
            image_path = abs_image_path
            
            # 检查文件是否存在
            if not os.path.exists(image_path):
                raise ValueError(f"图像文件不存在: {image_path}")

            # 检查文件大小
            file_size = os.path.getsize(image_path)
            if file_size == 0:
                raise ValueError(f"图像文件为空: {image_path}")

            # 检查文件大小是否合理（至少1KB）
            if file_size < 1024:
                raise ValueError(f"图像文件太小（{file_size} 字节），可能不是有效的图像文件: {image_path}")

            logger.info(f"图像文件大小: {file_size} 字节")

            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图像文件（可能格式不支持或文件损坏）: {image_path}")
            
            logger.info(f"原始图像尺寸: {image.shape}")
            logger.info(f"接收到的预处理选项: {options}")

            # 预处理步骤
            processed_image = image.copy()

            # 1. 转换为灰度图
            if len(processed_image.shape) == 3:
                processed_image = cv2.cvtColor(processed_image, cv2.COLOR_BGR2GRAY)
            
            # 检查是否需要预处理
            basic_preprocessing_enabled = (
                options.get('deskew', False) or
                options.get('denoise', False) or
                options.get('enhance_contrast', False)
            )

            watermark_only = options.get('remove_watermark', False) and not basic_preprocessing_enabled

            logger.info(f"基础预处理选项启用状态: {basic_preprocessing_enabled}")
            logger.info(f"仅水印处理模式: {watermark_only}")
            logger.info(f"具体选项: deskew={options.get('deskew', False)}, denoise={options.get('denoise', False)}, enhance_contrast={options.get('enhance_contrast', False)}, remove_watermark={options.get('remove_watermark', False)}")

            # 如果用户没有启用任何预处理选项，直接复制原图
            if not basic_preprocessing_enabled and not options.get('remove_watermark', False):
                logger.info("用户未启用任何预处理选项，直接使用原图")
                return self._copy_original_image(image_path, output_name)

            # 2. 倾斜校正
            if options.get('deskew', False):
                processed_image = self._deskew_image(processed_image)

            # 3. 噪点移除
            if options.get('denoise', False):
                processed_image = self._denoise_image(processed_image)

            # 4. 对比度增强
            if options.get('enhance_contrast', False):
                processed_image = self._enhance_contrast(processed_image)

            # 5. 水印移除（实验性功能）
            if options.get('remove_watermark', False):
                processed_image = self._remove_watermark(processed_image)

            # 6. 二值化（OCR需要，但只在有基础预处理时执行）
            # 如果只是水印移除，跳过二值化以保持图像质量
            if options.get('binarize', True) and not watermark_only:
                logger.info("执行二值化处理")
                processed_image = self._binarize_image(processed_image)
            elif watermark_only:
                logger.info("仅水印处理模式，跳过二值化以保持图像质量")

            # 7. 形态学操作（OCR需要，但只在有基础预处理时执行）
            # 如果只是水印移除，跳过形态学操作以保持图像质量
            if options.get('morphology', True) and not watermark_only:
                logger.info("执行形态学操作")
                processed_image = self._apply_morphology(processed_image)
            elif watermark_only:
                logger.info("仅水印处理模式，跳过形态学操作以保持图像质量")
            
            # 保存处理后的图像
            if output_name is None:
                output_name = Path(image_path).stem + '_processed'
            
            output_path = os.path.join(self.temp_folder, f"{output_name}.png")
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            success = cv2.imwrite(output_path, processed_image)
            if not success:
                raise RuntimeError("保存处理后的图像失败")
            
            logger.info(f"图像预处理完成: {output_path}")

            # 如果进行了水印处理，保存最终对比图
            if options.get('remove_watermark', False):
                self._save_final_comparison(image, processed_image, "final_processing_comparison")

            return {
                'success': True,
                'output_path': output_path,
                'original_size': image.shape,
                'processed_size': processed_image.shape,
                'steps_applied': [k for k, v in options.items() if v],
                'watermark_only_mode': watermark_only
            }
            
        except Exception as e:
            logger.error(f"图像预处理失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _copy_original_image(self, image_path, output_name=None):
        """直接复制原图，不进行任何预处理"""
        try:
            if output_name is None:
                output_name = Path(image_path).stem + '_processed'

            output_path = os.path.join(self.temp_folder, f"{output_name}.png")

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 检查文件是否存在
            if not os.path.exists(image_path):
                raise ValueError(f"图像文件不存在: {image_path}")

            # 读取原图并直接保存（保持原始质量）
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图像文件（可能格式不支持或文件损坏）: {image_path}")

            success = cv2.imwrite(output_path, image)
            if not success:
                raise RuntimeError("保存原始图像失败")

            logger.info(f"原图复制完成: {output_path}")

            return {
                'success': True,
                'output_path': output_path,
                'original_size': image.shape,
                'processed_size': image.shape,
                'steps_applied': []  # 没有应用任何处理步骤
            }

        except Exception as e:
            logger.error(f"复制原图失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _deskew_image(self, image):
        """倾斜校正"""
        try:
            # 使用霍夫变换检测直线
            edges = cv2.Canny(image, 50, 150, apertureSize=3)
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
            
            if lines is not None:
                angles = []
                for rho, theta in lines[:20]:  # 只考虑前20条线
                    angle = theta * 180 / np.pi
                    if angle < 45:
                        angles.append(angle)
                    elif angle > 135:
                        angles.append(angle - 180)
                
                if angles:
                    # 计算平均角度
                    median_angle = np.median(angles)
                    
                    # 如果倾斜角度超过阈值，进行校正
                    if abs(median_angle) > 0.5:
                        logger.info(f"检测到倾斜角度: {median_angle:.2f}°")
                        
                        # 旋转图像
                        (h, w) = image.shape[:2]
                        center = (w // 2, h // 2)
                        rotation_matrix = cv2.getRotationMatrix2D(center, median_angle, 1.0)
                        
                        # 计算新的边界框
                        cos = np.abs(rotation_matrix[0, 0])
                        sin = np.abs(rotation_matrix[0, 1])
                        new_w = int((h * sin) + (w * cos))
                        new_h = int((h * cos) + (w * sin))
                        
                        # 调整旋转中心
                        rotation_matrix[0, 2] += (new_w / 2) - center[0]
                        rotation_matrix[1, 2] += (new_h / 2) - center[1]
                        
                        # 执行旋转
                        rotated = cv2.warpAffine(image, rotation_matrix, (new_w, new_h), 
                                               flags=cv2.INTER_CUBIC, 
                                               borderMode=cv2.BORDER_REPLICATE)
                        return rotated
            
            return image
            
        except Exception as e:
            logger.warning(f"倾斜校正失败: {str(e)}")
            return image
    
    def _denoise_image(self, image):
        """噪点移除"""
        try:
            # 使用非局部均值去噪
            denoised = cv2.fastNlMeansDenoising(image, None, 10, 7, 21)
            return denoised
        except Exception as e:
            logger.warning(f"去噪失败: {str(e)}")
            return image
    
    def _enhance_contrast(self, image):
        """对比度增强"""
        try:
            # 使用CLAHE (Contrast Limited Adaptive Histogram Equalization)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(image)
            return enhanced
        except Exception as e:
            logger.warning(f"对比度增强失败: {str(e)}")
            return image

    def _remove_watermark(self, image):
        """水印移除（实验性功能）- 质量优先版本"""
        try:
            logger.info("开始质量优先的水印移除处理...")

            # 保存原图副本
            original = image.copy()

            # 首先尝试最温和的处理
            processed_image = self._remove_watermark_gentle(image)

            # 严格的质量检查
            if self._is_image_too_blurred(processed_image, original):
                logger.warning("水印移除导致质量下降，使用原图以确保OCR识别准确性")
                return original

            # 额外检查：确保音符区域没有被过度处理
            if self._check_music_content_integrity(processed_image, original):
                logger.info("水印淡化完成，音符内容完整性良好")

                # 保存处理前后的对比图（用于调试）
                self._save_watermark_comparison(original, processed_image)

                return processed_image
            else:
                logger.warning("检测到音符内容可能受损，使用原图")
                return original

        except Exception as e:
            logger.warning(f"水印移除失败: {str(e)}")
            return image

    def _check_music_content_integrity(self, processed, original):
        """检查音乐内容的完整性"""
        try:
            # 计算深色像素（音符）的比例
            def count_dark_pixels(img, threshold=128):
                return np.sum(img < threshold)

            original_dark = count_dark_pixels(original)
            processed_dark = count_dark_pixels(processed)

            # 如果深色像素减少超过15%，可能音符受损
            dark_pixel_ratio = processed_dark / original_dark if original_dark > 0 else 1

            logger.info(f"音符像素保持率: {dark_pixel_ratio:.3f}")

            # 保持率应该在85%以上，更严格地保护音符
            if dark_pixel_ratio < 0.85:
                logger.warning(f"音符内容可能受损，像素保持率仅为 {dark_pixel_ratio:.1%}")
                return False

            return True

        except Exception as e:
            logger.warning(f"音乐内容完整性检查失败: {str(e)}")
            return True  # 检查失败时默认认为完整

    def _save_watermark_comparison(self, original, processed):
        """保存水印处理前后的对比图（用于调试）"""
        try:
            # 创建对比图：左边原图，右边处理后
            height, width = original.shape
            comparison = np.zeros((height, width * 2), dtype=np.uint8)

            # 左半部分：原图
            comparison[:, :width] = original

            # 右半部分：处理后
            comparison[:, width:] = processed

            # 添加分割线
            comparison[:, width-1:width+1] = 128

            # 保存对比图
            comparison_path = os.path.join(self.temp_folder, 'watermark_comparison.png')
            cv2.imwrite(comparison_path, comparison)

            logger.info(f"水印处理对比图已保存: {comparison_path}")

        except Exception as e:
            logger.warning(f"保存对比图失败: {str(e)}")

    def _save_final_comparison(self, original, final_result, filename_prefix):
        """保存最终处理结果的对比图"""
        try:
            # 创建对比图：左边原图，右边最终结果
            height, width = original.shape
            comparison = np.zeros((height, width * 2), dtype=np.uint8)

            # 左半部分：原图
            comparison[:, :width] = original

            # 右半部分：最终结果
            comparison[:, width:] = final_result

            # 添加分割线
            comparison[:, width-1:width+1] = 128

            # 保存对比图
            comparison_path = os.path.join(self.temp_folder, f'{filename_prefix}.png')
            cv2.imwrite(comparison_path, comparison)

            logger.info(f"最终处理对比图已保存: {comparison_path}")

        except Exception as e:
            logger.warning(f"保存最终对比图失败: {str(e)}")

    def _remove_watermark_frequency_domain(self, image):
        """基于频域的水印移除"""
        try:
            # 傅里叶变换
            f_transform = np.fft.fft2(image)
            f_shift = np.fft.fftshift(f_transform)

            # 创建高通滤波器，移除低频重复模式
            rows, cols = image.shape
            crow, ccol = rows // 2, cols // 2

            # 创建掩码
            mask = np.ones((rows, cols), np.uint8)
            r = 30  # 滤波器半径
            center = [crow, ccol]
            x, y = np.ogrid[:rows, :cols]
            mask_area = (x - center[0]) ** 2 + (y - center[1]) ** 2 <= r*r
            mask[mask_area] = 0

            # 应用滤波器
            f_shift_filtered = f_shift * mask

            # 逆变换
            f_ishift = np.fft.ifftshift(f_shift_filtered)
            img_back = np.fft.ifft2(f_ishift)
            img_back = np.abs(img_back)

            return np.uint8(img_back)

        except Exception as e:
            logger.warning(f"频域水印移除失败: {str(e)}")
            return image

    def _remove_small_components(self, image):
        """移除小的连通区域"""
        try:
            # 二值化
            _, binary = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 查找连通区域
            num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary, connectivity=8)

            # 计算平均区域大小
            areas = stats[1:, cv2.CC_STAT_AREA]  # 排除背景
            if len(areas) > 0:
                mean_area = np.mean(areas)
                # 移除小于平均面积1/10的区域（可能是水印）
                min_area = max(50, mean_area / 10)

                # 创建掩码
                mask = np.zeros_like(binary)
                for i in range(1, num_labels):
                    if stats[i, cv2.CC_STAT_AREA] >= min_area:
                        mask[labels == i] = 255

                # 应用掩码到原图
                result = cv2.bitwise_and(image, mask)
                return result

            return image

        except Exception as e:
            logger.warning(f"小区域移除失败: {str(e)}")
            return image

    def _remove_watermark_by_color(self, image):
        """基于颜色的水印移除"""
        try:
            # 检测并移除浅色水印（通常是灰色或浅色）
            # 创建掩码，保留深色区域（音符）
            mask = cv2.threshold(image, 200, 255, cv2.THRESH_BINARY_INV)[1]

            # 形态学操作，连接断开的音符
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)

            # 应用掩码
            result = cv2.bitwise_and(image, mask)

            # 将背景设为白色
            result[mask == 0] = 255

            return result

        except Exception as e:
            logger.warning(f"颜色水印移除失败: {str(e)}")
            return image

    def _remove_watermark_gentle(self, image):
        """精准的水印移除方法 - 专门针对浅色水印"""
        try:
            logger.info("使用精准方法移除浅色水印...")

            # 保存原图副本
            original = image.copy()

            # 方法1: 识别和淡化浅色水印
            # 计算图像统计信息
            mean_val = np.mean(image)
            std_val = np.std(image)

            logger.info(f"图像统计: 均值={mean_val:.2f}, 标准差={std_val:.2f}")

            # 识别水印区域：通常水印比背景稍暗，但比音符浅很多
            # 设定水印检测范围：比平均值稍暗到比平均值稍亮
            watermark_lower = mean_val - std_val * 0.3
            watermark_upper = mean_val + std_val * 0.8

            # 创建水印掩码
            watermark_mask = ((image >= watermark_lower) & (image <= watermark_upper)).astype(np.uint8)

            # 排除明显的音符区域（很暗的区域）
            # 调整阈值，更好地保护音符
            music_threshold = mean_val - std_val * 0.8  # 从1.2调整为0.8，更保守地保护音符
            music_mask = (image < music_threshold).astype(np.uint8)

            # 保护乐谱边缘区域（行末音符容易被误删）
            h, w = image.shape
            edge_protection = np.zeros_like(image, dtype=np.uint8)
            # 保护右边缘20%的区域
            edge_protection[:, int(w*0.8):] = 1

            # 最终水印掩码：在水印范围内但不是音符且不在保护区域的区域
            final_watermark_mask = watermark_mask * (1 - music_mask) * (1 - edge_protection)

            logger.info(f"水印检测范围: {watermark_lower:.1f} - {watermark_upper:.1f}")
            logger.info(f"音符阈值: {music_threshold:.1f}")

            # 方法2: 对水印区域进行亮化处理（让水印接近背景色）
            result = original.copy().astype(np.float32)

            # 计算背景色（图像中最亮的区域的平均值）
            background_pixels = image[image > mean_val + std_val]
            if len(background_pixels) > 0:
                background_color = np.mean(background_pixels)
            else:
                background_color = 255  # 默认白色背景

            logger.info(f"检测到的背景色: {background_color:.1f}")

            # 对水印区域进行渐进式亮化
            watermark_pixels = final_watermark_mask > 0
            if np.any(watermark_pixels):
                # 计算亮化强度：让水印像素向背景色靠近
                current_values = result[watermark_pixels]
                target_values = background_color

                # 渐进式亮化：不是直接替换，而是向背景色移动70%
                lightening_factor = 0.7
                new_values = current_values + (target_values - current_values) * lightening_factor
                result[watermark_pixels] = new_values

                logger.info(f"处理了 {np.sum(watermark_pixels)} 个水印像素")

            # 方法3: 轻微的高斯模糊来平滑处理边界
            result = cv2.GaussianBlur(result, (3, 3), 0.5)

            result = np.clip(result, 0, 255).astype(np.uint8)

            logger.info("精准水印移除处理完成")
            return result

        except Exception as e:
            logger.warning(f"精准水印移除失败: {str(e)}")
            return image

    def _remove_isolated_pixels(self, binary_image, min_area=10):
        """移除孤立的小像素点，但保留重要的音符部分"""
        try:
            # 查找连通区域
            num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(
                255 - binary_image, connectivity=8)  # 注意：反转图像，因为我们要分析黑色区域

            # 创建输出图像
            result = binary_image.copy()

            # 移除面积小于min_area的区域
            for i in range(1, num_labels):  # 跳过背景标签0
                area = stats[i, cv2.CC_STAT_AREA]
                if area < min_area:
                    # 将小区域设为白色（移除）
                    result[labels == i] = 255

            return result

        except Exception as e:
            logger.warning(f"孤立像素移除失败: {str(e)}")
            return binary_image

    def _is_image_too_blurred(self, processed, original):
        """检查图像是否过度模糊或质量下降"""
        try:
            # 方法1: 计算拉普拉斯方差来评估清晰度
            def calculate_sharpness(img):
                laplacian = cv2.Laplacian(img, cv2.CV_64F)
                return laplacian.var()

            # 方法2: 计算梯度幅值来评估边缘信息
            def calculate_edge_strength(img):
                grad_x = cv2.Sobel(img, cv2.CV_64F, 1, 0, ksize=3)
                grad_y = cv2.Sobel(img, cv2.CV_64F, 0, 1, ksize=3)
                gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
                return np.mean(gradient_magnitude)

            original_sharpness = calculate_sharpness(original)
            processed_sharpness = calculate_sharpness(processed)

            original_edges = calculate_edge_strength(original)
            processed_edges = calculate_edge_strength(processed)

            # 计算质量比例
            sharpness_ratio = processed_sharpness / original_sharpness if original_sharpness > 0 else 1
            edge_ratio = processed_edges / original_edges if original_edges > 0 else 1

            logger.info(f"清晰度对比: 原图={original_sharpness:.2f}, 处理后={processed_sharpness:.2f}, 比例={sharpness_ratio:.3f}")
            logger.info(f"边缘强度对比: 原图={original_edges:.2f}, 处理后={processed_edges:.2f}, 比例={edge_ratio:.3f}")

            # 如果清晰度或边缘信息下降超过50%，认为质量下降过多
            quality_degraded = sharpness_ratio < 0.5 or edge_ratio < 0.5

            if quality_degraded:
                logger.warning("检测到图像质量显著下降，建议使用原图")

            return quality_degraded

        except Exception as e:
            logger.warning(f"清晰度检查失败: {str(e)}")
            return False
    
    def _binarize_image(self, image):
        """二值化"""
        try:
            # 使用自适应阈值
            binary = cv2.adaptiveThreshold(
                image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            return binary
        except Exception as e:
            logger.warning(f"二值化失败: {str(e)}")
            return image
    
    def _apply_morphology(self, image):
        """形态学操作"""
        try:
            # 创建结构元素
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            
            # 开运算：去除小噪点
            opened = cv2.morphologyEx(image, cv2.MORPH_OPEN, kernel)
            
            # 闭运算：填充小空洞
            closed = cv2.morphologyEx(opened, cv2.MORPH_CLOSE, kernel)
            
            return closed
        except Exception as e:
            logger.warning(f"形态学操作失败: {str(e)}")
            return image
    
    def resize_image(self, image_path, max_width=2000, max_height=2000):
        """调整图像尺寸"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("无法读取图像")
            
            h, w = image.shape[:2]
            
            # 计算缩放比例
            scale_w = max_width / w if w > max_width else 1
            scale_h = max_height / h if h > max_height else 1
            scale = min(scale_w, scale_h)
            
            if scale < 1:
                new_w = int(w * scale)
                new_h = int(h * scale)
                resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
                
                # 保存调整后的图像
                output_path = image_path.replace('.', '_resized.')
                cv2.imwrite(output_path, resized)
                
                return {
                    'success': True,
                    'output_path': output_path,
                    'original_size': (w, h),
                    'new_size': (new_w, new_h),
                    'scale': scale
                }
            
            return {
                'success': True,
                'output_path': image_path,
                'original_size': (w, h),
                'new_size': (w, h),
                'scale': 1.0
            }
            
        except Exception as e:
            logger.error(f"图像尺寸调整失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_opencv_version(self):
        """获取OpenCV版本"""
        return cv2.__version__
