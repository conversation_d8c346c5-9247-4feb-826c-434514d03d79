{% extends "base.html" %}

{% block title %}可视化乐谱编辑器{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">
                <i class="fas fa-edit me-2"></i>可视化乐谱编辑
            </h2>
            
            <!-- 步骤指示器 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="progress-steps">
                        <div class="step completed">
                            <div class="step-number">1</div>
                            <div class="step-label">上传图片</div>
                        </div>
                        <div class="step active">
                            <div class="step-number">2</div>
                            <div class="step-label">确认识别</div>
                        </div>
                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-label">生成指法</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 友好提示 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info shadow-sm">
                <h5><i class="fas fa-search me-2"></i>识别结果检查</h5>
                <div class="row">
                    <div class="col-md-8">
                        <p class="mb-2">
                            <strong>OCR识别可能不够精准，</strong>识别的乐谱可能存在漏掉的音符或音符时值错误的情况。
                            请仔细对比左侧原图和右侧预览，确认识别结果的准确性。
                        </p>
                        <p class="mb-0">
                            <i class="fas fa-hand-pointer me-1"></i>
                            您可以切换到<strong>"编辑"模式</strong>进行修正，或直接点击<strong>"确认并生成指法"</strong>继续。
                        </p>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-eye fa-2x text-primary mb-2"></i>
                            <div class="small text-muted">
                                仔细检查识别结果<br>
                                确保音符准确无误
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 识别统计信息 - 放在顶部 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm" id="stats-info">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>识别统计
                    </h6>
                </div>
                <div class="card-body py-2">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <small class="text-muted">当前调号</small>
                                <div class="h6 mb-0" id="key-display">-</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <small class="text-muted">当前拍号</small>
                                <div class="h6 mb-0" id="time-display">-</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <small class="text-muted">小节数</small>
                                <div class="h6 mb-0" id="measure-count">-</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <small class="text-muted">音符数</small>
                                <div class="h6 mb-0" id="note-count">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 - 原始图片和音符编辑并列 -->
    <div class="row">
        <!-- 左侧：原始图片 -->
        <div class="col-lg-6">
            <div class="card shadow-sm" style="height: 750px;">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-image me-2"></i>原始图片
                    </h6>
                </div>
                <div class="card-body text-center d-flex align-items-center justify-content-center">
                    <img id="original-image" src="" class="img-fluid rounded" alt="原始乐谱" style="max-height: 680px; max-width: 100%;">
                </div>
            </div>
        </div>

        <!-- 右侧：预览和编辑区 -->
        <div class="col-lg-6">

            <!-- 预览/编辑切换区域 -->
            <div class="card shadow-sm" style="height: 750px;">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-music me-2"></i><span id="mode-title">乐谱预览</span>
                    </h6>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-primary active" id="show-preview">
                            <i class="fas fa-eye"></i> 预览
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="show-editor">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                    </div>
                </div>
                <div class="card-body p-0" style="height: 660px; overflow-y: auto;">
                    <!-- 预览区域 -->
                    <div id="preview-area" class="h-100 d-flex align-items-center justify-content-center">
                        <div id="preview-loading" class="d-none text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">生成预览中...</span>
                            </div>
                            <p class="mt-2">正在生成预览...</p>
                        </div>
                        <img id="recognition-preview" src="" class="img-fluid d-none" alt="识别预览"
                             style="max-height: 650px; cursor: pointer;"
                             title="点击图片可查看位置信息">
                        <div id="preview-error" class="alert alert-warning d-none m-3">
                            <i class="fas fa-exclamation-triangle"></i>
                            预览生成失败，请切换到编辑模式进行修改
                        </div>
                    </div>

                    <!-- 编辑区域 -->
                    <div id="editor-area" class="d-none p-3">
                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>编辑提示：</strong>参考左侧原图，选择编辑方式进行修改，完成后点击预览查看效果。
                        </div>

                        <!-- 编辑方式选择 -->
                        <div class="mb-3">
                            <div class="btn-group btn-group-sm w-100" role="group">
                                <button type="button" class="btn btn-primary active" id="visual-edit-mode">
                                    <i class="fas fa-mouse-pointer me-2"></i>可视化编辑
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="xml-edit-mode">
                                    <i class="fas fa-code me-2"></i>XML编辑
                                </button>
                            </div>
                        </div>

                        <!-- 可视化编辑区域 -->
                        <div id="visual-edit-area">
                            <!-- 编辑工具 -->
                            <div class="mb-3">
                                <h6 class="text-primary">
                                    <i class="fas fa-tools me-2"></i>编辑工具
                                </h6>
                                <!-- 无修改时：只显示智能修复按钮 -->
                                <div class="row" id="no-changes-row">
                                    <div class="col-12" id="auto-fix-col">
                                        <button type="button" class="btn btn-info btn-sm w-100" id="auto-fix">
                                            <i class="fas fa-magic me-2"></i>智能修复
                                        </button>
                                    </div>
                                </div>

                                <!-- 有修改时：显示智能修复、保存、撤销三个按钮 -->
                                <div class="row" id="save-undo-buttons-row" style="display: none;">
                                    <div class="col-4" id="auto-fix-col-modified">
                                        <!-- 智能修复按钮会被移动到这里 -->
                                    </div>
                                    <div class="col-4" id="save-changes-col">
                                        <button type="button" class="btn btn-warning btn-sm w-100" id="save-visual-changes">
                                            <i class="fas fa-exclamation-triangle me-2"></i>有修改 - 保存
                                        </button>
                                    </div>
                                    <div class="col-4" id="undo-changes-col">
                                        <button type="button" class="btn btn-danger btn-sm w-100" id="undo-all-changes">
                                            <i class="fas fa-undo me-2"></i>撤销所有修改
                                        </button>
                                    </div>
                                </div>
                                <small class="text-muted mt-2 d-block">
                                    <i class="fas fa-info-circle me-1"></i>
                                    进行修改后会显示"保存"和"撤销"按钮。保存后系统将更新预览
                                </small>
                            </div>



                            <!-- 标题信息编辑 -->
                            <div class="mb-3">
                                <h6 class="text-primary">
                                    <i class="fas fa-heading me-2"></i>标题信息
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label small">主标题</label>
                                        <input type="text" class="form-control form-control-sm" id="main-title" placeholder="请输入主标题">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label small">副标题</label>
                                        <input type="text" class="form-control form-control-sm" id="sub-title" placeholder="请输入副标题">
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-4">
                                        <label class="form-label small">原唱</label>
                                        <input type="text" class="form-control form-control-sm" id="original-artist" placeholder="原唱信息">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label small">编曲</label>
                                        <input type="text" class="form-control form-control-sm" id="arranger" placeholder="编曲信息">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label small">版权</label>
                                        <textarea class="form-control form-control-sm" id="copyright" placeholder="版权信息（支持多行）" rows="2" style="resize: vertical;"></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- 基本信息编辑 -->
                            <div class="mb-3">
                                <h6 class="text-primary">
                                    <i class="fas fa-cog me-2"></i>基本信息
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label small">调号</label>
                                        <select class="form-select form-select-sm" id="key-signature">
                                            <option value="0">C大调</option>
                                            <option value="1">G大调 (1♯)</option>
                                            <option value="2">D大调 (2♯)</option>
                                            <option value="-1">F大调 (1♭)</option>
                                            <option value="-2">Bb大调 (2♭)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label small">拍号</label>
                                        <select class="form-select form-select-sm" id="time-signature">
                                            <option value="4/4">4/4拍</option>
                                            <option value="3/4">3/4拍</option>
                                            <option value="2/4">2/4拍</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 连线工具 -->
                            <div class="mb-3">
                                <h6 class="text-primary">
                                    <i class="fas fa-link me-2"></i>连线工具
                                </h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-outline-success btn-sm w-100" id="add-tie-mode" title="连音线：连接相同音高的音符">
                                            <i class="fas fa-link me-1"></i>连音线
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-outline-info btn-sm w-100" id="add-slur-mode" title="连奏线：连接不同音高的音符">
                                            <i class="fas fa-bezier-curve me-1"></i>连奏线
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-outline-secondary btn-sm w-100" id="add-beam-mode" title="连音符：连接八分音符、十六分音符等的符杆">
                                            <i class="fas fa-grip-lines me-1"></i>连音符
                                        </button>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-outline-primary btn-sm w-100" id="finish-slur" title="完成连奏线创建" style="display: none;">
                                            <i class="fas fa-check me-1"></i>完成连奏线
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-outline-primary btn-sm w-100" id="finish-beam" title="完成连音符创建" style="display: none;">
                                            <i class="fas fa-check me-1"></i>完成连音符
                                        </button>
                                    </div>
                                </div>
                                <small class="text-muted mt-2 d-block">
                                    <i class="fas fa-info-circle me-1"></i>
                                    连音线：相同音高；连奏线：不同音高；连音符：八分音符等符杆连接
                                </small>
                            </div>

                            <!-- 音符列表 -->
                            <div id="notes-container">
                                <div class="text-center text-muted">
                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                    正在加载音符列表...
                                </div>
                            </div>
                        </div>

                        <!-- XML编辑区域 -->
                        <div id="xml-edit-area" class="d-none">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>高级编辑：</strong>直接编辑MusicXML代码，请谨慎修改。
                            </div>

                            <!-- XML编辑工具 -->
                            <div class="mb-3">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="format-xml">
                                    <i class="fas fa-indent me-2"></i>格式化
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm" id="validate-xml">
                                    <i class="fas fa-check me-2"></i>验证
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm" id="save-xml">
                                    <i class="fas fa-save me-2"></i>保存并预览
                                </button>
                            </div>

                            <!-- XML编辑器 -->
                            <div class="mb-3">
                                <textarea id="xml-editor" class="form-control font-monospace"
                                          style="height: 300px; font-size: 12px;"
                                          placeholder="XML内容加载中..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

    
    <!-- 操作按钮 -->
    <div class="row mt-4 mb-4">
        <div class="col-12 text-center">
            <button type="button" class="btn btn-secondary me-3" id="back-to-upload">
                <i class="fas fa-arrow-left me-2"></i>返回上传
            </button>

            <button type="button" class="btn btn-primary" id="proceed-to-fingering">
                <i class="fas fa-hand-paper me-2"></i>确认并生成指法
            </button>
        </div>
    </div>
</div>

<style>
.progress-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 30px;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 50px;
    width: 60px;
    height: 2px;
    background-color: #dee2e6;
    z-index: 1;
}

.step.completed::after,
.step.active::after {
    background-color: #0d6efd;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
}

.step.completed .step-number {
    background-color: #198754;
    color: white;
}

.step.active .step-number {
    background-color: #0d6efd;
    color: white;
}

.step-label {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

.step.completed .step-label,
.step.active .step-label {
    color: #495057;
    font-weight: 500;
}
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/visual_editor.js') }}?v=20250802131000"></script>
{% endblock %}
