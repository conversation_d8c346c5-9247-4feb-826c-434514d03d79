// 历史记录页面JavaScript

let historyData = [];
let filteredData = [];
let currentPage = 1;
let itemsPerPage = 10;

document.addEventListener('DOMContentLoaded', function() {
    loadHistoryData();
    initializeEventListeners();
});

// 加载历史数据
function loadHistoryData() {
    showLoadingIndicator();
    
    fetch('/api/history')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                historyData = data.data;
                filteredData = [...historyData];
                renderHistoryTable();
                hideLoadingIndicator();
            } else {
                throw new Error(data.error || '加载历史记录失败');
            }
        })
        .catch(error => {
            console.error('加载历史记录失败:', error);
            PianoFingeringApp.utils.showNotification('加载历史记录失败: ' + error.message, 'error');
            showEmptyState();
            hideLoadingIndicator();
        });
}

// 显示加载指示器
function showLoadingIndicator() {
    document.getElementById('loading-indicator').classList.remove('d-none');
    document.getElementById('history-table-container').classList.add('d-none');
    document.getElementById('empty-state').classList.add('d-none');
}

// 隐藏加载指示器
function hideLoadingIndicator() {
    document.getElementById('loading-indicator').classList.add('d-none');
}

// 显示空状态
function showEmptyState() {
    document.getElementById('empty-state').classList.remove('d-none');
    document.getElementById('history-table-container').classList.add('d-none');
}

// 渲染历史记录表格
function renderHistoryTable() {
    if (filteredData.length === 0) {
        showEmptyState();
        return;
    }
    
    document.getElementById('history-table-container').classList.remove('d-none');
    document.getElementById('empty-state').classList.add('d-none');
    
    const tbody = document.getElementById('history-tbody');
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageData = filteredData.slice(startIndex, endIndex);
    
    let html = '';
    pageData.forEach(item => {
        const isImage = item.filename.toLowerCase().endsWith('.png') || 
                       item.filename.toLowerCase().endsWith('.jpg') || 
                       item.filename.toLowerCase().endsWith('.jpeg');
        
        html += `
            <tr>
                <td>
                    ${isImage ? `
                        <img src="/api/preview/${item.filename}" 
                             class="img-thumbnail" 
                             style="width: 60px; height: 60px; object-fit: cover; cursor: pointer;"
                             onclick="showPreview('${item.filename}')"
                             alt="预览">
                    ` : `
                        <i class="fas fa-file-pdf fa-2x text-danger"></i>
                    `}
                </td>
                <td>
                    <div class="d-flex flex-column">
                        <span class="fw-bold">${item.filename}</span>
                        <small class="text-muted">${getFileType(item.filename)}</small>
                    </div>
                </td>
                <td>${PianoFingeringApp.utils.formatFileSize(item.size)}</td>
                <td>
                    <div class="d-flex flex-column">
                        <span>${formatDate(item.created_time)}</span>
                        <small class="text-muted">${formatTime(item.created_time)}</small>
                    </div>
                </td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        ${isImage ? `
                            <button type="button" class="btn btn-outline-primary" 
                                    onclick="showPreview('${item.filename}')"
                                    data-bs-toggle="tooltip" title="预览">
                                <i class="fas fa-eye"></i>
                            </button>
                        ` : ''}
                        <a href="/api/download/result/${item.filename}" 
                           class="btn btn-outline-success"
                           data-bs-toggle="tooltip" title="下载">
                            <i class="fas fa-download"></i>
                        </a>
                        <button type="button" class="btn btn-outline-danger" 
                                onclick="showDeleteConfirm('${item.filename}')"
                                data-bs-toggle="tooltip" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
    
    // 重新初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    renderPagination();
}

// 渲染分页
function renderPagination() {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    const paginationContainer = document.getElementById('pagination-container');
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
        paginationContainer.classList.add('d-none');
        return;
    }
    
    paginationContainer.classList.remove('d-none');
    
    let html = '';
    
    // 上一页
    html += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    if (startPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a></li>`;
    }
    
    // 下一页
    html += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;
    
    pagination.innerHTML = html;
}

// 切换页面
function changePage(page) {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    renderHistoryTable();
}

// 初始化事件监听器
function initializeEventListeners() {
    // 搜索
    const searchInput = document.getElementById('search-input');
    searchInput.addEventListener('input', function() {
        filterData();
    });
    
    // 排序
    const sortSelect = document.getElementById('sort-select');
    sortSelect.addEventListener('change', function() {
        sortData();
    });
    
    // 刷新
    const refreshBtn = document.getElementById('refresh-btn');
    refreshBtn.addEventListener('click', function() {
        loadHistoryData();
    });
    
    // 清空全部
    const clearAllBtn = document.getElementById('clear-all-btn');
    clearAllBtn.addEventListener('click', function() {
        if (confirm('确定要删除所有历史记录吗？此操作不可撤销。')) {
            clearAllHistory();
        }
    });
    
    // 删除确认
    const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
    confirmDeleteBtn.addEventListener('click', function() {
        const filename = this.getAttribute('data-filename');
        if (filename) {
            deleteFile(filename);
        }
    });
}

// 过滤数据
function filterData() {
    const searchTerm = document.getElementById('search-input').value.toLowerCase();
    
    if (searchTerm === '') {
        filteredData = [...historyData];
    } else {
        filteredData = historyData.filter(item => 
            item.filename.toLowerCase().includes(searchTerm)
        );
    }
    
    currentPage = 1;
    renderHistoryTable();
}

// 排序数据
function sortData() {
    const sortType = document.getElementById('sort-select').value;
    
    filteredData.sort((a, b) => {
        switch (sortType) {
            case 'date_desc':
                return new Date(b.created_time) - new Date(a.created_time);
            case 'date_asc':
                return new Date(a.created_time) - new Date(b.created_time);
            case 'name_asc':
                return a.filename.localeCompare(b.filename);
            case 'name_desc':
                return b.filename.localeCompare(a.filename);
            case 'size_desc':
                return b.size - a.size;
            case 'size_asc':
                return a.size - b.size;
            default:
                return 0;
        }
    });
    
    currentPage = 1;
    renderHistoryTable();
}

// 显示预览
function showPreview(filename) {
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    const image = document.getElementById('preview-modal-image');
    const downloadBtn = document.getElementById('preview-download-btn');
    
    image.src = `/api/preview/${filename}`;
    downloadBtn.href = `/api/download/result/${filename}`;
    
    modal.show();
}

// 显示删除确认
function showDeleteConfirm(filename) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const fileInfo = document.getElementById('delete-file-info');
    const confirmBtn = document.getElementById('confirm-delete-btn');
    
    const item = historyData.find(item => item.filename === filename);
    if (item) {
        fileInfo.innerHTML = `
            <strong>文件名:</strong> ${item.filename}<br>
            <strong>大小:</strong> ${PianoFingeringApp.utils.formatFileSize(item.size)}<br>
            <strong>创建时间:</strong> ${formatDate(item.created_time)}
        `;
    }
    
    confirmBtn.setAttribute('data-filename', filename);
    modal.show();
}

// 删除文件
function deleteFile(filename) {
    fetch(`/api/delete/${filename}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            PianoFingeringApp.utils.showNotification('文件删除成功', 'success');
            
            // 从数据中移除
            historyData = historyData.filter(item => item.filename !== filename);
            filterData(); // 重新过滤和渲染
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
            modal.hide();
        } else {
            throw new Error(data.error || '删除失败');
        }
    })
    .catch(error => {
        console.error('删除文件失败:', error);
        PianoFingeringApp.utils.showNotification('删除失败: ' + error.message, 'error');
    });
}

// 清空所有历史记录
function clearAllHistory() {
    // 这里应该调用API批量删除
    // 暂时逐个删除
    const deletePromises = historyData.map(item => 
        fetch(`/api/delete/${item.filename}`, { method: 'DELETE' })
    );
    
    Promise.all(deletePromises)
        .then(() => {
            PianoFingeringApp.utils.showNotification('所有历史记录已清空', 'success');
            historyData = [];
            filteredData = [];
            renderHistoryTable();
        })
        .catch(error => {
            console.error('清空历史记录失败:', error);
            PianoFingeringApp.utils.showNotification('清空失败，请重试', 'error');
        });
}

// 工具函数
function getFileType(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    switch (ext) {
        case 'png': return 'PNG图片';
        case 'jpg':
        case 'jpeg': return 'JPEG图片';
        case 'pdf': return 'PDF文档';
        default: return '未知格式';
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleTimeString('zh-CN');
}
