{% extends "base.html" %}

{% block title %}MusicXML编辑器{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">
                <i class="fas fa-edit me-2"></i>确认识别结果
            </h2>
            
            <!-- 步骤指示器 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="progress-steps">
                        <div class="step completed">
                            <div class="step-number">1</div>
                            <div class="step-label">上传图片</div>
                        </div>
                        <div class="step active">
                            <div class="step-number">2</div>
                            <div class="step-label">确认识别</div>
                        </div>
                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-label">生成指法</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 友好提示 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning shadow-sm">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>识别结果确认</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>⚠️ 可能存在的问题：</h6>
                        <ul class="mb-0">
                            <li><strong>音符遗漏：</strong>部分音符可能未被识别</li>
                            <li><strong>时值错误：</strong>音符时值可能不准确</li>
                            <li><strong>音高偏差：</strong>音符位置可能有偏移</li>
                            <li><strong>符号缺失：</strong>装饰音、连线等可能丢失</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>✅ 您需要做的操作：</h6>
                        <ul class="mb-0">
                            <li><strong>对比检查：</strong>参考左侧原图仔细核对</li>
                            <li><strong>修正错误：</strong>直接编辑XML代码或使用可视化编辑</li>
                            <li><strong>验证结果：</strong>点击"保存并预览"查看效果</li>
                            <li><strong>确认无误：</strong>检查完毕后点击"确认并生成指法"</li>
                        </ul>
                    </div>
                </div>
                <hr>
                <p class="mb-0 text-primary">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>提示：</strong>建议先使用"可视化编辑"进行快速修正，复杂修改可切换到XML编辑模式。
                </p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 左侧：原始图片 -->
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-image me-2"></i>原始图片
                    </h5>
                </div>
                <div class="card-body text-center">
                    <img id="original-image" src="" class="img-fluid rounded" alt="原始乐谱">
                </div>
            </div>
        </div>
        
        <!-- 右侧：识别预览 -->
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-music me-2"></i>识别预览
                    </h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary" id="refresh-preview">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                        <button type="button" class="btn btn-outline-success" id="switch-to-visual">
                            <i class="fas fa-eye"></i> 可视化编辑
                        </button>
                        <button type="button" class="btn btn-outline-info" id="view-xml">
                            <i class="fas fa-code"></i> 查看XML
                        </button>
                    </div>
                </div>
                <div class="card-body text-center">
                    <div id="preview-loading" class="d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">生成预览中...</span>
                        </div>
                        <p class="mt-2">正在生成预览...</p>
                    </div>
                    <img id="recognition-preview" src="" class="img-fluid rounded d-none" alt="识别预览">
                    <div id="preview-error" class="alert alert-warning d-none">
                        <i class="fas fa-exclamation-triangle"></i>
                        预览生成失败，但您仍可以继续编辑XML
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 标题信息编辑 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-heading me-2"></i>标题信息编辑
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-light">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>提示：</strong>如果OCR识别的标题信息不准确，您可以在此处手动修改。
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">主标题</label>
                            <input type="text" class="form-control" id="xml-main-title" placeholder="请输入主标题">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">副标题</label>
                            <input type="text" class="form-control" id="xml-sub-title" placeholder="请输入副标题">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <label class="form-label">原唱</label>
                            <input type="text" class="form-control" id="xml-original-artist" placeholder="原唱信息">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">编曲</label>
                            <input type="text" class="form-control" id="xml-arranger" placeholder="编曲信息">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">版权</label>
                            <textarea class="form-control" id="xml-copyright" placeholder="版权信息（支持多行）" rows="2" style="resize: vertical;"></textarea>
                        </div>
                    </div>
                    <div class="mt-3 text-end">
                        <button type="button" class="btn btn-primary btn-sm" id="apply-title-changes">
                            <i class="fas fa-check me-2"></i>应用标题修改
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- XML编辑区域 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-code me-2"></i>MusicXML编辑
                    </h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary" id="format-xml">
                            <i class="fas fa-indent"></i> 格式化
                        </button>
                        <button type="button" class="btn btn-outline-warning" id="validate-xml">
                            <i class="fas fa-check-circle"></i> 验证
                        </button>
                        <button type="button" class="btn btn-outline-info" id="reset-xml">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>编辑提示：</strong>您可以直接编辑下方的MusicXML代码来修正识别错误。
                        常见修改包括：音符音高、时值、小节线等。
                    </div>
                    
                    <!-- XML编辑器 -->
                    <div id="xml-editor-container">
                        <textarea id="xml-editor" class="form-control" rows="20" style="font-family: 'Courier New', monospace; font-size: 12px;"></textarea>
                    </div>
                    
                    <!-- 验证结果 -->
                    <div id="validation-result" class="mt-3 d-none"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="row mt-4 mb-4">
        <div class="col-12 text-center">
            <button type="button" class="btn btn-secondary me-3" id="back-to-upload">
                <i class="fas fa-arrow-left me-2"></i>返回上传
            </button>
            <button type="button" class="btn btn-success me-3" id="generate-preview">
                <i class="fas fa-eye me-2"></i>保存并预览
            </button>
            <button type="button" class="btn btn-primary" id="proceed-to-fingering">
                <i class="fas fa-hand-paper me-2"></i>确认并生成指法
            </button>
        </div>
    </div>
</div>

<!-- XML查看模态框 -->
<div class="modal fade" id="xmlModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">MusicXML代码</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <pre id="xml-display" style="max-height: 400px; overflow-y: auto;"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="copy-xml">复制XML</button>
            </div>
        </div>
    </div>
</div>

<style>
.progress-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 30px;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 50px;
    width: 60px;
    height: 2px;
    background-color: #dee2e6;
    z-index: 1;
}

.step.completed::after,
.step.active::after {
    background-color: #0d6efd;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
}

.step.completed .step-number {
    background-color: #198754;
    color: white;
}

.step.active .step-number {
    background-color: #0d6efd;
    color: white;
}

.step-label {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

.step.completed .step-label,
.step.active .step-label {
    color: #495057;
    font-weight: 500;
}

#xml-editor {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

#xml-editor:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/xml_editor.js') }}?v=20250727160000"></script>
{% endblock %}
