/* 自定义样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* 上传区域 */
.upload-area {
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.upload-area.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
    transform: scale(1.02);
}

/* 文件预览 */
#preview-image {
    max-height: 300px;
    object-fit: contain;
    border: 1px solid #dee2e6;
}

/* 进度条 */
.progress {
    height: 25px;
}

.progress-bar {
    font-size: 14px;
    line-height: 25px;
}

/* 步骤指示器 */
.step-number {
    position: relative;
}

.step-number::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 100%;
    width: 100%;
    height: 2px;
    background-color: #dee2e6;
    z-index: -1;
}

.step-number:last-child::after {
    display: none;
}

/* 功能图标 */
.feature-icon {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 卡片悬停效果 */
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-success {
    background-color: #28a745;
}

.status-warning {
    background-color: #ffc107;
}

.status-danger {
    background-color: #dc3545;
}

.status-info {
    background-color: #17a2b8;
}

/* 处理步骤 */
.step-item {
    padding: 15px;
    border-left: 3px solid #dee2e6;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 0 5px 5px 0;
}

.step-item.active {
    border-left-color: #007bff;
    background-color: #e3f2fd;
}

.step-item.completed {
    border-left-color: #28a745;
    background-color: #d4edda;
}

.step-item.failed {
    border-left-color: #dc3545;
    background-color: #f8d7da;
}

/* 结果展示 */
.result-image {
    max-width: 100%;
    height: auto;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 下载按钮组 */
.download-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

/* 历史记录表格 */
.history-table {
    background-color: #fff;
}

.history-table th {
    background-color: #f8f9fa;
    border-top: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .step-number::after {
        display: none;
    }
    
    .download-buttons {
        flex-direction: column;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip-inner {
    max-width: 300px;
    text-align: left;
}

/* 错误消息 */
.error-message {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

/* 成功消息 */
.success-message {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

/* 文件信息 */
.file-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.file-info dt {
    font-weight: 600;
    color: #495057;
}

.file-info dd {
    color: #6c757d;
    margin-bottom: 8px;
}
