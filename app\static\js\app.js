// 全局JavaScript功能

// Socket.IO连接（暂时禁用，使用HTTP轮询）
// const socket = io();
const socket = null;

// 全局变量（currentTaskId在各页面单独声明）

// 工具函数
const utils = {
    // 格式化文件大小
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // 格式化时间
    formatTime: function(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    },
    
    // 显示通知
    showNotification: function(message, type = 'info') {
        const alertClass = type === 'error' ? 'alert-danger' : 
                          type === 'success' ? 'alert-success' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 在页面顶部插入通知
        const container = document.querySelector('main .container, main .container-fluid');
        if (container) {
            container.insertAdjacentHTML('afterbegin', alertHtml);
            
            // 5秒后自动关闭
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }
    },
    
    // AJAX请求封装
    request: function(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        return fetch(url, finalOptions)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('Request failed:', error);
                throw error;
            });
    }
};

// Socket.IO事件处理（暂时禁用）
if (socket) {
    socket.on('connect', function() {
        console.log('Socket.IO连接成功');
    });

    socket.on('disconnect', function() {
        console.log('Socket.IO连接断开');
    });

    socket.on('task_progress', function(data) {
        console.log('收到任务进度更新:', data);
        // currentTaskId在各页面单独管理
        // if (data.task_id === currentTaskId) {
        //     updateProgress(data);
        // }
    });
}

// 进度更新函数
function updateProgress(data) {
    const progressBar = document.getElementById('progress-bar');
    const statusText = document.getElementById('status-text');
    const stepDetails = document.getElementById('step-details');
    
    if (progressBar) {
        progressBar.style.width = data.progress + '%';
        progressBar.textContent = data.progress + '%';
    }
    
    if (statusText) {
        statusText.textContent = data.status || '处理中...';
    }
    
    if (stepDetails && data.steps) {
        updateStepDetails(data.steps);
    }
}

// 更新步骤详情
function updateStepDetails(steps) {
    const stepDetails = document.getElementById('step-details');
    if (!stepDetails) return;
    
    const stepNames = {
        'preprocessing': '图像预处理',
        'ocr': '乐谱识别',
        'fingering': '指法生成',
        'rendering': '乐谱渲染'
    };
    
    let html = '';
    for (const [stepKey, stepData] of Object.entries(steps)) {
        const stepName = stepNames[stepKey] || stepKey;
        let statusClass = '';
        let statusIcon = '';
        
        if (stepData.status === 'completed') {
            statusClass = 'completed';
            statusIcon = '<i class="fas fa-check-circle text-success"></i>';
        } else if (stepData.status === 'running') {
            statusClass = 'active';
            statusIcon = '<i class="fas fa-spinner fa-spin text-primary"></i>';
        } else if (stepData.status === 'failed') {
            statusClass = 'failed';
            statusIcon = '<i class="fas fa-times-circle text-danger"></i>';
        } else {
            statusIcon = '<i class="fas fa-clock text-muted"></i>';
        }
        
        html += `
            <div class="step-item ${statusClass}">
                <div class="d-flex align-items-center">
                    ${statusIcon}
                    <span class="ms-2">${stepName}</span>
                    ${stepData.error ? `<small class="text-danger ms-2">${stepData.error}</small>` : ''}
                </div>
            </div>
        `;
    }
    
    stepDetails.innerHTML = html;
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化弹出框
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // 检查引擎状态
    checkEngineStatus();
});

// 检查引擎状态
function checkEngineStatus() {
    utils.request('/api/engine-status')
        .then(data => {
            console.log('引擎状态:', data);
            updateEngineStatusDisplay(data);
        })
        .catch(error => {
            console.error('获取引擎状态失败:', error);
        });
}

// 更新引擎状态显示
function updateEngineStatusDisplay(status) {
    const statusElements = document.querySelectorAll('[data-engine-status]');
    
    statusElements.forEach(element => {
        const engineName = element.getAttribute('data-engine-status');
        const engineStatus = status[engineName];
        
        if (engineStatus) {
            const isAvailable = engineStatus.available;
            const statusClass = isAvailable ? 'status-success' : 'status-danger';
            const statusText = isAvailable ? '可用' : '不可用';
            
            element.innerHTML = `
                <span class="status-indicator ${statusClass}"></span>
                ${statusText}
                ${engineStatus.version ? `<small class="text-muted">(${engineStatus.version})</small>` : ''}
            `;
        }
    });
}

// 导出全局对象
window.PianoFingeringApp = {
    utils: utils,
    socket: socket,
    updateProgress: updateProgress,
    checkEngineStatus: checkEngineStatus
};
