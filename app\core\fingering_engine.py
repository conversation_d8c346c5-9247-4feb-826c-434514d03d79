"""
钢琴指法生成引擎
基于人体工学模型和动态规划算法
"""
import logging
import xml.etree.ElementTree as ET
import os
import re
import zipfile
import tempfile

logger = logging.getLogger(__name__)

class FingeringEngine:
    """钢琴指法生成引擎"""
    
    def __init__(self, config=None):
        # 手指编号：1=拇指, 2=食指, 3=中指, 4=无名指, 5=小指
        self.finger_weights = {
            1: 1.0,  # 拇指
            2: 0.9,  # 食指
            3: 0.8,  # 中指
            4: 0.7,  # 无名指
            5: 0.6   # 小指
        }
        
        # 手指跨度限制（半音数）
        self.max_span = {
            (1, 2): 2,   # 拇指到食指
            (1, 3): 4,   # 拇指到中指
            (1, 4): 6,   # 拇指到无名指
            (1, 5): 8,   # 拇指到小指
            (2, 3): 2,   # 食指到中指
            (2, 4): 4,   # 食指到无名指
            (2, 5): 6,   # 食指到小指
            (3, 4): 2,   # 中指到无名指
            (3, 5): 4,   # 中指到小指
            (4, 5): 2    # 无名指到小指
        }
        
        # 黑键位置（C=0, C#=1, D=2, ...）
        self.black_keys = {1, 3, 6, 8, 10}  # C#, D#, F#, G#, A#
    
    def process_musicxml(self, xml_path):
        """
        处理MusicXML文件，添加指法标注

        Args:
            xml_path (str): MusicXML文件路径

        Returns:
            dict: 处理结果
        """
        try:
            # 处理压缩的MusicXML文件(.mxl)
            if xml_path.lower().endswith('.mxl'):
                xml_content = self._extract_mxl_file(xml_path)
                if not xml_content:
                    raise ValueError("无法从MXL文件中提取XML内容")

                # 解析XML内容
                root = ET.fromstring(xml_content)
                # 创建ElementTree对象以便写入文件
                tree = ET.ElementTree(root)
            else:
                # 直接解析XML文件
                tree = ET.parse(xml_path)
                root = tree.getroot()

            # 查找所有音符
            notes = self._find_notes_in_xml(root)

            # 为音符添加指法
            self._add_fingering_to_xml_notes(notes)

            # 生成带指法的MusicXML
            output_path = xml_path.replace('.xml', '_fingered.xml').replace('.mxl', '_fingered.xml')
            tree.write(output_path, encoding='utf-8', xml_declaration=True)

            logger.info(f"指法生成完成: {output_path}")

            return {
                'success': True,
                'output_path': output_path,
                'notes_processed': len(notes)
            }

        except Exception as e:
            logger.error(f"指法生成失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _extract_mxl_file(self, mxl_path):
        """
        从MXL文件中提取XML内容

        Args:
            mxl_path (str): MXL文件路径

        Returns:
            str: XML内容，如果失败返回None
        """
        try:
            with zipfile.ZipFile(mxl_path, 'r') as zip_file:
                # 查找主要的XML文件
                xml_files = [f for f in zip_file.namelist() if f.endswith('.xml')]

                if not xml_files:
                    logger.error("MXL文件中没有找到XML文件")
                    return None

                # 通常主文件是第一个XML文件，或者与MXL文件同名的XML文件
                main_xml = xml_files[0]

                # 尝试找到与MXL文件同名的XML文件
                mxl_basename = os.path.splitext(os.path.basename(mxl_path))[0]
                for xml_file in xml_files:
                    if os.path.splitext(xml_file)[0] == mxl_basename:
                        main_xml = xml_file
                        break

                logger.info(f"从MXL文件中提取: {main_xml}")

                # 读取XML内容
                with zip_file.open(main_xml) as xml_file:
                    xml_content = xml_file.read().decode('utf-8')
                    return xml_content

        except Exception as e:
            logger.error(f"提取MXL文件失败: {str(e)}")
            return None
    
    def _find_notes_in_xml(self, root):
        """在XML中查找所有音符"""
        notes = []

        # 查找所有note元素
        for note_elem in root.iter('note'):
            # 跳过休止符
            if note_elem.find('rest') is not None:
                continue

            # 获取音高信息
            pitch_elem = note_elem.find('pitch')
            if pitch_elem is not None:
                step = pitch_elem.find('step').text if pitch_elem.find('step') is not None else 'C'
                octave = int(pitch_elem.find('octave').text) if pitch_elem.find('octave') is not None else 4

                # 获取声部信息
                staff = note_elem.find('staff')
                staff_num = int(staff.text) if staff is not None else 1

                notes.append({
                    'element': note_elem,
                    'step': step,
                    'octave': octave,
                    'staff': staff_num,
                    'midi': self._step_octave_to_midi(step, octave)
                })

        return notes
    
    def _add_fingering_to_xml_notes(self, notes):
        """为XML音符添加指法标注"""
        if not notes:
            return

        # 按声部分组
        staff_notes = {}
        for note in notes:
            staff = note['staff']
            if staff not in staff_notes:
                staff_notes[staff] = []
            staff_notes[staff].append(note)

        # 为每个声部生成指法
        for staff_num, staff_note_list in staff_notes.items():
            hand = 'right' if staff_num == 1 else 'left'
            fingerings = self._calculate_optimal_fingering(staff_note_list, hand)

            # 应用指法到XML元素
            for note, fingering in zip(staff_note_list, fingerings):
                self._add_fingering_to_xml_note(note['element'], fingering)
    
    def _calculate_optimal_fingering(self, notes, hand):
        """使用简化算法计算指法序列"""
        n = len(notes)
        if n == 0:
            return []

        fingerings = []

        for i, note in enumerate(notes):
            midi = note['midi']

            # 简化的指法分配规则
            if hand == 'right':
                # 右手指法规则
                if midi < 60:  # 中央C以下
                    finger = 1  # 拇指
                elif midi < 65:  # C-F
                    finger = 2 if i % 2 == 0 else 3
                elif midi < 70:  # F-A#
                    finger = 3 if i % 2 == 0 else 4
                else:  # A#以上
                    finger = 4 if i % 2 == 0 else 5
            else:
                # 左手指法规则
                if midi > 60:  # 中央C以上
                    finger = 1  # 拇指
                elif midi > 55:  # G-C
                    finger = 2 if i % 2 == 0 else 3
                elif midi > 50:  # D-F#
                    finger = 3 if i % 2 == 0 else 4
                else:  # D以下
                    finger = 4 if i % 2 == 0 else 5

            # 黑键优化
            if self._is_black_key(note['step']):
                if finger == 1:  # 拇指不弹黑键
                    finger = 2
                elif finger == 5 and i > 0:  # 小指尽量少弹黑键
                    finger = 4

            fingerings.append(finger)

        return fingerings
    
    def _step_octave_to_midi(self, step, octave):
        """将音名和八度转换为MIDI音高"""
        note_values = {'C': 0, 'D': 2, 'E': 4, 'F': 5, 'G': 7, 'A': 9, 'B': 11}
        return (octave + 1) * 12 + note_values.get(step, 0)

    def _is_black_key(self, step):
        """判断是否为黑键"""
        # 这里简化处理，实际应该考虑升降号
        return False  # 暂时都当作白键处理

    def _add_fingering_to_xml_note(self, note_element, fingering):
        """为XML音符元素添加指法标注"""
        try:
            # 查找或创建notations元素
            notations = note_element.find('notations')
            if notations is None:
                notations = ET.SubElement(note_element, 'notations')

            # 查找或创建technical元素
            technical = notations.find('technical')
            if technical is None:
                technical = ET.SubElement(notations, 'technical')

            # 添加指法元素
            fingering_elem = ET.SubElement(technical, 'fingering')
            fingering_elem.text = str(fingering)
            fingering_elem.set('alternate', 'no')
            fingering_elem.set('substitution', 'no')

            logger.debug(f"为音符添加指法: {fingering}")

        except Exception as e:
            logger.warning(f"添加指法标注失败: {str(e)}")
