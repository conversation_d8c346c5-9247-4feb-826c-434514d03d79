{% extends "base.html" %}

{% block title %}历史记录 - 钢琴指法自动标注系统{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="fas fa-history me-2"></i>处理历史记录
            </h2>
            
            <!-- 搜索和筛选 -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="search-input" 
                                       placeholder="搜索文件名...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="sort-select">
                                <option value="date_desc">按日期降序</option>
                                <option value="date_asc">按日期升序</option>
                                <option value="name_asc">按名称升序</option>
                                <option value="name_desc">按名称降序</option>
                                <option value="size_desc">按大小降序</option>
                                <option value="size_asc">按大小升序</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary" id="refresh-btn">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button class="btn btn-outline-danger" id="clear-all-btn">
                                <i class="fas fa-trash me-1"></i>清空全部
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 历史记录表格 -->
            <div class="card shadow-sm">
                <div class="card-body">
                    <div id="loading-indicator" class="text-center py-4">
                        <div class="loading-spinner me-2"></div>
                        加载中...
                    </div>
                    
                    <div id="history-table-container" class="d-none">
                        <div class="table-responsive">
                            <table class="table table-hover history-table">
                                <thead>
                                    <tr>
                                        <th>预览</th>
                                        <th>文件名</th>
                                        <th>大小</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="history-tbody">
                                    <!-- 历史记录将在这里动态加载 -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <nav aria-label="历史记录分页" id="pagination-container" class="d-none">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将在这里动态生成 -->
                            </ul>
                        </nav>
                    </div>
                    
                    <div id="empty-state" class="text-center py-5 d-none">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无历史记录</h5>
                        <p class="text-muted">您还没有处理过任何乐谱文件</p>
                        <a href="{{ url_for('main.upload') }}" class="btn btn-primary">
                            <i class="fas fa-upload me-1"></i>开始上传
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除这个文件吗？此操作不可撤销。</p>
                <div id="delete-file-info" class="alert alert-warning">
                    <!-- 文件信息将在这里显示 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirm-delete-btn">
                    <i class="fas fa-trash me-1"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">文件预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="preview-modal-image" class="img-fluid" alt="文件预览">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <a id="preview-download-btn" class="btn btn-primary" target="_blank">
                    <i class="fas fa-download me-1"></i>下载
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/history.js') }}"></script>
{% endblock %}
