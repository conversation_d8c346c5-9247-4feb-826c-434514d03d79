"""
文字识别集成模块 - 将PaddleOCR识别的文字信息集成到MusicXML中
"""
import os
import logging
import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional
from app.core.text_recognition import TextRecognitionEngine

logger = logging.getLogger(__name__)

class TextIntegrationEngine:
    """文字识别集成引擎"""
    
    def __init__(self):
        self.text_engine = TextRecognitionEngine()
    
    def integrate_text_with_existing_info(self, musicxml_path: str, text_info: Dict[str, Any], output_path: str = None) -> Dict[str, Any]:
        """
        使用已识别的文本信息集成到MusicXML文件中（避免重复OCR）

        Args:
            musicxml_path: MusicXML文件路径
            text_info: 已识别的文本信息
            output_path: 输出文件路径，如果为None则覆盖原文件

        Returns:
            集成结果字典
        """
        try:
            logger.info(f"开始文字集成到MusicXML: {musicxml_path}")
            logger.info(f"文本信息类型: {type(text_info)}")
            logger.info(f"文本信息内容: {text_info}")

            # 检查text_info是否为None或空
            if not text_info:
                logger.warning("text_info为空，跳过文字集成")
                return {'success': True, 'output_file': musicxml_path, 'text_count': 0}

            # 确保text_info是字典类型
            if not isinstance(text_info, dict):
                logger.error(f"text_info类型错误，期望dict，实际{type(text_info)}")
                return {'success': True, 'output_file': musicxml_path, 'text_count': 0}

            # 直接处理XML文件，不重新进行OCR
            import xml.etree.ElementTree as ET

            # 解析XML文件
            tree = ET.parse(musicxml_path)
            root = tree.getroot()

            # 使用结构化文本信息集成方法
            try:
                logger.info("开始调用_integrate_structured_text_info...")
                self._integrate_structured_text_info(root, text_info)
                logger.info("_integrate_structured_text_info完成")
            except Exception as e:
                logger.error(f"_integrate_structured_text_info失败: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
                raise

            # 同时使用原有的标题信息处理方法（保持兼容性）
            try:
                logger.info("开始调用_add_standard_title_info...")
                texts = text_info.get('other_texts', [])
                self._add_standard_title_info(root, texts, text_info)
                logger.info("_add_standard_title_info完成")
            except Exception as e:
                logger.error(f"_add_standard_title_info失败: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
                raise

            # 修复小节编号（预备小节改为第1小节，其他小节+1）
            self._fix_measure_numbers(root)

            # 保存结果
            try:
                logger.info("开始保存XML文件...")
                output_file = output_path if output_path else musicxml_path
                self._save_formatted_xml(tree, output_file)
                logger.info("XML文件保存完成")
            except Exception as e:
                logger.error(f"保存XML文件失败: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
                raise

            result = output_file

            return {
                'success': True,
                'output_file': result,
                'text_count': len(texts)
            }

        except Exception as e:
            logger.error(f"文字集成失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def integrate_text_to_musicxml(self, image_path: str, musicxml_path: str, output_path: str = None) -> Dict[str, Any]:
        """
        将PaddleOCR识别的文字信息集成到MusicXML文件中

        Args:
            image_path: 原始图片路径
            musicxml_path: MusicXML文件路径
            output_path: 输出文件路径，如果为None则覆盖原文件

        Returns:
            集成结果字典
        """
        print(f"🔥🔥🔥 DEBUG: integrate_text_to_musicxml被调用! image_path={image_path} 🔥🔥🔥")
        try:
            # 1. 使用PaddleOCR识别文字
            logger.info(f"开始识别图片文字: {image_path}")
            ocr_result = self.text_engine.recognize_text(image_path)
            
            if not ocr_result['success']:
                return {
                    'success': False,
                    'error': f"文字识别失败: {ocr_result['error']}",
                    'added_texts': []
                }
            
            # 2. 格式化识别结果
            formatted_result = self.text_engine.format_recognition_result(ocr_result['texts'], image_path)
            
            # 3. 读取MusicXML文件
            logger.info(f"读取MusicXML文件: {musicxml_path}")
            tree = ET.parse(musicxml_path)
            root = tree.getroot()
            
            # 4. 添加标准标题信息（基于OCR识别结果）
            logger.info("添加标准标题信息...")
            self._add_standard_title_info(root, formatted_result.get('texts', []))

            # 5. 然后集成其他文字信息（跳过作者信息，因为已经有标准信息）
            added_texts = self._integrate_texts_to_xml(root, formatted_result)

            # 6. 保存更新后的文件（格式化XML以确保MuseScore正确解析）
            output_file = output_path if output_path else musicxml_path
            self._save_formatted_xml(tree, output_file)
            
            logger.info(f"文字集成完成，添加了 {len(added_texts)} 个文字信息")
            
            return {
                'success': True,
                'added_texts': added_texts,
                'total_recognized': formatted_result['total_count'],
                'title_candidates': [t['text'] for t in formatted_result['title_candidates']],
                'author_candidates': [t['text'] for t in formatted_result['author_candidates']],
                'output_file': output_file
            }
            
        except Exception as e:
            logger.error(f"文字集成失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'added_texts': []
            }
    
    def _integrate_texts_to_xml(self, root: ET.Element, formatted_result: Dict[str, Any]) -> List[Dict[str, str]]:
        """
        将识别的文字信息集成到XML中

        Args:
            root: XML根元素
            formatted_result: 格式化的识别结果

        Returns:
            添加的文字信息列表
        """
        added_texts = []

        # 1. 标准标题信息将在主流程中添加，这里只记录
        added_texts.append({
            'type': 'standard_title_added',
            'value': 'Added standard title information based on OCR results'
        })

        # 2. 标题信息已在标准标题信息中处理，这里跳过以避免重复
        added_texts.append({
            'type': 'title_skip',
            'value': 'Skipped title update to avoid duplication with standard title info'
        })
        
        # 2. 跳过作者信息添加（因为我们使用标准标题信息）
        author_candidates = formatted_result['author_candidates']
        if author_candidates:
            # 记录识别到的作者信息，但不添加到XML中
            for author in author_candidates:
                added_texts.append({
                    'type': 'author_skipped',
                    'value': f"跳过OCR识别的作者信息: {author['text']}",
                    'confidence': author['confidence']
                })
        
        # 3. 跳过移除原始credit和添加OCR识别的文字作为credit
        # 因为_add_standard_title_info会处理所有credit相关的逻辑
        # 注释掉原来的OCR credit添加逻辑，因为我们已经在_add_standard_title_info中添加了标准标题
        added_texts.append({
            'type': 'ocr_credits_skipped',
            'value': 'Skipped OCR credits in favor of standard title info'
        })
        
        return added_texts

    def _integrate_structured_text_info(self, root: ET.Element, text_info: Dict[str, Any]):
        """
        使用结构化的文本信息集成到XML中

        Args:
            root: XML根元素
            text_info: 结构化的文本信息
        """
        import logging
        logger = logging.getLogger(__name__)
        logger.info("开始集成结构化文本信息...")

        # 1. 处理主标题 (work-title)
        title = text_info.get('title', '') or ''
        if title and title.strip():
            work = root.find('work')
            if work is None:
                work = ET.Element('work')
                work_title = ET.SubElement(work, 'work-title')
                work_title.text = title
                root.insert(0, work)
                logger.info(f"添加主标题: {title}")
            else:
                work_title = work.find('work-title')
                if work_title is not None:
                    work_title.text = title
                    logger.info(f"更新主标题: {title}")

        # 2. 处理副标题 (movement-title)
        subtitle = text_info.get('subtitle', '') or ''
        if subtitle and subtitle.strip():
            movement_title = root.find('movement-title')
            if movement_title is None:
                movement_title = ET.Element('movement-title')
                movement_title.text = subtitle
                work = root.find('work')
                work_index = list(root).index(work) if work is not None else 0
                root.insert(work_index + 1, movement_title)
                logger.info(f"添加副标题: {subtitle}")
            else:
                movement_title.text = subtitle
                logger.info(f"更新副标题: {subtitle}")

        # 3. 处理创作者信息
        self._update_creator_info(root, text_info)

        # 4. 处理版权信息
        copyright_text = text_info.get('copyright', '') or ''
        if copyright_text and copyright_text.strip():
            self._update_copyright_info(root, copyright_text)

    def _update_creator_info(self, root: ET.Element, text_info: Dict[str, Any]):
        """更新创作者信息"""
        import logging
        logger = logging.getLogger(__name__)

        identification = root.find('identification')
        if identification is None:
            identification = ET.Element('identification')
            root.insert(-1, identification)

        # 清除现有的creator信息（除了encoding）
        creators_to_remove = identification.findall('creator')
        for creator in creators_to_remove:
            identification.remove(creator)

        # 添加作曲信息
        composer = text_info.get('composer', '') or ''
        if composer and composer.strip():
            composer_elem = ET.Element('creator')
            composer_elem.set('type', 'composer')
            composer_elem.text = composer.strip()
            identification.insert(0, composer_elem)
            logger.info(f"添加作曲: {composer.strip()}")

        # 添加编曲信息
        arranger = text_info.get('arranger', '') or ''
        if arranger and arranger.strip():
            arranger_elem = ET.Element('creator')
            arranger_elem.set('type', 'arranger')
            arranger_elem.text = arranger.strip()
            identification.insert(-1, arranger_elem)
            logger.info(f"添加编曲: {arranger.strip()}")

        # 添加原唱信息（使用lyricist类型）
        original_artist = text_info.get('original_artist', '') or ''
        if original_artist and original_artist.strip():
            artist_elem = ET.Element('creator')
            artist_elem.set('type', 'lyricist')
            artist_elem.text = original_artist.strip()
            identification.insert(-1, artist_elem)
            logger.info(f"添加原唱: {original_artist.strip()}")

    def _update_copyright_info(self, root: ET.Element, copyright_text: str):
        """更新版权信息"""
        import logging
        logger = logging.getLogger(__name__)

        identification = root.find('identification')
        if identification is not None:
            rights = identification.find('rights')
            if rights is not None:
                rights.text = copyright_text
                logger.info(f"更新版权信息: {copyright_text}")

    def _fix_measure_numbers(self, root: ET.Element):
        """
        修复小节编号
        将预备小节（小节0）改为小节1，其他小节依次+1
        """
        import logging
        logger = logging.getLogger(__name__)

        # 查找所有小节
        measures = root.findall('.//measure')

        # 按照原始编号排序
        measures.sort(key=lambda m: int(m.get('number', '0')))

        # 重新编号：预备小节（0）变成1，其他小节+1
        fixes_made = 0
        logger.info(f"🔥🔥🔥 开始修复小节编号，共找到 {len(measures)} 个小节 🔥🔥🔥")

        for measure in measures:
            old_number = int(measure.get('number', '0'))
            new_number = old_number + 1
            measure.set('number', str(new_number))
            fixes_made += 1
            logger.info(f"🔥 小节编号修改: {old_number} → {new_number}")

        logger.info(f"🔥🔥🔥 小节编号修复完成，共修改了 {fixes_made} 个小节 🔥🔥🔥")

    def _add_standard_title_info(self, root: ET.Element, texts: List[Dict] = None, structured_info: Dict = None):
        """
        添加标题信息，优先使用结构化的文本信息，如果没有则使用OCR文字列表

        Args:
            root: XML根元素
            texts: OCR识别的文字列表
            structured_info: 结构化的文本信息
        """
        if texts is None:
            texts = []
        if structured_info is None:
            structured_info = {}
        import logging
        logger = logging.getLogger(__name__)
        logger.info("开始添加标题信息，优先使用结构化信息...")

        # 优先使用结构化信息，如果没有则从文字列表中提取
        title_text = structured_info.get('title', '') or ''
        if title_text and title_text.strip():
            title_text = title_text.strip()
        else:
            title_text = self._extract_title_from_texts(texts)

        subtitle_text = structured_info.get('subtitle', '') or ''
        if subtitle_text and subtitle_text.strip():
            subtitle_text = subtitle_text.strip()
        else:
            subtitle_text = self._extract_subtitle_from_texts(texts)

        logger.info(f"使用标题信息: 主标题='{title_text}', 副标题='{subtitle_text}'")

        # 1. 处理work-title
        work = root.find('work')
        if work is None and title_text:
            # 只有识别到标题时才创建work元素
            work = ET.Element('work')
            work_title = ET.SubElement(work, 'work-title')
            work_title.text = title_text
            root.insert(0, work)
            logger.info(f"添加work-title: {title_text}")
        elif work is not None and title_text:
            # 如果work存在且识别到标题，更新work-title
            work_title = work.find('work-title')
            if work_title is not None:
                work_title.text = title_text
                logger.info(f"更新work-title: {title_text}")

        # 2. 处理movement-title
        movement_title = root.find('movement-title')
        if movement_title is None and subtitle_text:
            # 只有识别到副标题时才创建movement-title
            movement_title = ET.Element('movement-title')
            movement_title.text = subtitle_text
            work_index = list(root).index(work) if work in root else 0
            root.insert(work_index + 1, movement_title)
            logger.info(f"添加movement-title: {subtitle_text}")
        elif movement_title is not None and subtitle_text:
            # 如果存在且识别到副标题，更新它
            movement_title.text = subtitle_text
            logger.info(f"更新movement-title: {subtitle_text}")

        # 3. 更新identification信息，优先使用结构化信息
        composer_text = structured_info.get('composer', '') or ''
        if composer_text and composer_text.strip():
            composer_text = composer_text.strip()
        else:
            composer_text = self._extract_composer_from_texts(texts)

        arranger_text = structured_info.get('arranger', '') or ''
        if arranger_text and arranger_text.strip():
            arranger_text = arranger_text.strip()
        else:
            arranger_text = self._extract_arranger_from_texts(texts)

        original_artist_text = structured_info.get('original_artist', '') or ''
        if original_artist_text and original_artist_text.strip():
            original_artist_text = original_artist_text.strip()
        else:
            original_artist_text = ''

        logger.info(f"使用创作者信息: 作曲='{composer_text}', 编曲='{arranger_text}', 原唱='{original_artist_text}'")

        identification = root.find('identification')
        if identification is not None:
            # 更新composer信息（只有识别到时才更新）
            if composer_text:
                existing_composer = identification.find('creator[@type="composer"]')
                if existing_composer is not None:
                    existing_composer.text = composer_text
                    logger.info(f"更新composer信息: {composer_text}")
                else:
                    # 添加新的composer
                    composer = ET.Element('creator')
                    composer.set('type', 'composer')
                    composer.text = composer_text
                    identification.insert(0, composer)
                    logger.info(f"添加新的composer信息: {composer_text}")

            # 更新arranger信息（只有识别到时才更新）
            if arranger_text:
                existing_arranger = identification.find('creator[@type="arranger"]')
                if existing_arranger is not None:
                    existing_arranger.text = arranger_text
                    logger.info(f"更新arranger信息: {arranger_text}")
                else:
                    # 添加新的arranger
                    arranger = ET.Element('creator')
                    arranger.set('type', 'arranger')
                    arranger.text = arranger_text
                    identification.insert(1, arranger)
                    logger.info(f"添加新的arranger信息: {arranger_text}")

            # 更新原唱信息（使用lyricist类型）
            if original_artist_text:
                existing_artist = identification.find('creator[@type="lyricist"]')
                if existing_artist is not None:
                    existing_artist.text = original_artist_text
                    logger.info(f"更新原唱信息: {original_artist_text}")
                else:
                    # 添加新的原唱信息
                    artist = ET.Element('creator')
                    artist.set('type', 'lyricist')
                    artist.text = original_artist_text
                    identification.insert(2, artist)
                    logger.info(f"添加新的原唱信息: {original_artist_text}")

        # 4. 清除旧的credit信息，但保留版权信息
        # 先保存Audiveris识别的版权信息 - 优先从rights元素中获取
        audiveris_copyright_text = None
        identification = root.find('identification')
        if identification is not None:
            rights = identification.find('rights')
            if rights is not None and rights.text:
                audiveris_copyright_text = rights.text.strip()
                # 保留rights元素，不删除，让它正常显示
                logger.info(f"找到rights元素中的版权信息: {audiveris_copyright_text}")

        # 如果rights元素中没有版权信息，则从credit元素中查找（作为备用）
        if not audiveris_copyright_text:
            credits_to_remove = root.findall('credit')
            for credit in credits_to_remove:
                credit_words = credit.find('credit-words')
                if credit_words is not None and credit_words.text:
                    text = credit_words.text.strip()
                    # 检查是否是版权信息（短文本且在底部）
                    if (len(text) <= 5 and
                        credit_words.get('default-y') and
                        int(credit_words.get('default-y', '0')) < 200):
                        audiveris_copyright_text = text
                        break

        # 保存Audiveris的版权信息credit元素，只移除其他credit元素
        audiveris_copyright_credit = None
        credits_to_remove = []

        for credit in root.findall('credit'):
            credit_words = credit.find('credit-words')
            if credit_words is not None and credit_words.text:
                text = credit_words.text.strip()
                # 识别版权信息credit元素（只匹配明确的版权标识）
                is_copyright = any(keyword in text.lower() for keyword in ['©', 'copyright', '版权', 'rights'])
                is_title = (
                    len(text) > 5 and (' ' in text or any('\u4e00' <= c <= '\u9fff' for c in text))
                )

                if is_copyright and not is_title:
                    # 这是版权信息，保留它
                    audiveris_copyright_credit = credit
                    logger.info(f"🔍 保留Audiveris版权信息credit: '{text}'")
                else:
                    # 这是其他信息（标题等），移除它
                    credits_to_remove.append(credit)
            else:
                # 空的credit元素，移除
                credits_to_remove.append(credit)

        # 移除非版权信息的credit元素
        for credit in credits_to_remove:
            root.remove(credit)

        logger.info(f"已移除 {len(credits_to_remove)} 个非版权信息credit元素，保留了版权信息credit")

        # 保留identification中的rights元素（Audiveris的版权信息）
        # 不删除rights元素，让版权信息正常显示

        # 添加新的标准credit信息
        # 找到part-list元素，在它之前插入credit元素
        part_list = root.find('part-list')
        logger.info(f"查找part-list元素: {'找到' if part_list is not None else '未找到'}")
        if part_list is not None:
            part_list_index = list(root).index(part_list)

            # 获取页面布局信息来确定正确的坐标
            page_layout = root.find('.//page-layout')
            if page_layout is not None:
                page_width_elem = page_layout.find('page-width')
                page_height_elem = page_layout.find('page-height')
                page_width = int(page_width_elem.text) if page_width_elem is not None else 800
                page_height = int(page_height_elem.text) if page_height_elem is not None else 1200
            else:
                page_width = 800
                page_height = 1200

            center_x = page_width // 2
            right_x = page_width - 50  # 右对齐，留50像素边距

            # 主标题 - 居中，更高的位置
            credit1 = ET.Element('credit')
            credit1.set('page', '1')
            credit_words1 = ET.SubElement(credit1, 'credit-words')
            credit_words1.set('default-x', str(center_x))
            credit_words1.set('default-y', '1650')  # 提高位置
            credit_words1.set('font-family', 'serif')
            credit_words1.set('font-size', '20')
            credit_words1.set('font-weight', 'bold')
            credit_words1.set('justify', 'center')
            credit_words1.set('halign', 'center')
            # 使用OCR识别的主标题文字
            if title_text:
                credit_words1.text = title_text
                root.insert(part_list_index, credit1)
                logger.info(f"添加主标题credit元素: {title_text}")
            else:
                logger.info("未找到主标题，跳过主标题credit元素")

            # 副标题 - 居中，增加与主标题的间距
            credit2 = ET.Element('credit')
            credit2.set('page', '1')
            credit_words2 = ET.SubElement(credit2, 'credit-words')
            credit_words2.set('default-x', str(center_x))
            credit_words2.set('default-y', '1580')  # 调整到Y=1580
            credit_words2.set('font-family', 'serif')
            credit_words2.set('font-size', '12')               # 从14px调整到12px
            credit_words2.set('justify', 'center')
            credit_words2.set('halign', 'center')
            # 使用OCR识别的副标题文字
            if subtitle_text:
                credit_words2.text = subtitle_text
                # 计算插入位置：如果添加了主标题，则在主标题后插入
                insert_index = part_list_index + (1 if title_text else 0)
                root.insert(insert_index, credit2)
                logger.info(f"添加副标题credit元素: {subtitle_text}")
            else:
                logger.info("未找到副标题，跳过副标题credit元素")

            # 原唱信息 - 独立的credit元素
            current_insert_index = part_list_index
            if title_text:
                current_insert_index += 1
            if subtitle_text:
                current_insert_index += 1

            if original_artist_text:
                credit_original = ET.Element('credit')
                credit_original.set('page', '1')
                credit_words_original = ET.SubElement(credit_original, 'credit-words')
                credit_words_original.set('default-x', '845')
                credit_words_original.set('default-y', '1540')
                credit_words_original.set('font-family', 'serif')
                credit_words_original.set('font-size', '9')
                credit_words_original.set('justify', 'right')
                credit_words_original.set('halign', 'right')
                credit_words_original.set('data-original-artist', 'true')  # 标记为原唱信息
                credit_words_original.text = original_artist_text
                root.insert(current_insert_index, credit_original)
                current_insert_index += 1
                logger.info(f"添加原唱信息credit元素: {original_artist_text}")

            # 编曲信息 - 独立的credit元素
            if arranger_text:
                credit_arranger = ET.Element('credit')
                credit_arranger.set('page', '1')
                credit_words_arranger = ET.SubElement(credit_arranger, 'credit-words')
                credit_words_arranger.set('default-x', '845')
                credit_words_arranger.set('default-y', '1500')
                credit_words_arranger.set('font-family', 'serif')
                credit_words_arranger.set('font-size', '9')
                credit_words_arranger.set('justify', 'right')
                credit_words_arranger.set('halign', 'right')
                credit_words_arranger.set('data-arranger', 'true')  # 标记为编曲信息
                credit_words_arranger.text = arranger_text
                root.insert(current_insert_index, credit_arranger)
                logger.info(f"添加编曲信息credit元素: {arranger_text}")

            if not original_artist_text and not arranger_text:
                logger.info("未找到原唱和编曲信息，跳过相关credit元素")

            # 处理版权信息：只有PaddleOCR识别到版权信息时才进行替换
            # 如果PaddleOCR没有识别到版权信息，就保留Audiveris原有的版权信息不做修改
            copyright_text = self._extract_copyright_info(texts)

            if copyright_text:
                # 只有PaddleOCR识别到版权信息时，才查找并替换Audiveris的版权信息
                # 优先替换rights元素中的版权信息
                identification = root.find('identification')
                if identification is not None:
                    rights = identification.find('rights')
                    if rights is not None and rights.text:
                        old_text = rights.text.strip()
                        rights.text = copyright_text
                        logger.info(f"✅ 替换rights元素版权信息: '{old_text}' → '{copyright_text}'")
                    else:
                        # 如果没有rights元素，查找credit元素
                        existing_copyright_credit = None
                        for credit in root.findall('credit'):
                            credit_words = credit.find('credit-words')
                            if credit_words is not None and credit_words.text:
                                text = credit_words.text.strip()
                                # 识别版权信息（只匹配明确的版权标识）
                                is_copyright = any(keyword in text.lower() for keyword in ['©', 'copyright', '版权', 'rights'])
                                is_title = (
                                    len(text) > 5 and (' ' in text or any('\u4e00' <= c <= '\u9fff' for c in text))
                                )

                                if is_copyright and not is_title:
                                    existing_copyright_credit = credit
                                    logger.info(f"🔍 找到credit版权信息: '{text}'")
                                    break

                        if existing_copyright_credit is not None:
                            # 替换credit版权信息
                            credit_words = existing_copyright_credit.find('credit-words')
                            old_text = credit_words.text
                            credit_words.text = copyright_text
                            logger.info(f"✅ 替换credit版权信息: '{old_text}' → '{copyright_text}'")
                        else:
                            logger.info(f"ℹ️ 未找到Audiveris版权信息，PaddleOCR识别到: {copyright_text}，但不添加新的")

                logger.info("版权信息处理完成")
            else:
                # PaddleOCR没有识别到版权信息，完全保留Audiveris原有的版权信息
                logger.info("ℹ️ PaddleOCR未识别到版权信息，保留Audiveris原有版权信息")

    def _add_title_credits(self, root: ET.Element, texts: List[Dict]):
        """添加标题credit信息"""
        # 这个方法已经在_add_standard_title_info中实现了
        # 保留这个方法以防其他地方调用
        pass

    def _remove_original_title_credits(self, root):
        """移除原始的标题credit元素，避免重复显示"""
        # 先移除所有现有的credit元素，然后重新添加我们识别的文字
        credits_to_remove = []

        # 查找所有credit元素
        for credit in root.findall('credit'):
            credits_to_remove.append(credit)

        # 移除所有credit元素
        for credit in credits_to_remove:
            root.remove(credit)

        # 同时移除可能存在的单独credit-words元素
        credit_words_to_remove = []
        for credit_words in root.findall('.//credit-words'):
            if credit_words.getparent() is None or credit_words.getparent().tag != 'credit':
                credit_words_to_remove.append(credit_words)

        for credit_words in credit_words_to_remove:
            if credit_words.getparent() is not None:
                credit_words.getparent().remove(credit_words)

    def _extract_title_from_texts(self, texts: List[Dict]) -> str:
        """从识别的文字中提取标题"""
        for text in texts:
            text_content = text.get('text', '').strip()
            # 标题通常在顶部，字体较大，内容较短
            if (len(text_content) > 2 and len(text_content) < 30 and
                not any(keyword in text_content.lower() for keyword in ['作曲', '编曲', '原唱', '演唱', '©', 'copyright'])):
                return text_content
        return ""

    def _extract_subtitle_from_texts(self, texts: List[Dict]) -> str:
        """从识别的文字中提取副标题"""
        for text in texts:
            text_content = text.get('text', '').strip()
            # 副标题通常包含特定的标识符或格式（使用通用的格式特征，不写死具体内容）
            if ('·' in text_content or '—' in text_content or '－' in text_content or
                any(keyword in text_content for keyword in ['主题曲', '插曲', 'OST', '主题', '片尾曲', '片头曲'])):
                return text_content
        return ""

    def _extract_composer_from_texts(self, texts: List[Dict]) -> str:
        """从识别的文字中提取作曲信息"""
        for text in texts:
            text_content = text.get('text', '').strip()
            if '作曲' in text_content:
                # 提取作曲后面的内容
                parts = text_content.split('作曲')
                if len(parts) > 1:
                    composer = parts[1].strip().replace('：', '').replace(':', '')
                    return composer
        return ""

    def _extract_arranger_from_texts(self, texts: List[Dict]) -> str:
        """从识别的文字中提取编曲信息"""
        for text in texts:
            text_content = text.get('text', '').strip()
            if '编曲' in text_content:
                # 提取编曲后面的内容
                parts = text_content.split('编曲')
                if len(parts) > 1:
                    arranger = parts[1].strip().replace('：', '').replace(':', '')
                    return arranger
        return ""

    def _extract_copyright_info(self, texts: List[Dict]) -> str:
        """提取版权信息 - 只有真正识别到版权相关内容时才返回"""
        copyright_texts = []

        # 按Y坐标排序，找到底部的文字
        # 检查texts中是否有center_y字段，如果没有则使用y字段或跳过排序
        try:
            if texts and 'center_y' in texts[0]:
                sorted_texts = sorted(texts, key=lambda x: x['center_y'], reverse=True)
                max_y = max(text['center_y'] for text in sorted_texts)
                bottom_threshold = max_y * 0.8
                bottom_texts = [text for text in sorted_texts if text['center_y'] > bottom_threshold]
            elif texts and 'y' in texts[0]:
                sorted_texts = sorted(texts, key=lambda x: x['y'], reverse=True)
                max_y = max(text['y'] for text in sorted_texts)
                bottom_threshold = max_y * 0.8
                bottom_texts = [text for text in sorted_texts if text['y'] > bottom_threshold]
            else:
                # 如果没有坐标信息，直接使用所有文字
                bottom_texts = texts
        except (KeyError, TypeError):
            # 如果出现任何错误，直接使用所有文字
            bottom_texts = texts

            # 查找明确的版权相关文字（更严格的匹配，排除MP）
            explicit_copyright_keywords = ['©', 'copyright', '版权', 'rights']

            for text in bottom_texts:
                text_content = text['text'].strip()
                # 只匹配明确的版权关键词，不包括MP等可能的误识别
                if any(keyword.lower() in text_content.lower() for keyword in explicit_copyright_keywords):
                    copyright_texts.append(text_content)

        # 不再添加默认版权信息，只有真正识别到时才返回
        return ' '.join(copyright_texts) if copyright_texts else None

    def _should_skip_text(self, text: str) -> bool:
        """判断是否应该跳过这个文字（不添加到credit中）"""
        if not text:
            return True

        # 不应该过滤特定的标题文字，所有文字都应该基于OCR识别结果动态处理
        # 删除了写死的标题过滤逻辑

        # 过滤明显的噪音文字（过短或过长的文字）
        if len(text) < 2 or len(text) > 100:
            return True

        return False

    def process_with_text_integration(self, image_path: str, audiveris_output_dir: str) -> Dict[str, Any]:
        """
        处理图片并集成文字识别结果
        
        Args:
            image_path: 图片路径
            audiveris_output_dir: Audiveris输出目录
            
        Returns:
            处理结果
        """
        try:
            # 1. 找到Audiveris生成的MXL文件
            image_name = os.path.splitext(os.path.basename(image_path))[0]
            mxl_file = os.path.join(audiveris_output_dir, f"{image_name}.mxl")
            
            if not os.path.exists(mxl_file):
                return {
                    'success': False,
                    'error': f"未找到MusicXML文件: {mxl_file}"
                }
            
            # 2. 解压MXL文件
            import zipfile
            import tempfile
            
            with tempfile.TemporaryDirectory() as temp_dir:
                with zipfile.ZipFile(mxl_file, 'r') as zip_ref:
                    zip_ref.extractall(temp_dir)
                
                # 找到XML文件
                xml_file = None
                for file in os.listdir(temp_dir):
                    if file.endswith('.xml'):
                        xml_file = os.path.join(temp_dir, file)
                        break
                
                if not xml_file:
                    return {
                        'success': False,
                        'error': "MXL文件中未找到XML文件"
                    }
                
                # 3. 集成文字信息到临时XML文件
                enhanced_xml_file = os.path.join(audiveris_output_dir, f"{image_name}_enhanced.xml")
                integration_result = self.integrate_text_to_musicxml(image_path, xml_file, enhanced_xml_file)

                if integration_result['success']:
                    # 确保enhanced_xml_file路径正确
                    integration_result['enhanced_xml_file'] = enhanced_xml_file
                    logger.info(f"增强版XML文件已保存: {enhanced_xml_file}")

                    # 5. 重新打包MXL文件，使用增强版XML
                    enhanced_mxl_file = os.path.join(audiveris_output_dir, f"{image_name}_enhanced.mxl")

                    with zipfile.ZipFile(enhanced_mxl_file, 'w', zipfile.ZIP_DEFLATED) as zip_ref:
                        # 添加增强版XML文件（包含所有文字集成修正）
                        zip_ref.write(enhanced_xml_file, os.path.basename(xml_file))

                        # 复制其他文件
                        with zipfile.ZipFile(mxl_file, 'r') as original_zip:
                            for item in original_zip.infolist():
                                if not item.filename.endswith('.xml'):
                                    data = original_zip.read(item.filename)
                                    zip_ref.writestr(item, data)

                    integration_result['enhanced_mxl_file'] = enhanced_mxl_file
                    logger.info(f"增强版MXL文件已保存: {enhanced_mxl_file}")
                
                return integration_result
                
        except Exception as e:
            logger.error(f"文字集成处理失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _save_formatted_xml(self, tree: ET.ElementTree, output_file: str):
        """
        保存格式化的XML文件，确保MuseScore能正确解析
        """
        try:
            # 格式化XML
            self._indent_xml(tree.getroot())

            # 保存文件
            tree.write(output_file, encoding='utf-8', xml_declaration=True)
            logger.info(f"格式化XML已保存: {output_file}")

        except Exception as e:
            logger.error(f"保存格式化XML失败: {e}")
            # 回退到普通保存
            tree.write(output_file, encoding='utf-8', xml_declaration=True)

    def _indent_xml(self, elem, level=0):
        """
        格式化XML元素，添加适当的缩进和换行
        """
        indent = "\n" + level * "  "
        if len(elem):
            if not elem.text or not (elem.text and elem.text.strip()):
                elem.text = indent + "  "
            if not elem.tail or not (elem.tail and elem.tail.strip()):
                elem.tail = indent
            for child in elem:
                self._indent_xml(child, level + 1)
            if not child.tail or not (child.tail and child.tail.strip()):
                child.tail = indent
        else:
            if level and (not elem.tail or not (elem.tail and elem.tail.strip())):
                elem.tail = indent
