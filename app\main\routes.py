"""
主要路由 - 页面渲染
"""
from flask import render_template, request, current_app
from app.main import bp
from app.core.processor import PianoFingeringProcessor

@bp.route('/')
def index():
    """主页"""
    return render_template('index.html')

@bp.route('/upload')
def upload():
    """上传页面"""
    return render_template('upload.html')

@bp.route('/test_upload')
def test_upload():
    """测试上传页面"""
    return render_template('test_upload.html')

@bp.route('/history')
def history():
    """历史记录页面"""
    return render_template('history.html')

@bp.route('/xml-editor')
def xml_editor():
    """XML编辑器页面"""
    return render_template('xml_editor.html')

@bp.route('/visual-editor')
def visual_editor():
    """可视化编辑器页面"""
    return render_template('visual_editor.html')

@bp.route('/simple-visual-editor')
def simple_visual_editor():
    """简化可视化编辑器页面"""
    return render_template('simple_visual_editor.html')

@bp.route('/fingering-result')
def fingering_result():
    """指法生成结果页面"""
    return render_template('fingering_result.html')

@bp.route('/about')
def about():
    """关于页面"""
    processor = PianoFingeringProcessor()
    engine_status = processor.get_engine_status()
    return render_template('about.html', engine_status=engine_status)

@bp.route('/help')
def help():
    """帮助页面"""
    return render_template('help.html')
