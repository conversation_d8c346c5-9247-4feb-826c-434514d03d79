/**
 * MusicXML编辑器JavaScript
 */

class XMLEditor {
    constructor() {
        this.taskId = null;
        this.originalXML = '';
        this.currentXML = '';
        this.init();
    }

    init() {
        // 从URL获取任务ID
        const urlParams = new URLSearchParams(window.location.search);
        this.taskId = urlParams.get('task_id');
        
        if (!this.taskId) {
            alert('缺少任务ID，请重新上传文件');
            window.location.href = '/upload';
            return;
        }

        this.bindEvents();
        this.loadTaskData();
    }

    bindEvents() {
        // 按钮事件
        document.getElementById('refresh-preview').addEventListener('click', () => this.generatePreview());
        document.getElementById('switch-to-visual').addEventListener('click', () => this.switchToVisual());
        document.getElementById('view-xml').addEventListener('click', () => this.showXMLModal());
        document.getElementById('format-xml').addEventListener('click', () => this.formatXML());
        document.getElementById('validate-xml').addEventListener('click', () => this.validateXML());
        document.getElementById('reset-xml').addEventListener('click', () => this.resetXML());
        document.getElementById('back-to-upload').addEventListener('click', () => this.backToUpload());
        document.getElementById('generate-preview').addEventListener('click', () => this.generatePreview());
        document.getElementById('proceed-to-fingering').addEventListener('click', () => this.proceedToFingering());
        document.getElementById('copy-xml').addEventListener('click', () => this.copyXML());
        document.getElementById('apply-title-changes').addEventListener('click', () => this.applyTitleChanges());

        // XML编辑器变化事件
        document.getElementById('xml-editor').addEventListener('input', (e) => {
            this.currentXML = e.target.value;
        });
    }

    async loadTaskData() {
        try {
            const response = await fetch(`/api/status/${this.taskId}`);
            const data = await response.json();
            
            if (data.state === 'SUCCESS' && data.result && data.result.files) {
                // 调试信息：显示所有可用文件
                console.log('任务文件列表:', Object.keys(data.result.files));
                console.log('enhanced_xml存在:', !!data.result.files.enhanced_xml);
                console.log('extracted_xml存在:', !!data.result.files.extracted_xml);

                // 加载原始图片
                if (data.result.files.preprocessed_image) {
                    document.getElementById('original-image').src = `/api/file/${this.taskId}/preprocessed_image`;
                }

                // 加载XML内容 - 优先使用增强版XML（包含文字集成信息）
                if (data.result.files.enhanced_xml || data.result.files.extracted_xml) {
                    await this.loadXMLContent(data.result.files);
                }
                
                // 生成初始预览
                this.generatePreview();
            } else {
                throw new Error('任务数据不完整');
            }
        } catch (error) {
            console.error('加载任务数据失败:', error);
            alert('加载数据失败，请重新上传文件');
            window.location.href = '/upload';
        }
    }

    async loadXMLContent(files) {
        try {
            // 调试信息：显示传入的files对象
            console.log('loadXMLContent接收到的files:', files);
            console.log('files.enhanced_xml值:', files ? files.enhanced_xml : 'files为空');
            console.log('files.extracted_xml值:', files ? files.extracted_xml : 'files为空');

            // 优先使用增强版XML（包含文字集成信息），如果不存在则使用原始XML
            let xmlFileType = 'extracted_xml';
            const hasEnhancedXml = files && files.enhanced_xml && typeof files.enhanced_xml === 'string' && files.enhanced_xml.length > 0;

            if (hasEnhancedXml) {
                xmlFileType = 'enhanced_xml';
                console.log('✅ 使用增强版XML文件（包含文字集成信息）');
                console.log('增强版XML路径:', files.enhanced_xml);
            } else {
                console.log('⚠️ 使用原始XML文件');
                if (files) {
                    console.log('enhanced_xml检查结果:');
                    console.log('  - files存在:', !!files);
                    console.log('  - enhanced_xml字段存在:', 'enhanced_xml' in files);
                    console.log('  - enhanced_xml值:', files.enhanced_xml);
                    console.log('  - enhanced_xml类型:', typeof files.enhanced_xml);
                    console.log('  - enhanced_xml长度:', files.enhanced_xml ? files.enhanced_xml.length : 'N/A');
                }
            }

            const response = await fetch(`/api/file/${this.taskId}/${xmlFileType}`);
            const xmlContent = await response.text();

            this.originalXML = xmlContent;
            this.currentXML = xmlContent;

            // 显示在编辑器中
            document.getElementById('xml-editor').value = xmlContent;

            // 解析并显示标题信息
            this.parseTitleInfo(xmlContent);
        } catch (error) {
            console.error('加载XML内容失败:', error);
            alert('加载XML内容失败');
        }
    }

    async generatePreview() {
        const previewImg = document.getElementById('recognition-preview');
        const loadingDiv = document.getElementById('preview-loading');
        const errorDiv = document.getElementById('preview-error');

        // 显示加载状态
        previewImg.classList.add('d-none');
        errorDiv.classList.add('d-none');
        loadingDiv.classList.remove('d-none');

        try {
            // 1. 先保存修改到服务器端的XML文件
            console.log('保存XML修改到服务器...');
            const saveResponse = await fetch('/api/save-visual-changes', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_id: this.taskId,
                    xml_content: this.currentXML
                })
            });

            const saveResult = await saveResponse.json();

            if (!saveResult.success) {
                throw new Error(saveResult.error || '保存修改失败');
            }

            console.log('XML修改已保存，开始生成预览...');

            // 2. 然后生成预览
            const response = await fetch('/api/generate-preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_id: this.taskId,
                    xml_content: this.currentXML
                })
            });

            const data = await response.json();

            if (data.success && data.preview_url) {
                previewImg.src = data.preview_url + '?t=' + Date.now(); // 防止缓存
                previewImg.classList.remove('d-none');
                console.log('预览生成成功');
            } else {
                throw new Error(data.error || '预览生成失败');
            }
        } catch (error) {
            console.error('保存并预览失败:', error);
            errorDiv.classList.remove('d-none');
            // 显示错误信息
            const errorMessage = document.createElement('div');
            errorMessage.className = 'alert alert-danger mt-2';
            errorMessage.textContent = '保存并预览失败: ' + error.message;
            errorDiv.appendChild(errorMessage);
        } finally {
            loadingDiv.classList.add('d-none');
        }
    }

    switchToVisual() {
        if (confirm('确定要切换到可视化编辑模式吗？')) {
            window.location.href = `/visual-editor?task_id=${this.taskId}`;
        }
    }

    showXMLModal() {
        document.getElementById('xml-display').textContent = this.currentXML;
        const modal = new bootstrap.Modal(document.getElementById('xmlModal'));
        modal.show();
    }

    formatXML() {
        try {
            // 简单的XML格式化
            const formatted = this.formatXMLString(this.currentXML);
            this.currentXML = formatted;
            document.getElementById('xml-editor').value = formatted;
            
            this.showMessage('XML格式化完成', 'success');
        } catch (error) {
            this.showMessage('XML格式化失败: ' + error.message, 'danger');
        }
    }

    formatXMLString(xml) {
        try {
            // 使用更安全的格式化方法，避免破坏XML内容
            return this.safeXMLFormat(xml);

        } catch (error) {
            // 如果格式化失败，返回原始XML
            console.warn('XML格式化失败:', error);
            this.showMessage('XML格式化失败: ' + error.message, 'warning');
            return xml;
        }
    }

    safeXMLFormat(xml) {
        // 更安全的XML格式化，保护标签内容不被破坏
        let formatted = '';
        let indent = 0;
        const tab = '  ';

        // 移除多余的空白，但保护标签内的内容
        let cleaned = xml.trim();

        // 使用正则表达式匹配XML标签和内容
        const xmlRegex = /(<[^>]+>)|([^<]+)/g;
        const tokens = [];
        let match;

        while ((match = xmlRegex.exec(cleaned)) !== null) {
            if (match[1]) {
                // 这是一个XML标签
                tokens.push({ type: 'tag', content: match[1] });
            } else if (match[2] && match[2].trim()) {
                // 这是标签间的文本内容
                tokens.push({ type: 'text', content: match[2].trim() });
            }
        }

        for (let i = 0; i < tokens.length; i++) {
            const token = tokens[i];

            if (token.type === 'tag') {
                const tag = token.content;

                if (tag.match(/^<\/\w/)) {
                    // 结束标签
                    indent = Math.max(0, indent - 1);
                    // 检查前一个token是否是文本，如果是则不换行
                    if (i > 0 && tokens[i-1].type === 'text') {
                        formatted += tag;
                    } else {
                        if (formatted && !formatted.endsWith('\n')) {
                            formatted += '\n';
                        }
                        formatted += tab.repeat(indent) + tag;
                    }
                } else if (tag.match(/^<\w[^>]*[^\/]>$/)) {
                    // 开始标签（非自闭合）
                    if (formatted && !formatted.endsWith('\n')) {
                        formatted += '\n';
                    }
                    formatted += tab.repeat(indent) + tag;

                    // 检查下一个token是否是文本
                    if (i + 1 < tokens.length && tokens[i + 1].type === 'text') {
                        // 如果下一个是文本，不换行
                        indent++;
                    } else {
                        // 如果下一个不是文本，换行并增加缩进
                        indent++;
                    }
                } else if (tag.match(/^<\w[^>]*\/>$/)) {
                    // 自闭合标签
                    if (formatted && !formatted.endsWith('\n')) {
                        formatted += '\n';
                    }
                    formatted += tab.repeat(indent) + tag;
                } else if (tag.match(/^<[?!]/)) {
                    // XML声明或注释
                    if (formatted) {
                        formatted += '\n';
                    }
                    formatted += tag;
                }
            } else if (token.type === 'text') {
                // 文本内容，直接添加不换行
                formatted += token.content;
            }
        }

        return formatted.trim();
    }

    addXMLIndentation(xml) {
        // 添加适当的缩进
        let formatted = '';
        let indent = 0;
        const tab = '  ';

        // 彻底清理所有空白字符，包括换行符
        xml = xml.replace(/>\s+</g, '><').replace(/\s+/g, ' ').trim();

        // 按标签分割，保留标签
        const tokens = xml.split(/(<[^>]*>)/).filter(token => token.length > 0);

        for (let i = 0; i < tokens.length; i++) {
            const token = tokens[i].trim();
            if (!token) continue;

            if (token.match(/^<\/\w/)) {
                // 结束标签
                indent = Math.max(0, indent - 1);
                if (formatted && !formatted.endsWith('\n')) {
                    formatted += '\n';
                }
                formatted += tab.repeat(indent) + token;
            } else if (token.match(/^<\w[^>]*[^\/]>$/)) {
                // 开始标签（非自闭合）
                if (formatted && !formatted.endsWith('\n')) {
                    formatted += '\n';
                }
                formatted += tab.repeat(indent) + token;
                indent++;
            } else if (token.match(/^<\w[^>]*\/>$/)) {
                // 自闭合标签
                if (formatted && !formatted.endsWith('\n')) {
                    formatted += '\n';
                }
                formatted += tab.repeat(indent) + token;
            } else if (token.match(/^<[?!]/)) {
                // XML声明或注释
                if (formatted) {
                    formatted += '\n';
                }
                formatted += token;
            } else if (token.trim()) {
                // 文本内容
                formatted += token;
            }
        }

        // 清理多余的空行
        return formatted.replace(/\n\s*\n/g, '\n').trim();
    }

    simpleXMLFormat(xml) {
        // 简单的备用格式化方法
        let indent = 0;
        const tab = '  ';

        // 彻底清理现有格式，移除所有多余空白
        let cleaned = xml.replace(/>\s+</g, '><').replace(/\s+/g, ' ').trim();

        // 在标签间添加换行
        cleaned = cleaned.replace(/></g, '>\n<');

        const lines = cleaned.split('\n');
        const result = [];

        for (const line of lines) {
            const trimmed = line.trim();
            if (!trimmed) continue;

            // 结束标签减少缩进
            if (trimmed.match(/^<\/\w/)) {
                indent = Math.max(0, indent - 1);
            }

            result.push(tab.repeat(indent) + trimmed);

            // 开始标签（非自闭合）增加缩进
            if (trimmed.match(/^<\w[^>]*[^\/]>$/)) {
                indent++;
            }
        }

        return result.join('\n');
    }

    async validateXML() {
        try {
            const response = await fetch('/api/validate-xml', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    xml_content: this.currentXML
                })
            });
            
            const data = await response.json();
            const resultDiv = document.getElementById('validation-result');
            
            if (data.valid) {
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>XML格式验证通过
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>XML格式错误: ${data.error}
                    </div>
                `;
            }
            
            resultDiv.classList.remove('d-none');
            
            // 3秒后自动隐藏
            setTimeout(() => {
                resultDiv.classList.add('d-none');
            }, 3000);
            
        } catch (error) {
            this.showMessage('验证失败: ' + error.message, 'danger');
        }
    }

    resetXML() {
        if (confirm('确定要重置为原始识别结果吗？所有修改将丢失。')) {
            this.currentXML = this.originalXML;
            document.getElementById('xml-editor').value = this.originalXML;
            this.showMessage('已重置为原始XML', 'info');
        }
    }

    backToUpload() {
        if (confirm('确定要返回上传页面吗？当前的修改将丢失。')) {
            window.location.href = '/upload';
        }
    }

    async proceedToFingering() {
        if (!this.currentXML.trim()) {
            alert('XML内容不能为空');
            return;
        }
        
        try {
            // 保存修改后的XML并继续指法生成
            const response = await fetch('/api/save-xml-and-generate-fingering', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_id: this.taskId,
                    xml_content: this.currentXML
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // 跳转到指法生成页面
                window.location.href = `/fingering-result?task_id=${this.taskId}`;
            } else {
                throw new Error(data.error || '处理失败');
            }
        } catch (error) {
            console.error('处理失败:', error);
            alert('处理失败: ' + error.message);
        }
    }

    copyXML() {
        navigator.clipboard.writeText(this.currentXML).then(() => {
            this.showMessage('XML已复制到剪贴板', 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            this.showMessage('复制失败', 'danger');
        });
    }

    showMessage(message, type = 'info') {
        // 创建临时消息提示
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }

    parseTitleInfo(xmlContent) {
        try {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xmlContent, 'application/xml');

            // 解析主标题
            const workTitle = xmlDoc.querySelector('work work-title');
            if (workTitle) {
                document.getElementById('xml-main-title').value = workTitle.textContent.trim();
            }

            // 解析副标题
            const movementTitle = xmlDoc.querySelector('movement-title');
            if (movementTitle) {
                document.getElementById('xml-sub-title').value = movementTitle.textContent.trim();
            }

            // 解析创作者信息 - 添加调试日志和防止覆盖
            const creators = xmlDoc.querySelectorAll('creator');
            console.log('XML编辑器 - 找到的creator元素数量:', creators.length);

            let originalArtistSet = false;
            let arrangerSet = false;

            creators.forEach((creator, index) => {
                const type = creator.getAttribute('type');
                const content = creator.textContent.trim();
                console.log(`XML编辑器 - Creator ${index}: type="${type}", content="${content}"`);

                // 只取第一个匹配的，避免被后续错误信息覆盖
                if ((type === 'composer' || type === 'lyricist') && !originalArtistSet) {
                    document.getElementById('xml-original-artist').value = content;
                    originalArtistSet = true;
                    console.log('XML编辑器 - 设置原唱为:', content);
                } else if (type === 'arranger' && !arrangerSet) {
                    document.getElementById('xml-arranger').value = content;
                    arrangerSet = true;
                    console.log('XML编辑器 - 设置编曲为:', content);
                }
            });

            // 解析版权信息
            const rights = xmlDoc.querySelector('rights');
            if (rights) {
                document.getElementById('xml-copyright').value = rights.textContent.trim();
            }

        } catch (error) {
            console.error('解析标题信息失败:', error);
        }
    }

    applyTitleChanges() {
        try {
            // 获取表单中的标题信息
            const titleInfo = {
                mainTitle: document.getElementById('xml-main-title').value.trim(),
                subTitle: document.getElementById('xml-sub-title').value.trim(),
                originalArtist: document.getElementById('xml-original-artist').value.trim(),
                arranger: document.getElementById('xml-arranger').value.trim(),
                copyright: document.getElementById('xml-copyright').value.trim()
            };

            // 更新XML内容
            this.currentXML = this.updateXMLTitleInfo(this.currentXML, titleInfo);

            // 更新编辑器显示
            document.getElementById('xml-editor').value = this.currentXML;

            this.showAlert('标题信息已应用到XML中', 'success');

        } catch (error) {
            console.error('应用标题修改失败:', error);
            this.showAlert('应用标题修改失败', 'danger');
        }
    }

    updateXMLTitleInfo(xml, titleInfo) {
        try {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xml, 'application/xml');

            // 更新主标题 (work-title)
            let workTitle = xmlDoc.querySelector('work work-title');
            if (titleInfo.mainTitle) {
                if (!workTitle) {
                    // 如果不存在work元素，创建它
                    let work = xmlDoc.querySelector('work');
                    if (!work) {
                        work = xmlDoc.createElement('work');
                        const scorePartwise = xmlDoc.querySelector('score-partwise');
                        if (scorePartwise) {
                            scorePartwise.insertBefore(work, scorePartwise.firstChild);
                        }
                    }
                    workTitle = xmlDoc.createElement('work-title');
                    work.appendChild(workTitle);
                }
                workTitle.textContent = titleInfo.mainTitle;
            } else if (workTitle) {
                workTitle.remove();
            }

            // 更新副标题 (movement-title)
            let movementTitle = xmlDoc.querySelector('movement-title');
            if (titleInfo.subTitle) {
                if (!movementTitle) {
                    movementTitle = xmlDoc.createElement('movement-title');
                    const scorePartwise = xmlDoc.querySelector('score-partwise');
                    if (scorePartwise) {
                        // 插入到work元素之后
                        const work = xmlDoc.querySelector('work');
                        if (work && work.nextSibling) {
                            scorePartwise.insertBefore(movementTitle, work.nextSibling);
                        } else {
                            scorePartwise.insertBefore(movementTitle, scorePartwise.firstChild);
                        }
                    }
                }
                movementTitle.textContent = titleInfo.subTitle;
            } else if (movementTitle) {
                movementTitle.remove();
            }

            // 更新创作者信息
            const identification = xmlDoc.querySelector('identification') || this.createIdentificationElement(xmlDoc);

            // 清除现有的creator元素
            const existingCreators = identification.querySelectorAll('creator');
            existingCreators.forEach(creator => creator.remove());

            // 添加原唱信息
            if (titleInfo.originalArtist) {
                const composer = xmlDoc.createElement('creator');
                composer.setAttribute('type', 'composer');
                composer.textContent = titleInfo.originalArtist;
                identification.appendChild(composer);
            }

            // 添加编曲信息
            if (titleInfo.arranger) {
                const arranger = xmlDoc.createElement('creator');
                arranger.setAttribute('type', 'arranger');
                arranger.textContent = titleInfo.arranger;
                identification.appendChild(arranger);
            }

            // 更新版权信息
            let rights = identification.querySelector('rights');
            if (titleInfo.copyright) {
                if (!rights) {
                    rights = xmlDoc.createElement('rights');
                    identification.appendChild(rights);
                }
                // 直接使用用户输入的文本（textarea已经包含真实的换行符）
                const copyrightText = titleInfo.copyright;
                rights.textContent = copyrightText;

                // 同时更新或创建版权信息的credit元素
                this.updateCopyrightCredit(xmlDoc, copyrightText);
            } else if (rights) {
                rights.remove();
                // 移除版权信息的credit元素
                this.removeCopyrightCredit(xmlDoc);
            }

            // 序列化回XML字符串
            const serializer = new XMLSerializer();
            return serializer.serializeToString(xmlDoc);

        } catch (error) {
            console.error('更新XML标题信息失败:', error);
            return xml; // 返回原始XML
        }
    }

    createIdentificationElement(xmlDoc) {
        const identification = xmlDoc.createElement('identification');
        const scorePartwise = xmlDoc.querySelector('score-partwise');
        if (scorePartwise) {
            // 插入到适当位置
            const movementTitle = xmlDoc.querySelector('movement-title');
            if (movementTitle && movementTitle.nextSibling) {
                scorePartwise.insertBefore(identification, movementTitle.nextSibling);
            } else {
                scorePartwise.insertBefore(identification, scorePartwise.firstChild);
            }
        }
        return identification;
    }

    updateCopyrightCredit(xmlDoc, copyrightText) {
        try {
            // 移除现有的版权credit元素
            this.removeCopyrightCredit(xmlDoc);

            if (!copyrightText || copyrightText.trim() === '') {
                return;
            }

            // 获取页面布局信息
            const pageLayout = xmlDoc.querySelector('page-layout');
            let pageWidth = 800; // 默认值
            if (pageLayout) {
                const pageWidthElem = pageLayout.querySelector('page-width');
                if (pageWidthElem) {
                    pageWidth = parseInt(pageWidthElem.textContent) || 800;
                }
            }

            const centerX = Math.floor(pageWidth / 2);

            // 处理换行符，为每行创建一个credit元素
            const lines = copyrightText.split('\n').filter(line => line.trim() !== '');
            const partList = xmlDoc.querySelector('part-list');

            if (partList && lines.length > 0) {
                // 计算版权信息的起始Y坐标（极度靠近页面底部）
                const startY = 5 + (lines.length - 1) * 4; // 从Y坐标5开始，每行间隔4像素

                lines.forEach((line, index) => {
                    const credit = xmlDoc.createElement('credit');
                    credit.setAttribute('page', '1');

                    const creditWords = xmlDoc.createElement('credit-words');
                    creditWords.setAttribute('default-x', centerX.toString());
                    creditWords.setAttribute('default-y', (startY - index * 4).toString()); // 每行间隔4像素
                    creditWords.setAttribute('font-family', 'serif');
                    creditWords.setAttribute('font-size', '1'); // 极小字体：确保明显差异
                    creditWords.setAttribute('halign', 'center');
                    creditWords.setAttribute('data-copyright', 'true'); // 标记为版权信息
                    creditWords.textContent = line.trim();

                    credit.appendChild(creditWords);

                    // 插入到part-list之前
                    partList.parentNode.insertBefore(credit, partList);
                });
            }
        } catch (error) {
            console.error('更新版权credit失败:', error);
        }
    }

    removeCopyrightCredit(xmlDoc) {
        try {
            // 查找并移除所有标记为版权信息的credit元素
            const copyrightCredits = xmlDoc.querySelectorAll('credit-words[data-copyright="true"]');
            copyrightCredits.forEach(creditWords => {
                const credit = creditWords.parentNode;
                if (credit && credit.tagName === 'credit') {
                    credit.parentNode.removeChild(credit);
                }
            });
        } catch (error) {
            console.error('移除版权credit失败:', error);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new XMLEditor();
});
