{% extends "base.html" %}

{% block title %}指法生成结果{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">
                <i class="fas fa-hand-paper me-2"></i>指法生成完成
            </h2>
            
            <!-- 步骤指示器 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="progress-steps">
                        <div class="step completed">
                            <div class="step-number">1</div>
                            <div class="step-label">上传图片</div>
                        </div>
                        <div class="step completed">
                            <div class="step-number">2</div>
                            <div class="step-label">确认识别</div>
                        </div>
                        <div class="step completed">
                            <div class="step-number">3</div>
                            <div class="step-label">生成指法</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 结果展示 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-image me-2"></i>最终结果
                    </h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" id="download-image">
                            <i class="fas fa-download me-2"></i>下载图片
                        </button>
                        <button type="button" class="btn btn-outline-success" id="download-xml">
                            <i class="fas fa-file-code me-2"></i>下载XML
                        </button>
                        <button type="button" class="btn btn-outline-info" id="new-upload">
                            <i class="fas fa-plus me-2"></i>处理新文件
                        </button>
                    </div>
                </div>
                <div class="card-body text-center">
                    <div id="result-loading" class="d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载结果...</p>
                    </div>
                    
                    <div id="result-content" class="d-none">
                        <img id="final-result" src="" class="img-fluid rounded shadow" alt="带指法的乐谱">
                        
                        <!-- 结果信息 -->
                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">处理时间</h6>
                                        <p class="card-text" id="processing-time">-</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">图片尺寸</h6>
                                        <p class="card-text" id="image-size">-</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">文件大小</h6>
                                        <p class="card-text" id="file-size">-</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="result-error" class="alert alert-danger d-none">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <span id="error-message">加载结果失败</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 对比展示 -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-image me-2"></i>原始图片
                    </h6>
                </div>
                <div class="card-body text-center">
                    <img id="original-comparison" src="" class="img-fluid rounded" alt="原始乐谱">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-hand-paper me-2"></i>指法标注
                    </h6>
                </div>
                <div class="card-body text-center">
                    <img id="fingered-comparison" src="" class="img-fluid rounded" alt="带指法乐谱">
                </div>
            </div>
        </div>
    </div>
    
    <!-- 操作区域 -->
    <div class="row mt-4 mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-tools me-2"></i>其他操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>重新处理</h6>
                            <p class="text-muted small">如果结果不满意，您可以：</p>
                            <button type="button" class="btn btn-outline-warning me-2" id="re-edit">
                                <i class="fas fa-edit me-2"></i>重新编辑XML
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="re-upload">
                                <i class="fas fa-upload me-2"></i>重新上传
                            </button>
                        </div>
                        <div class="col-md-6">
                            <h6>分享与保存</h6>
                            <p class="text-muted small">保存您的处理结果：</p>
                            <button type="button" class="btn btn-outline-info me-2" id="copy-link">
                                <i class="fas fa-link me-2"></i>复制链接
                            </button>
                            <button type="button" class="btn btn-outline-success" id="save-to-history">
                                <i class="fas fa-bookmark me-2"></i>保存到历史
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.progress-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 30px;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 50px;
    width: 60px;
    height: 2px;
    background-color: #dee2e6;
    z-index: 1;
}

.step.completed::after {
    background-color: #198754;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
}

.step.completed .step-number {
    background-color: #198754;
    color: white;
}

.step-label {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

.step.completed .step-label {
    color: #495057;
    font-weight: 500;
}
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/fingering_result.js') }}"></script>
{% endblock %}
