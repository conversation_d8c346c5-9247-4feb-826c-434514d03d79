// 上传页面JavaScript
console.log('🔧 upload.js 已加载 - 版本 20250727132800');

// 全局变量声明
let selectedFile = null;
let uploadInProgress = false;
let currentPollInterval = null;
let currentTaskId = null;
let lastProgress = 0;

document.addEventListener('DOMContentLoaded', function() {
    initializeUploadArea();
    initializeFileInput();
    initializeProcessingButton();
});

// 初始化上传区域
function initializeUploadArea() {
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');
    
    // 拖拽事件
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelection(files[0]);
        }
    });
    
    // 点击上传区域（但不包括按钮）
    uploadArea.addEventListener('click', function(e) {
        // 如果点击的是按钮，不触发文件选择
        if (e.target.id === 'select-file-btn' || e.target.closest('#select-file-btn')) {
            return;
        }
        fileInput.click();
    });

    // 单独处理选择文件按钮
    const selectFileBtn = document.getElementById('select-file-btn');
    if (selectFileBtn) {
        selectFileBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            fileInput.click();
        });
    }
}

// 初始化文件输入
function initializeFileInput() {
    const fileInput = document.getElementById('file-input');

    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelection(e.target.files[0]);
        }
        // 清空文件输入，允许重新选择同一文件
        e.target.value = '';
    });
}

// 处理文件选择
function handleFileSelection(file) {
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
        PianoFingeringApp.utils.showNotification('请选择JPG或PNG格式的图片文件', 'error');
        return;
    }
    
    // 验证文件大小 (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
        PianoFingeringApp.utils.showNotification('文件大小不能超过5MB', 'error');
        return;
    }
    
    selectedFile = file;
    displayFilePreview(file);
    enableProcessingButton();
}

// 显示文件预览
function displayFilePreview(file) {
    const filePreview = document.getElementById('file-preview');
    const previewImage = document.getElementById('preview-image');
    const fileInfo = document.getElementById('file-info');
    
    // 显示图片预览
    const reader = new FileReader();
    reader.onload = function(e) {
        previewImage.src = e.target.result;
    };
    reader.readAsDataURL(file);
    
    // 显示文件信息
    fileInfo.innerHTML = `
        <div class="file-info">
            <dl class="row mb-0">
                <dt class="col-sm-4">文件名:</dt>
                <dd class="col-sm-8">${file.name}</dd>
                <dt class="col-sm-4">文件大小:</dt>
                <dd class="col-sm-8">${PianoFingeringApp.utils.formatFileSize(file.size)}</dd>
                <dt class="col-sm-4">文件类型:</dt>
                <dd class="col-sm-8">${file.type}</dd>
                <dt class="col-sm-4">最后修改:</dt>
                <dd class="col-sm-8">${new Date(file.lastModified).toLocaleString()}</dd>
            </dl>
        </div>
    `;
    
    filePreview.classList.remove('d-none');
}

// 启用处理按钮
function enableProcessingButton() {
    const startButton = document.getElementById('start-processing');
    if (selectedFile && !uploadInProgress) {
        startButton.disabled = false;
        startButton.textContent = '开始处理';
        startButton.className = 'btn btn-primary btn-lg';
    }
}

// 初始化处理按钮
function initializeProcessingButton() {
    const startButton = document.getElementById('start-processing');

    // 初始状态：禁用按钮
    startButton.disabled = true;
    startButton.textContent = '请先选择文件';
    startButton.className = 'btn btn-secondary btn-lg';

    startButton.addEventListener('click', function() {
        if (!selectedFile) {
            PianoFingeringApp.utils.showNotification('请先选择文件', 'warning');
            return;
        }

        if (uploadInProgress) {
            PianoFingeringApp.utils.showNotification('正在处理中，请稍候...', 'info');
            return;
        }

        startProcessing();
    });
}

// 开始处理
function startProcessing() {
    console.log('🚀 startProcessing 被调用');
    console.log('📊 当前状态 - selectedFile:', !!selectedFile, 'uploadInProgress:', uploadInProgress);

    if (!selectedFile) {
        PianoFingeringApp.utils.showNotification('请先选择文件', 'error');
        return;
    }

    if (uploadInProgress) {
        console.log('⚠️ 上传已在进行中，忽略重复请求');
        PianoFingeringApp.utils.showNotification('正在处理中，请稍候...', 'warning');
        return;
    }

    uploadInProgress = true;
    console.log('✅ 开始处理，设置 uploadInProgress = true');

    // 显示处理区域
    const processingArea = document.getElementById('processing-area');
    const resultArea = document.getElementById('result-area');
    const startButton = document.getElementById('start-processing');

    processingArea.classList.remove('d-none');
    resultArea.classList.add('d-none');

    // 更新按钮状态为上传中
    startButton.disabled = true;
    startButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>上传中...';
    startButton.className = 'btn btn-secondary btn-lg';
    
    // 准备表单数据
    const formData = new FormData();
    formData.append('file', selectedFile);

    // 获取处理选项
    formData.append('deskew', document.getElementById('deskew').checked);
    formData.append('denoise', document.getElementById('denoise').checked);
    formData.append('enhance_contrast', document.getElementById('enhance_contrast').checked);
    formData.append('remove_watermark', document.getElementById('remove_watermark').checked);
    formData.append('generate_pdf', document.getElementById('generate_pdf').checked);
    formData.append('dpi', document.getElementById('dpi').value);

    // 上传文件并开始处理（使用简化路由）
    console.log('🚀 开始上传文件...');
    console.log('📁 选中文件:', selectedFile.name, selectedFile.size, 'bytes');
    console.log('📋 FormData内容:');
    for (let [key, value] of formData.entries()) {
        if (key === 'file') {
            console.log(`  ${key}: ${value.name} (${value.size} bytes)`);
        } else {
            console.log(`  ${key}: ${value}`);
        }
    }

    // 创建一个带超时的fetch请求
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
        controller.abort();
        console.log('⏰ 请求超时，已取消');
    }, 30000); // 30秒超时

    fetch('/api/upload', {
        method: 'POST',
        body: formData,
        signal: controller.signal
        // 不设置任何headers，让浏览器自动处理
    })
    .then(response => {
        // 清除超时定时器
        clearTimeout(timeoutId);

        console.log('📊 响应状态:', response.status);
        console.log('📊 响应头:', response.headers);

        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }

        return response.json();
    })
    .then(data => {
        console.log('✅ 上传响应:', data);

        if (data.success) {
            currentTaskId = data.task_id;

            // 更新按钮状态为处理中
            const startButton = document.getElementById('start-processing');
            startButton.innerHTML = '<i class="fas fa-cog fa-spin me-2"></i>处理中...';
            startButton.className = 'btn btn-info btn-lg';

            PianoFingeringApp.utils.showNotification('文件上传成功，开始处理...', 'success');

            // 开始轮询任务状态
            pollTaskStatus(data.task_id);
        } else {
            throw new Error(data.error || '上传失败');
        }
    })
    .catch(error => {
        // 清除超时定时器
        clearTimeout(timeoutId);

        console.error('❌ 上传失败:', error);
        console.error('❌ 错误类型:', error.name);
        console.error('❌ 错误消息:', error.message);

        let errorMessage = '上传失败';

        // 根据不同的错误类型提供更具体的错误信息
        if (error.name === 'TypeError') {
            if (error.message.includes('fetch')) {
                errorMessage = '网络请求失败，请检查网络连接';
            } else if (error.message.includes('Failed to fetch')) {
                errorMessage = '无法连接到服务器，请确认服务器正在运行';
            } else {
                errorMessage = '请求格式错误: ' + error.message;
            }
        } else if (error.name === 'AbortError') {
            errorMessage = '请求被取消';
        } else if (error.message.includes('timeout')) {
            errorMessage = '请求超时，请重试';
        } else {
            errorMessage = '上传失败: ' + error.message;
        }

        PianoFingeringApp.utils.showNotification(errorMessage, 'error');

        // 确保重置状态
        uploadInProgress = false;
        resetProcessingState();
    });
}

// 轮询任务状态函数

// 轮询任务状态
function pollTaskStatus(taskId) {
    console.log(`🔄 开始轮询任务状态: ${taskId}`);

    // 如果已经有轮询在运行，先清除它
    if (currentPollInterval) {
        console.log(`🛑 清除之前的轮询: ${currentTaskId}`);
        clearInterval(currentPollInterval);
        currentPollInterval = null;
    }

    currentTaskId = taskId;
    lastProgress = 0; // 重置进度记录
    let pollCount = 0;
    const maxPolls = 150; // 最多轮询5分钟 (150 * 2秒)

    currentPollInterval = setInterval(() => {
        pollCount++;
        console.log(`📡 第${pollCount}次轮询任务状态...`);

        // 防止无限轮询
        if (pollCount > maxPolls) {
            clearInterval(currentPollInterval);
            currentPollInterval = null;
            currentTaskId = null;
            console.error('❌ 轮询超时，停止查询');
            PianoFingeringApp.utils.showNotification('任务处理超时，请刷新页面重试', 'error');
            resetProcessingState();
            return;
        }

        fetch(`/api/status/${taskId}?t=${Date.now()}`, {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        })
            .then(response => {
                console.log(`🌐 HTTP响应状态: ${response.status} ${response.statusText}`);
                console.log(`🌐 响应头:`, Object.fromEntries(response.headers.entries()));

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log(`📊 任务状态响应 (原始):`, data);
                console.log(`📊 状态字段检查: state="${data.state}", typeof=${typeof data.state}`);
                console.log(`📊 是否为FAILURE: ${data.state === 'FAILURE'}`);
                console.log(`📊 是否为SUCCESS: ${data.state === 'SUCCESS'}`);

                updateProgressDisplay(data);

                // 强制检查状态，确保不会遗漏
                const currentState = String(data.state).toUpperCase().trim();
                console.log(`🔍 强制状态检查: "${currentState}"`);

                if (currentState === 'SUCCESS' || data.state === 'SUCCESS') {
                    console.log('✅ 任务处理成功');
                    clearInterval(currentPollInterval);
                    currentPollInterval = null;
                    currentTaskId = null;

                    // 检查是否是简化模式
                    if (taskId.startsWith('simple_')) {
                        console.log('🧪 简化模式完成');

                        // 更新按钮状态为完成
                        const startButton = document.getElementById('start-processing');
                        startButton.innerHTML = '<i class="fas fa-check me-2"></i>测试完成';
                        startButton.className = 'btn btn-success btn-lg';
                        startButton.disabled = false;

                        PianoFingeringApp.utils.showNotification('简化模式：上传测试完成！', 'success');

                        // 2秒后重置状态
                        setTimeout(() => {
                            resetProcessingState();
                        }, 2000);

                        return;
                    }

                    // 检查是否是OCR完成状态
                    if (data.current_step === 'ocr_completed') {
                        console.log('🎵 OCR识别完成，跳转到可视化编辑器');
                        clearInterval(currentPollInterval); // 清除轮询，防止重复触发
                        currentPollInterval = null;
                        currentTaskId = null;
                        PianoFingeringApp.utils.showNotification('OCR识别完成！正在跳转到编辑页面...', 'success');
                        // 跳转到可视化编辑器页面
                        setTimeout(() => {
                            window.location.href = `/visual-editor?task_id=${taskId}`;
                        }, 1500);
                        return;
                    }

                    // 更新按钮状态为完成
                    const startButton = document.getElementById('start-processing');
                    startButton.innerHTML = '<i class="fas fa-check me-2"></i>处理完成';
                    startButton.className = 'btn btn-success btn-lg';
                    startButton.disabled = false;

                    showProcessingResult(data.result);
                    resetProcessingState();
                } else if (currentState === 'FAILURE' || data.state === 'FAILURE') {
                    console.error('❌ 任务处理失败:', data.error);
                    clearInterval(currentPollInterval);
                    currentPollInterval = null;
                    currentTaskId = null;



                    // 更新按钮状态为失败
                    const startButton = document.getElementById('start-processing');
                    startButton.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>处理失败';
                    startButton.className = 'btn btn-danger btn-lg';
                    startButton.disabled = false;

                    // 显示详细的错误信息
                    showErrorDetails(data);

                    // 简洁的通知消息，避免重复
                    PianoFingeringApp.utils.showNotification('处理过程中发生异常，请查看详情', 'error');
                    // 重置处理状态但保留错误详情
                    resetProcessingState(false);
                } else {
                    // 继续轮询
                    console.log(`⏳ 任务进行中: ${data.state} - ${data.status} (${data.progress || 0}%)`);
                }
            })
            .catch(error => {
                console.error('❌ 获取任务状态失败:', error);
                clearInterval(currentPollInterval);
                currentPollInterval = null;
                currentTaskId = null;
                PianoFingeringApp.utils.showNotification('获取处理状态失败: ' + error.message, 'error');
                resetProcessingState();
            });
    }, 2000); // 每2秒轮询一次
}

// 更新进度显示
function updateProgressDisplay(data) {
    const progressBar = document.getElementById('progress-bar');
    const statusText = document.getElementById('status-text');
    const processingHeader = document.querySelector('#processing-area .card-header h5');

    if (progressBar) {
        let progress = data.progress || 0;

        // 防止进度回退（除非是失败状态）
        if (data.state !== 'FAILURE' && progress < lastProgress) {
            console.log(`⚠️ 进度回退检测: ${progress}% < ${lastProgress}%，保持之前的进度`);
            progress = lastProgress;
        } else {
            lastProgress = progress;
        }

        progressBar.style.width = progress + '%';
        progressBar.textContent = progress + '%';

        // 根据状态设置进度条颜色和动画
        if (data.state === 'FAILURE') {
            progressBar.className = 'progress-bar bg-danger';
            progressBar.textContent = '异常';

            // 更新标题为"处理异常"并停止转圈动画
            if (processingHeader) {
                processingHeader.innerHTML = '<i class="fas fa-exclamation-triangle me-2 text-danger"></i>处理异常';
            }
        } else if (data.state === 'SUCCESS') {
            progressBar.className = 'progress-bar bg-success';
            progressBar.textContent = '完成';

            // 更新标题为"处理完成"并停止转圈动画
            if (processingHeader) {
                processingHeader.innerHTML = '<i class="fas fa-check-circle me-2 text-success"></i>处理完成';
            }
        } else {
            progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';

            // 保持"处理进度"标题和转圈动画
            if (processingHeader) {
                processingHeader.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>处理进度';
            }
        }
    }

    if (statusText) {
        let statusMessage = data.status || '处理中...';

        // 如果是失败状态，只显示简洁的状态信息（避免重复）
        if (data.state === 'FAILURE') {
            statusMessage = data.status || '处理过程中发生异常';
            statusText.className = 'text-danger fw-bold';
        } else if (data.state === 'SUCCESS') {
            statusMessage = `✅ ${statusMessage}`;
            statusText.className = 'text-success fw-bold';
        } else {
            statusMessage = `🔄 ${statusMessage}`;
            statusText.className = 'text-primary';
        }

        statusText.textContent = statusMessage;
    }

    // 更新步骤详情
    if (data.steps) {
        updateStepDetails(data.steps);
    }

    // 在控制台输出详细状态信息
    console.log('任务状态更新:', {
        state: data.state,
        status: data.status,
        progress: data.progress,
        error: data.error,
        timestamp: new Date().toLocaleTimeString()
    });
}

// 显示错误详情
function showErrorDetails(data) {
    const errorDetails = document.getElementById('error-details');
    const errorMessage = document.getElementById('error-message');

    if (errorDetails && errorMessage) {
        let errorHtml = `
            <div class="mb-2">
                <strong>状态:</strong> ${data.status || '处理异常'}
            </div>
        `;

        if (data.error) {
            errorHtml += `
                <div class="mb-2">
                    <strong>错误信息:</strong> ${data.error}
                </div>
            `;
        }

        if (data.error_type) {
            errorHtml += `
                <div class="mb-2">
                    <strong>错误类型:</strong> ${data.error_type}
                </div>
            `;
        }

        if (data.updated_at) {
            errorHtml += `
                <div class="mb-2">
                    <strong>发生时间:</strong> ${new Date(data.updated_at).toLocaleString()}
                </div>
            `;
        }

        errorMessage.innerHTML = errorHtml;
        errorDetails.classList.remove('d-none');
    }
}

// 重试处理
function retryProcessing() {
    if (!selectedFile) {
        PianoFingeringApp.utils.showNotification('没有选择的文件，请重新上传', 'error');
        return;
    }

    console.log('🔄 用户点击重试处理');

    // 隐藏错误详情
    const errorDetails = document.getElementById('error-details');
    if (errorDetails) {
        errorDetails.classList.add('d-none');
    }

    // 重新开始处理
    startProcessing();
}

// 重置到上传状态
function resetToUpload() {
    console.log('🔄 用户点击重新上传');

    // 清理所有状态
    selectedFile = null;
    uploadInProgress = false;
    currentTaskId = null;

    // 隐藏处理区域和结果区域
    const processingArea = document.getElementById('processing-area');
    const resultArea = document.getElementById('result-area');
    const errorDetails = document.getElementById('error-details');

    if (processingArea) processingArea.classList.add('d-none');
    if (resultArea) resultArea.classList.add('d-none');
    if (errorDetails) errorDetails.classList.add('d-none');

    // 重置文件预览
    const filePreview = document.getElementById('file-preview');
    const startButton = document.getElementById('start-processing');

    if (filePreview) {
        filePreview.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-image fa-3x mb-3"></i>
                <p>请选择要处理的钢琴谱图片</p>
            </div>
        `;
    }

    if (startButton) {
        startButton.disabled = true;
    }

    // 重置进度条
    const progressBar = document.getElementById('progress-bar');
    const statusText = document.getElementById('status-text');

    if (progressBar) {
        progressBar.style.width = '0%';
        progressBar.textContent = '0%';
        progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
    }

    if (statusText) {
        statusText.textContent = '准备开始处理...';
        statusText.className = 'text-center text-muted';
    }

    PianoFingeringApp.utils.showNotification('已重置，请重新选择文件', 'info');
}



// 显示处理结果
function showProcessingResult(result) {
    const resultArea = document.getElementById('result-area');
    const resultContent = document.getElementById('result-content');
    const processingArea = document.getElementById('processing-area');
    
    processingArea.classList.add('d-none');
    resultArea.classList.remove('d-none');
    
    let html = `
        <div class="row">
            <div class="col-md-8">
                <h6>处理结果预览:</h6>
    `;
    
    // 显示结果图片
    if (result.files && result.files.output_png) {
        // 处理Windows和Unix路径分隔符
        const filename = result.files.output_png.split(/[/\\]/).pop();
        console.log('预览文件名:', filename);
        html += `
            <img src="/api/preview/${filename}" class="result-image mb-3" alt="处理结果">
        `;
    }
    
    html += `
            </div>
            <div class="col-md-4">
                <h6>下载文件:</h6>
                <div class="download-buttons">
    `;
    
    // 添加下载按钮
    if (result.files) {
        if (result.files.output_png) {
            // 处理Windows和Unix路径分隔符
            const pngFilename = result.files.output_png.split(/[/\\]/).pop();
            console.log('PNG下载文件名:', pngFilename);
            html += `
                <a href="/api/download/result/${pngFilename}" class="btn btn-primary btn-sm">
                    <i class="fas fa-download me-1"></i>下载PNG
                </a>
            `;
        }

        if (result.files.output_pdf) {
            // 处理Windows和Unix路径分隔符
            const pdfFilename = result.files.output_pdf.split(/[/\\]/).pop();
            console.log('PDF下载文件名:', pdfFilename);
            html += `
                <a href="/api/download/result/${pdfFilename}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-file-pdf me-1"></i>下载PDF
                </a>
            `;
        }
    }
    
    html += `
                </div>
                
                <div class="mt-3">
                    <h6>处理信息:</h6>
                    <small class="text-muted">
                        处理ID: ${result.process_id}<br>
                        处理时间: ${result.total_time ? PianoFingeringApp.utils.formatTime(result.total_time) : '未知'}
                    </small>
                </div>
            </div>
        </div>
    `;
    
    resultContent.innerHTML = html;
    
    PianoFingeringApp.utils.showNotification('处理完成！', 'success');
}

// 重置处理状态
function resetProcessingState(hideErrorDetails = true) {
    uploadInProgress = false;

    // 清理轮询相关状态
    if (currentPollInterval) {
        clearInterval(currentPollInterval);
        currentPollInterval = null;
    }
    currentTaskId = null;
    lastProgress = 0; // 重置进度记录

    const startButton = document.getElementById('start-processing');

    if (selectedFile) {
        // 有文件时，启用按钮
        startButton.disabled = false;
        startButton.textContent = '开始处理';
        startButton.className = 'btn btn-primary btn-lg';
    } else {
        // 没有文件时，禁用按钮
        startButton.disabled = true;
        startButton.textContent = '请先选择文件';
        startButton.className = 'btn btn-secondary btn-lg';
    }

    // 如果需要隐藏错误详情
    if (hideErrorDetails) {
        const errorDetails = document.getElementById('error-details');
        if (errorDetails) {
            errorDetails.classList.add('d-none');
        }
    }
}
