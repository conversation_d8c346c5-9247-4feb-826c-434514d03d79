"""
Celery异步任务
"""
import os
import logging
from celery import Celery

# 延迟导入以避免循环依赖
def get_processor():
    from app.core.processor import PianoFingeringProcessor
    return PianoFingeringProcessor()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Celery实例
celery = Celery('piano_fingering')

# 从环境变量加载配置
celery.conf.update(
    broker_url=os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/0'),
    result_backend=os.environ.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0'),
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=600,  # 10分钟任务超时
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=50,
    # Windows兼容性配置
    worker_pool='solo',  # 使用solo池避免进程问题
    broker_connection_retry_on_startup=True,  # 修复警告
    worker_concurrency=3,  # 支持3个并发任务
    # 异常处理配置
    task_always_eager=False,  # 确保异步执行
    task_eager_propagates=False,  # 不传播异常
    task_ignore_result=False,  # 保存结果
    result_expires=3600,  # 结果保存1小时
)

@celery.task(bind=True, soft_time_limit=540, time_limit=600)  # 9分钟软超时，10分钟硬超时
def process_image_task(self, image_path, options=None):
    """
    异步处理图像任务

    Args:
        image_path (str): 图像文件路径
        options (dict): 处理选项

    Returns:
        dict: 处理结果
    """
    try:
        from app.core.task_manager import task_manager

        task_id = self.request.id

        # 验证文件路径
        abs_image_path = os.path.abspath(image_path)
        logger.info(f"开始处理图像: {image_path}")
        logger.info(f"绝对路径: {abs_image_path}")
        logger.info(f"任务ID: {task_id}")

        # 检查文件是否存在
        if not os.path.exists(abs_image_path):
            error_msg = f"输入文件不存在: {abs_image_path}"
            logger.error(error_msg)

            # 更新任务状态为失败
            task_manager.update_task_status(
                task_id, 'FAILURE', '处理失败', 0,
                error=f"处理失败: 图像预处理失败: 输入图像不存在: {image_path}",
                error_type='FileNotFoundError'
            )

            # 返回失败结果而不是抛出异常
            return {
                'success': False,
                'error': error_msg,
                'task_id': task_id
            }

        # 记录文件信息
        file_size = os.path.getsize(abs_image_path)
        logger.info(f"文件大小: {file_size} 字节")

        # 使用绝对路径进行处理
        image_path = abs_image_path

        # 更新任务状态
        task_manager.update_task_status(
            task_id, 'PROGRESS', '初始化处理器...', 5,
            current_step='initialization'
        )

        self.update_state(
            state='PROGRESS',
            meta={
                'status': '初始化处理器...',
                'progress': 5,
                'current_step': 'initialization'
            }
        )
        
        # 创建处理器实例
        processor = get_processor()
        
        # 定义进度回调函数
        def progress_callback(step, progress, status):
            # 更新简单任务管理器
            task_manager.update_task_status(
                task_id, 'PROGRESS', status, progress,
                current_step=step
            )

            # 同时更新Celery状态
            self.update_state(
                state='PROGRESS',
                meta={
                    'status': status,
                    'progress': progress,
                    'current_step': step
                }
            )

            # 记录日志
            logger.info(f"[{task_id}] {step}: {status} ({progress}%)")
        
        # 执行处理，传入进度回调（使用完整处理流程，包含文本集成）
        result = processor.process_image(image_path, options, progress_callback=progress_callback)
        
        if result['success']:
            logger.info(f"图像处理成功: {result['process_id']}")

            # 更新最终状态（保持处理器设置的步骤状态）
            task_manager.update_task_status(
                task_id, 'SUCCESS', '处理完成', 100,
                current_step='ocr_completed',  # 设置为ocr_completed以触发跳转
                result=result
            )

            # Celery状态不重复设置ocr_completed，避免重复弹出提示
            self.update_state(
                state='SUCCESS',
                meta={
                    'status': '处理完成',
                    'progress': 100,
                    'current_step': 'completed'  # 使用不同的状态，避免重复触发
                }
            )

            return {
                'success': True,
                'process_id': result['process_id'],
                'files': result['files'],
                'total_time': result.get('total_time', 0),
                'steps': result['steps']
            }
        else:
            logger.error(f"图像处理失败: {result.get('errors', [])}")
            error_msg = '; '.join(result.get('errors', ['未知错误']))

            # 更新失败状态
            task_manager.update_task_status(
                task_id, 'FAILURE', '处理失败', 0,
                error=error_msg
            )

            # 返回失败结果而不是抛出异常
            return {
                'success': False,
                'error': error_msg,
                'task_id': task_id
            }

    except Exception as e:
        from app.core.task_manager import task_manager
        import traceback
        from celery.exceptions import SoftTimeLimitExceeded, TimeLimitExceeded

        task_id = self.request.id

        # 处理超时异常
        if isinstance(e, (SoftTimeLimitExceeded, TimeLimitExceeded)):
            logger.error(f"任务超时: {task_id}")
            error_msg = "任务处理超时，请尝试使用更小的图片或简化处理选项"
            error_type = "TaskTimeout"
        else:
            logger.error(f"任务执行异常: {str(e)}")
            logger.error(f"异常详情: {traceback.format_exc()}")
            error_msg = str(e)
            error_type = type(e).__name__

        # 更新失败状态
        task_manager.update_task_status(
            task_id, 'FAILURE', '处理失败', 0,
            error=error_msg,
            error_type=error_type
        )

        # 使用更安全的方式更新Celery状态
        try:
            self.update_state(
                state='FAILURE',
                meta={
                    'status': '处理失败',
                    'error': error_msg,
                    'error_type': error_type,
                    'progress': 0
                }
            )
        except Exception as update_error:
            logger.error(f"更新Celery状态失败: {str(update_error)}")

        # 返回错误信息而不是抛出异常
        return {
            'success': False,
            'error': error_msg,
            'error_type': error_type,
            'status': '处理失败'
        }

@celery.task
def cleanup_old_files():
    """清理旧文件的定时任务"""
    try:
        import time
        from pathlib import Path
        from app.core.task_manager import task_manager

        # 清理任务状态
        cleaned_tasks = task_manager.cleanup_old_tasks(24)  # 清理24小时前的任务
        logger.info(f"清理了 {cleaned_tasks} 个过期任务状态")

        # 清理超过24小时的临时文件
        temp_folder = os.environ.get('TEMP_FOLDER', 'temp')
        if os.path.exists(temp_folder):
            current_time = time.time()
            for file_path in Path(temp_folder).glob('*'):
                if file_path.is_file() and file_path.name != 'task_status.json':
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > 86400:  # 24小时
                        try:
                            file_path.unlink()
                            logger.info(f"删除过期临时文件: {file_path}")
                        except Exception as e:
                            logger.warning(f"删除文件失败 {file_path}: {str(e)}")

        # 清理超过7天的输出文件
        output_folder = os.environ.get('OUTPUT_FOLDER', 'output')
        if os.path.exists(output_folder):
            current_time = time.time()
            for file_path in Path(output_folder).glob('*'):
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > 604800:  # 7天
                        try:
                            file_path.unlink()
                            logger.info(f"删除过期输出文件: {file_path}")
                        except Exception as e:
                            logger.warning(f"删除文件失败 {file_path}: {str(e)}")

        return {'success': True, 'message': f'文件清理完成，清理了{cleaned_tasks}个任务状态'}

    except Exception as e:
        logger.error(f"文件清理失败: {str(e)}")
        return {'success': False, 'error': str(e)}

# 简化的清理任务
@celery.task
def cleanup_old_files():
    """清理旧文件"""
    try:
        import os
        import time
        from pathlib import Path

        # 清理超过24小时的临时文件
        temp_folder = Path('temp')
        output_folder = Path('output')

        current_time = time.time()
        cleaned_count = 0

        for folder in [temp_folder, output_folder]:
            if folder.exists():
                for file_path in folder.iterdir():
                    if file_path.is_file():
                        file_age = current_time - file_path.stat().st_mtime
                        if file_age > 86400:  # 24小时
                            try:
                                file_path.unlink()
                                cleaned_count += 1
                            except Exception as e:
                                logger.warning(f"删除文件失败 {file_path}: {e}")

        return {'success': True, 'message': f'清理了 {cleaned_count} 个旧文件'}
    except Exception as e:
        logger.error(f"文件清理失败: {str(e)}")
        return {'success': False, 'error': str(e)}

# 简化的定时任务配置
celery.conf.beat_schedule = {
    'cleanup-old-files': {
        'task': 'app.tasks.cleanup_old_files',
        'schedule': 3600.0,  # 每小时执行一次
    },
}

if __name__ == '__main__':
    celery.start()
