{% extends "base.html" %}

{% block title %}上传乐谱 - 钢琴指法自动标注系统{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <h2 class="text-center mb-4">
                <i class="fas fa-upload me-2"></i>上传钢琴乐谱
            </h2>

            <!-- 友好提示 -->
            <div class="alert alert-info shadow-sm mb-4">
                <h5><i class="fas fa-lightbulb me-2"></i>上传建议</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>📸 图片质量要求：</h6>
                        <ul class="mb-0">
                            <li><strong>分辨率：</strong>至少300DPI，推荐600DPI以上</li>
                            <li><strong>清晰度：</strong>避免模糊，确保五线谱线条清晰</li>
                            <li><strong>角度：</strong>避免倾斜，保持乐谱水平</li>
                            <li><strong>光线：</strong>均匀照明，避免阴影和反光</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>🎼 乐谱内容建议：</h6>
                        <ul class="mb-0">
                            <li><strong>干净整洁：</strong>尽量去除水印和噪声</li>
                            <li><strong>完整可见：</strong>确保所有音符和符号清晰可见</li>
                            <li><strong>单页处理：</strong>每次上传一页乐谱</li>
                            <li><strong>标准格式：</strong>支持常见的五线谱格式</li>
                        </ul>
                    </div>
                </div>
                <hr>
                <p class="mb-0 text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>温馨提示：</strong>虽然系统提供了图像优化功能，但高质量的原始图片能显著提升识别准确度。
                </p>
            </div>



            <!-- 上传区域 -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div id="upload-area" class="upload-area text-center p-5 border-2 border-dashed rounded">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted mb-3">拖拽文件到此处或点击选择</h5>
                        <p class="text-muted mb-3">支持 JPG、PNG 格式，文件大小不超过 5MB</p>
                        <input type="file" id="file-input" class="d-none" accept=".jpg,.jpeg,.png">
                        <button type="button" class="btn btn-primary" id="select-file-btn">
                            <i class="fas fa-folder-open me-2"></i>选择文件
                        </button>
                    </div>
                    
                    <!-- 文件预览 -->
                    <div id="file-preview" class="mt-4 d-none">
                        <h6>文件预览：</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <img id="preview-image" class="img-fluid rounded" alt="预览图片">
                            </div>
                            <div class="col-md-6">
                                <div id="file-info" class="mt-3 mt-md-0">
                                    <!-- 文件信息将在这里显示 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 处理选项 -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>处理选项
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 处理选项说明 -->
                    <div class="alert alert-light mb-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>使用建议：</strong>根据您的图片质量选择合适的预处理选项，可以提升识别准确度。
                        </small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6>图像预处理</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="deskew" checked>
                                <label class="form-check-label" for="deskew">
                                    倾斜校正
                                </label>
                                <small class="form-text text-muted d-block">
                                    ✅ 推荐勾选：适用于手机拍摄或扫描时有轻微倾斜的图片
                                </small>
                            </div>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" id="denoise" checked>
                                <label class="form-check-label" for="denoise">
                                    噪点移除
                                </label>
                                <small class="form-text text-muted d-block">
                                    ✅ 推荐勾选：去除图片中的杂点和噪声，提升清晰度
                                </small>
                            </div>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" id="enhance_contrast" checked>
                                <label class="form-check-label" for="enhance_contrast">
                                    对比度增强
                                </label>
                                <small class="form-text text-muted d-block">
                                    ✅ 推荐勾选：增强黑白对比度，让音符更清晰
                                </small>
                            </div>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" id="remove_watermark">
                                <label class="form-check-label" for="remove_watermark">
                                    水印淡化
                                </label>
                                <small class="form-text text-muted d-block">
                                    ⚠️ 按需勾选：仅当图片有明显浅色水印时使用，可能影响音符识别
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>输出选项</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="generate_pdf" checked>
                                <label class="form-check-label" for="generate_pdf">
                                    生成PDF文件
                                </label>
                            </div>
                            <div class="mb-3">
                                <label for="dpi" class="form-label">输出分辨率 (DPI)</label>
                                <select class="form-select" id="dpi">
                                    <option value="150">150 DPI (快速)</option>
                                    <option value="300" selected>300 DPI (标准)</option>
                                    <option value="600">600 DPI (高质量)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 开始处理按钮 -->
            <div class="text-center mb-4">
                <button id="start-processing" class="btn btn-success btn-lg" disabled>
                    <i class="fas fa-play me-2"></i>开始处理
                </button>
            </div>
            
            <!-- 处理进度 -->
            <div id="processing-area" class="card shadow-sm d-none">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-spinner fa-spin me-2"></i>处理进度
                    </h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <div id="status-text" class="text-center text-muted">
                        准备开始处理...
                    </div>



                    <!-- 错误详情显示 -->
                    <div id="error-details" class="alert alert-danger mt-3 d-none">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>错误详情</h6>
                        <div id="error-message"></div>
                        <div class="mt-3">
                            <button id="retry-button" class="btn btn-warning btn-sm me-2" onclick="retryProcessing()">
                                <i class="fas fa-redo me-1"></i>重新处理
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="resetToUpload()">
                                <i class="fas fa-upload me-1"></i>重新上传
                            </button>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                请检查控制台获取更多技术详情
                            </small>
                        </div>
                    </div>

                    <div id="step-details" class="mt-3">
                        <!-- 详细步骤信息 -->
                    </div>
                </div>
            </div>
            
            <!-- 处理结果 -->
            <div id="result-area" class="card shadow-sm d-none">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-check-circle text-success me-2"></i>处理完成
                    </h5>
                </div>
                <div class="card-body">
                    <div id="result-content">
                        <!-- 结果内容将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/upload.js') }}?v=20250727132800"></script>
{% endblock %}
