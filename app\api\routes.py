"""
API路由 - 数据接口
"""
import os
import uuid
from datetime import datetime
from flask import request, jsonify, current_app, send_file
from werkzeug.utils import secure_filename
from app.api import bp
from app.core.processor import PianoFingeringProcessor
from app.tasks import process_image_task

def allowed_file(filename):
    """检查文件类型是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

@bp.route('/test', methods=['GET'])
def test_route():
    """测试路由"""
    return jsonify({'success': True, 'message': 'Flask应用正常运行'})

@bp.route('/upload_simple', methods=['POST'])
def upload_simple():
    """简化的上传处理"""
    try:
        current_app.logger.info("收到简化上传请求")
        current_app.logger.info(f"请求方法: {request.method}")
        current_app.logger.info(f"请求头: {dict(request.headers)}")
        current_app.logger.info(f"请求文件: {list(request.files.keys())}")
        current_app.logger.info(f"请求表单: {dict(request.form)}")

        # 检查是否有文件
        if 'file' in request.files:
            file = request.files['file']
            current_app.logger.info(f"文件名: {file.filename}")
            current_app.logger.info(f"文件类型: {file.content_type}")
            # 读取文件大小但不保存
            file_content = file.read()
            file_size = len(file_content)
            current_app.logger.info(f"文件大小: {file_size} 字节")
            file.seek(0)  # 重置文件指针

        # 立即返回成功，不进行实际处理
        task_id = 'simple_' + str(uuid.uuid4())[:8]

        response_data = {
            'success': True,
            'task_id': task_id,
            'filename': 'test_file.jpg',
            'message': '文件上传成功（简化模式）'
        }

        current_app.logger.info(f"返回响应: {response_data}")

        return jsonify(response_data)

    except Exception as e:
        current_app.logger.error(f"简化上传失败: {str(e)}")
        current_app.logger.error(f"异常详情: {str(e.__class__.__name__)}: {str(e)}")
        import traceback
        current_app.logger.error(f"堆栈跟踪: {traceback.format_exc()}")

        return jsonify({'success': False, 'error': f'上传失败: {str(e)}'}), 500



@bp.route('/upload', methods=['POST'])
def upload_file():
    """文件上传接口"""
    try:
        current_app.logger.info("收到文件上传请求")
        current_app.logger.info(f"请求文件: {list(request.files.keys())}")
        current_app.logger.info(f"请求表单: {dict(request.form)}")

        if 'file' not in request.files:
            current_app.logger.error("请求中没有文件字段")
            return jsonify({'success': False, 'error': '没有选择文件'}), 400
        
        file = request.files['file']
        current_app.logger.info(f"文件名: {file.filename}")
        current_app.logger.info(f"文件大小: {len(file.read())} 字节")
        file.seek(0)  # 重置文件指针

        if file.filename == '':
            current_app.logger.error("文件名为空")
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        if not allowed_file(file.filename):
            current_app.logger.error(f"不支持的文件格式: {file.filename}")
            return jsonify({'success': False, 'error': '不支持的文件格式'}), 400
        
        # 生成安全的文件名
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        safe_filename = f"{timestamp}_{unique_id}_{filename}"
        
        # 保存文件
        upload_path = os.path.join(current_app.config['UPLOAD_FOLDER'], safe_filename)

        # 确保上传目录存在
        os.makedirs(os.path.dirname(upload_path), exist_ok=True)

        # 保存文件并验证
        file.save(upload_path)

        # 验证文件是否成功保存
        if not os.path.exists(upload_path):
            return jsonify({'success': False, 'error': '文件保存失败'}), 500

        # 记录文件信息
        file_size = os.path.getsize(upload_path)
        current_app.logger.info(f"文件上传成功: {safe_filename}, 大小: {file_size} 字节")

        # 获取处理选项
        preprocessing_options = {
            'deskew': request.form.get('deskew', 'false').lower() == 'true',
            'denoise': request.form.get('denoise', 'false').lower() == 'true',
            'enhance_contrast': request.form.get('enhance_contrast', 'false').lower() == 'true',
            'remove_watermark': request.form.get('remove_watermark', 'false').lower() == 'true',
        }

        output_options = {
            'generate_pdf': request.form.get('generate_pdf', 'false').lower() == 'true',
            'dpi': int(request.form.get('dpi', 300))
        }

        # 构建完整的选项结构
        options = {
            'preprocessing': preprocessing_options,
            'output': output_options
        }

        # 记录处理选项以便调试
        current_app.logger.info(f"预处理选项: {preprocessing_options}")
        current_app.logger.info(f"输出选项: {output_options}")

        # 尝试使用Celery，失败时回退到伪异步处理
        use_celery = True  # 重新启用Celery
        current_app.logger.info("尝试使用Celery异步处理模式")

        if use_celery:
            # 使用绝对路径启动异步处理任务
            abs_upload_path = os.path.abspath(upload_path)
            current_app.logger.info(f"启动异步任务，文件路径: {abs_upload_path}")

            try:
                # 直接尝试提交Celery任务
                task = process_image_task.delay(abs_upload_path, options)
                current_app.logger.info(f"Celery任务提交成功: {task.id}")

                return jsonify({
                    'success': True,
                    'task_id': task.id,
                    'filename': safe_filename,
                    'message': '文件上传成功，开始处理...'
                })

            except Exception as e:
                current_app.logger.error(f"Celery任务提交失败: {e}")
                current_app.logger.info("切换到伪异步模式")
                use_celery = False
        else:
            # 伪异步处理模式 - 立即返回任务ID，然后在后台线程中处理
            import threading
            from app.core.processor import PianoFingeringProcessor
            from app.core.task_manager import task_manager

            abs_upload_path = os.path.abspath(upload_path)
            sync_task_id = 'sync_' + unique_id

            current_app.logger.info(f"启动伪异步处理，文件路径: {abs_upload_path}")
            current_app.logger.info(f"任务ID: {sync_task_id}")

            # 初始化任务状态
            task_manager.update_task_status(
                sync_task_id, 'PROGRESS', '文件上传完成，准备开始处理...', 5,
                current_step='uploaded'
            )

            # 在主线程中获取所有必要的配置
            config_data = {
                'AUDIVERIS_PATH': current_app.config.get('AUDIVERIS_PATH'),
                'MUSESCORE_PATH': current_app.config.get('MUSESCORE_PATH'),
                'OUTPUT_FOLDER': current_app.config.get('OUTPUT_FOLDER'),
                'TEMP_FOLDER': current_app.config.get('TEMP_FOLDER', 'temp'),
                'UPLOAD_FOLDER': current_app.config.get('UPLOAD_FOLDER')
            }

            current_app.logger.info(f"配置数据: {config_data}")

            # 定义后台处理函数
            def background_process():
                try:
                    import logging
                    logger = logging.getLogger('piano_fingering')

                    # 使用配置数据创建处理器
                    processor = PianoFingeringProcessor(config=config_data)

                    # 定义进度回调函数
                    def progress_callback(step, progress, status):
                        logger.info(f"后台处理进度: {step} - {status} ({progress}%)")
                        task_manager.update_task_status(
                            sync_task_id, 'PROGRESS', status, progress,
                            current_step=step
                        )

                    # 开始处理
                    logger.info(f"开始后台处理任务: {sync_task_id}")
                    task_manager.update_task_status(
                        sync_task_id, 'PROGRESS', '开始处理...', 10,
                        current_step='processing'
                    )

                    result = processor.process_image_ocr_only(abs_upload_path, options, progress_callback)

                    logger.info(f"后台处理结果: success={result.get('success')}, errors={result.get('errors')}")

                    if result.get('success', False):
                        # 更新任务状态为OCR完成（这是同步处理，不会触发前端跳转）
                        task_manager.update_task_status(
                            sync_task_id, 'SUCCESS', 'OCR识别完成，请确认结果', 100,
                            current_step='sync_ocr_completed',  # 使用不同的状态，避免与异步处理冲突
                            result=result
                        )
                        logger.info(f"任务 {sync_task_id} 处理成功")
                    else:
                        error_msg = '; '.join(result.get('errors', ['处理失败']))
                        logger.error(f"后台处理失败: {error_msg}")

                        # 更新任务状态为失败
                        task_manager.update_task_status(
                            sync_task_id, 'FAILURE', '处理失败', 0,
                            current_step='failed',
                            error=error_msg,
                            error_type='ProcessingError'
                        )

                except Exception as e:
                    logger.error(f"后台处理异常: {str(e)}")
                    import traceback
                    logger.error(f"异常详情: {traceback.format_exc()}")

                    task_manager.update_task_status(
                        sync_task_id, 'FAILURE', '处理异常', 0,
                        current_step='error',
                        error=str(e),
                        error_type='Exception'
                    )

            # 启动后台线程
            thread = threading.Thread(target=background_process)
            thread.daemon = True
            thread.start()

            # 立即返回任务ID，让前端开始轮询
            return jsonify({
                'success': True,
                'task_id': sync_task_id,
                'filename': safe_filename,
                'message': '文件上传成功，开始处理...'
            })
        
    except Exception as e:
        current_app.logger.error(f"文件上传失败: {str(e)}")
        return jsonify({'success': False, 'error': f'上传失败: {str(e)}'}), 500

@bp.route('/status/<task_id>')
def get_task_status(task_id):
    """获取任务状态"""
    try:
        current_app.logger.info(f"查询任务状态: {task_id}")

        # 如果是简化模式的任务ID
        if task_id.startswith('simple_'):
            current_app.logger.info("简化模式任务，返回成功状态")
            # 模拟一个成功的处理结果
            return jsonify({
                'state': 'SUCCESS',
                'status': '处理完成',
                'progress': 100,
                'result': {
                    'success': True,
                    'message': '简化模式：上传测试完成',
                    'processed_image': None,
                    'original_image': None,
                    'pdf_file': None
                }
            })

        # 同步任务也使用任务管理器查询状态
        # 不再特殊处理sync_前缀的任务

        # 优先使用简单任务管理器
        try:
            from app.core.task_manager import task_manager
            response = task_manager.get_task_status(task_id)

            # 如果任务管理器中有结果，直接返回
            if response.get('state') != 'PENDING' or 'result' in response:
                return jsonify(response)

        except Exception as tm_error:
            current_app.logger.warning(f"任务管理器查询失败: {str(tm_error)}")

        # 如果简单管理器没有结果，尝试Celery（但更安全）
        try:
            from app.tasks import process_image_task
            task = process_image_task.AsyncResult(task_id)

            # 只获取基本状态，避免复杂的info查询
            state = str(task.state) if hasattr(task, 'state') else 'PENDING'

            if state == 'PENDING':
                response = {
                    'state': state,
                    'status': '等待处理...',
                    'progress': 0
                }
            elif state == 'SUCCESS':
                response = {
                    'state': state,
                    'status': '处理完成',
                    'progress': 100,
                    'result': {'success': True, 'message': '处理完成'}
                }
            elif state == 'FAILURE':
                response = {
                    'state': state,
                    'status': '处理失败',
                    'progress': 0,
                    'error': '任务执行失败'
                }
            else:
                response = {
                    'state': state,
                    'status': '处理中...',
                    'progress': 50
                }

        except Exception as celery_error:
            # Celery查询失败，返回默认状态
            current_app.logger.warning(f"Celery查询失败: {str(celery_error)}")
            response = {
                'state': 'PENDING',
                'status': '查询中...',
                'progress': 0,
                'message': '正在获取任务状态...'
            }

        return jsonify(response)

    except Exception as e:
        current_app.logger.error(f"获取任务状态失败: {str(e)}")
        return jsonify({
            'state': 'FAILURE',
            'status': '状态查询失败',
            'progress': 0,
            'error': f'查询异常: {str(e)}'
        }), 500

@bp.route('/download/<file_type>/<filename>')
def download_file(file_type, filename):
    """文件下载接口"""
    try:
        # 使用绝对路径解决路径问题 - 需要回到项目根目录
        base_dir = os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

        if file_type == 'result':
            folder_path = os.path.join(base_dir, 'output')
        elif file_type == 'temp':
            folder_path = os.path.join(base_dir, 'temp')
        else:
            return jsonify({'error': '无效的文件类型'}), 400

        file_path = os.path.join(folder_path, filename)
        current_app.logger.info(f"下载文件请求: {file_type}/{filename}")
        current_app.logger.info(f"文件路径: {file_path}")
        
        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404
        
        return send_file(file_path, as_attachment=True)
        
    except Exception as e:
        current_app.logger.error(f"文件下载失败: {str(e)}")
        return jsonify({'error': f'下载失败: {str(e)}'}), 500

@bp.route('/preview/<filename>')
def preview_file(filename):
    """文件预览接口"""
    try:
        # 使用绝对路径解决路径问题 - 需要回到项目根目录
        base_dir = os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        output_folder = os.path.join(base_dir, 'output')
        file_path = os.path.join(output_folder, filename)

        current_app.logger.info(f"预览文件请求: {filename}")
        current_app.logger.info(f"基础目录: {base_dir}")
        current_app.logger.info(f"输出目录: {output_folder}")
        current_app.logger.info(f"完整文件路径: {file_path}")
        current_app.logger.info(f"文件是否存在: {os.path.exists(file_path)}")

        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404
        
        return send_file(file_path)
        
    except Exception as e:
        current_app.logger.error(f"文件预览失败: {str(e)}")
        return jsonify({'error': f'预览失败: {str(e)}'}), 500

@bp.route('/engine-status')
def engine_status():
    """获取引擎状态"""
    try:
        processor = PianoFingeringProcessor()
        status = processor.get_engine_status()
        return jsonify(status)
        
    except Exception as e:
        current_app.logger.error(f"获取引擎状态失败: {str(e)}")
        return jsonify({'error': f'获取状态失败: {str(e)}'}), 500

@bp.route('/history')
def get_history():
    """获取处理历史"""
    try:
        # 这里应该从数据库或文件系统获取历史记录
        # 暂时返回示例数据
        history_data = []
        
        output_folder = current_app.config['OUTPUT_FOLDER']
        if os.path.exists(output_folder):
            for filename in os.listdir(output_folder):
                if filename.endswith('.png') or filename.endswith('.pdf'):
                    file_path = os.path.join(output_folder, filename)
                    stat = os.stat(file_path)
                    
                    history_data.append({
                        'filename': filename,
                        'size': stat.st_size,
                        'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })
        
        # 按修改时间排序
        history_data.sort(key=lambda x: x['modified_time'], reverse=True)
        
        return jsonify({
            'success': True,
            'data': history_data
        })
        
    except Exception as e:
        current_app.logger.error(f"获取历史记录失败: {str(e)}")
        return jsonify({'error': f'获取历史失败: {str(e)}'}), 500

@bp.route('/delete/<filename>', methods=['DELETE'])
def delete_file(filename):
    """删除文件"""
    try:
        file_path = os.path.join(current_app.config['OUTPUT_FOLDER'], filename)
        
        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404
        
        os.remove(file_path)
        
        return jsonify({
            'success': True,
            'message': '文件删除成功'
        })
        
    except Exception as e:
        current_app.logger.error(f"删除文件失败: {str(e)}")
        return jsonify({'error': f'删除失败: {str(e)}'}), 500

@bp.route('/debug-info')
def get_debug_info():
    """获取调试信息"""
    try:
        import time
        debug_info = {
            'timestamp': time.time(),
            'directories': {},
            'task_manager': {},
            'celery_status': {}
        }

        # 检查目录
        for dir_name in ['uploads', 'output', 'temp', 'logs']:
            if os.path.exists(dir_name):
                files = os.listdir(dir_name)
                debug_info['directories'][dir_name] = {
                    'exists': True,
                    'file_count': len(files),
                    'files': files[:5]  # 只显示前5个文件
                }
            else:
                debug_info['directories'][dir_name] = {'exists': False}

        # 检查任务管理器
        try:
            from app.core.task_manager import task_manager
            all_tasks = task_manager.load_status()
            debug_info['task_manager'] = {
                'available': True,
                'task_count': len(all_tasks),
                'recent_tasks': list(all_tasks.keys())[-5:] if all_tasks else []
            }
        except Exception as e:
            debug_info['task_manager'] = {
                'available': False,
                'error': str(e)
            }

        # 检查Celery状态
        try:
            from app.tasks import celery
            inspect = celery.control.inspect()
            stats = inspect.stats()
            debug_info['celery_status'] = {
                'available': True,
                'workers': list(stats.keys()) if stats else [],
                'worker_count': len(stats) if stats else 0
            }
        except Exception as e:
            debug_info['celery_status'] = {
                'available': False,
                'error': str(e)
            }

        return jsonify(debug_info)

    except Exception as e:
        current_app.logger.error(f"获取调试信息失败: {str(e)}")
        return jsonify({'error': f'获取调试信息失败: {str(e)}'}), 500

@bp.route('/file/<task_id>/<file_type>')
def get_file(task_id, file_type):
    """获取任务相关文件"""
    try:
        current_app.logger.info(f"获取文件请求: task_id={task_id}, file_type={file_type}")

        from app.core.task_manager import task_manager
        task_status = task_manager.get_task_status(task_id)

        if task_status.get('state') != 'SUCCESS':
            current_app.logger.error(f"任务未完成: {task_status.get('state')}")
            return jsonify({'error': '任务未完成'}), 404

        files = task_status.get('result', {}).get('files', {})
        current_app.logger.info(f"任务文件列表: {list(files.keys())}")

        if file_type not in files:
            current_app.logger.error(f"文件类型不存在: {file_type}, 可用类型: {list(files.keys())}")
            return jsonify({'error': '文件不存在'}), 404

        file_path = files[file_type]
        current_app.logger.info(f"原始文件路径: {file_path}")

        # 处理相对路径，确保从项目根目录开始
        if not os.path.isabs(file_path):
            # 如果是相对路径，从项目根目录开始
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            file_path = os.path.join(project_root, file_path)

        # 特殊处理预览文件：如果文件不存在，尝试查找带页码后缀的文件
        if file_type == 'preview' and not os.path.exists(file_path):
            base_name = os.path.splitext(file_path)[0]
            extension = os.path.splitext(file_path)[1]

            # 尝试查找 -1, -2 等后缀的文件
            for i in range(1, 10):
                variant_path = f"{base_name}-{i}{extension}"
                if os.path.exists(variant_path):
                    file_path = variant_path
                    current_app.logger.info(f"找到预览文件变体: {variant_path}")
                    break

        if not os.path.exists(file_path):
            current_app.logger.error(f"文件不存在: {file_path}")
            return jsonify({'error': f'文件不存在: {file_path}'}), 404

        # 根据文件类型返回适当的响应
        if file_type.endswith('_xml') or file_type == 'xml':
            return send_file(file_path, mimetype='application/xml')
        elif file_type.endswith('_image') or file_type.endswith('_png'):
            return send_file(file_path, mimetype='image/png')
        else:
            return send_file(file_path)

    except Exception as e:
        current_app.logger.error(f"获取文件失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/generate-preview', methods=['POST'])
def generate_preview():
    """生成XML预览图片"""
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        xml_content = data.get('xml_content')

        if not task_id or not xml_content:
            return jsonify({'error': '缺少必要参数'}), 400

        # 保存临时XML文件
        temp_xml_path = os.path.join(current_app.config['TEMP_FOLDER'], f"{task_id}_temp.xml")
        current_app.logger.info(f"保存临时XML文件: {temp_xml_path}")

        with open(temp_xml_path, 'w', encoding='utf-8') as f:
            f.write(xml_content)

        # 验证XML文件是否正确保存
        if not os.path.exists(temp_xml_path):
            return jsonify({'error': '临时XML文件保存失败'}), 500

        current_app.logger.info(f"临时XML文件大小: {os.path.getsize(temp_xml_path)} 字节")

        # 使用MuseScore生成预览
        from app.core.musescore_engine import MuseScoreEngine
        musescore = MuseScoreEngine()

        current_app.logger.info(f"开始生成预览: {temp_xml_path}")
        preview_result = musescore.render_to_png(
            temp_xml_path,
            output_name=f"{task_id}_preview",
            dpi=150  # 更低DPI用于快速预览
        )
        current_app.logger.info(f"预览生成结果: {preview_result.get('success', False)}")

        if preview_result['success']:
            # 返回预览图片URL - 直接使用文件名
            preview_filename = os.path.basename(preview_result['output_path'])
            preview_url = f"/api/preview/{preview_filename}"

            # 更新任务状态，添加预览文件
            from app.core.task_manager import task_manager
            task_status = task_manager.get_task_status(task_id)
            if 'files' not in task_status.get('result', {}):
                task_status['result']['files'] = {}
            task_status['result']['files']['preview'] = preview_result['output_path']
            task_manager.save_status({task_id: task_status})

            return jsonify({
                'success': True,
                'preview_url': preview_url
            })
        else:
            return jsonify({
                'success': False,
                'error': preview_result['error']
            }), 500

    except Exception as e:
        current_app.logger.error(f"生成预览失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/validate-xml', methods=['POST'])
def validate_xml():
    """验证XML格式"""
    try:
        data = request.get_json()
        xml_content = data.get('xml_content')

        if not xml_content:
            return jsonify({'valid': False, 'error': 'XML内容为空'})

        # 简单的XML格式验证
        import xml.etree.ElementTree as ET
        try:
            ET.fromstring(xml_content)
            return jsonify({'valid': True})
        except ET.ParseError as e:
            return jsonify({'valid': False, 'error': str(e)})

    except Exception as e:
        return jsonify({'valid': False, 'error': str(e)})

@bp.route('/save-visual-changes', methods=['POST'])
def save_visual_changes():
    """保存可视化编辑器的修改到原始XML文件"""
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        xml_content = data.get('xml_content')

        if not task_id or not xml_content:
            return jsonify({'error': '缺少必要参数'}), 400

        # 获取任务状态
        from app.core.task_manager import task_manager
        task_status = task_manager.get_task_status(task_id)

        if task_status.get('state') != 'SUCCESS':
            return jsonify({'error': '任务状态异常'}), 400

        # 获取原始XML文件路径
        files = task_status.get('result', {}).get('files', {})
        enhanced_xml_path = files.get('enhanced_xml')
        extracted_xml_path = files.get('extracted_xml')

        # 优先更新enhanced_xml，如果没有则更新extracted_xml
        target_xml_path = enhanced_xml_path or extracted_xml_path

        if not target_xml_path:
            return jsonify({'error': '未找到可更新的XML文件'}), 400

        # 处理相对路径
        if not os.path.isabs(target_xml_path):
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            target_xml_path = os.path.join(project_root, target_xml_path)

        current_app.logger.info(f"保存可视化修改到: {target_xml_path}")

        # 保存修改后的XML内容到原始文件
        with open(target_xml_path, 'w', encoding='utf-8') as f:
            f.write(xml_content)

        current_app.logger.info(f"可视化修改已保存到: {target_xml_path}")

        return jsonify({'success': True, 'message': '修改已保存'})

    except Exception as e:
        current_app.logger.error(f"保存可视化修改失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/save-xml-and-generate-fingering', methods=['POST'])
def save_xml_and_generate_fingering():
    """保存修改后的XML并生成指法"""
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        xml_content = data.get('xml_content')

        if not task_id or not xml_content:
            return jsonify({'error': '缺少必要参数'}), 400

        # 保存修改后的XML
        from app.core.task_manager import task_manager
        task_status = task_manager.get_task_status(task_id)

        if task_status.get('state') != 'SUCCESS':
            return jsonify({'error': '任务状态异常'}), 400

        # 保存用户修改的XML
        edited_xml_path = os.path.join(
            current_app.config['OUTPUT_FOLDER'],
            f"{task_id}_edited.xml"
        )

        with open(edited_xml_path, 'w', encoding='utf-8') as f:
            f.write(xml_content)

        # 更新任务状态
        task_status['result']['files']['edited_xml'] = edited_xml_path
        task_manager.save_status({task_id: task_status})

        # 启动指法生成
        from app.core.processor import PianoFingeringProcessor
        processor = PianoFingeringProcessor()

        # 使用修改后的XML生成指法
        fingering_result = processor.fingering.process_musicxml(edited_xml_path)

        if fingering_result['success']:
            # 渲染最终结果
            final_result = processor.musescore.render_to_png(
                fingering_result['output_path'],
                output_name=f"{task_id}_final",
                dpi=300
            )

            if final_result['success']:
                # 更新任务状态
                task_status['result']['files']['fingered_xml'] = fingering_result['output_path']
                task_status['result']['files']['final_image'] = final_result['output_path']
                task_manager.save_status({task_id: task_status})

                return jsonify({'success': True})
            else:
                return jsonify({'error': f"渲染失败: {final_result['error']}"}), 500
        else:
            return jsonify({'error': f"指法生成失败: {fingering_result['error']}"}), 500

    except Exception as e:
        current_app.logger.error(f"处理失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/test-ocr')
def test_ocr():
    """测试OCR文字识别"""
    try:
        from app.core.text_recognition import TextRecognitionEngine

        # 找到测试图片
        upload_dir = current_app.config['UPLOAD_FOLDER']
        test_image = None

        for filename in os.listdir(upload_dir):
            if '20250724_212242_aa0df4b1' in filename and filename.endswith('.jpg'):
                test_image = os.path.join(upload_dir, filename)
                break

        if not test_image:
            return jsonify({'error': '未找到测试图片'})

        # 初始化OCR引擎
        engine = TextRecognitionEngine()

        # 识别文字
        result = engine.recognize_text(test_image)

        if result['success']:
            # 格式化结果
            formatted_result = engine.format_recognition_result(result['texts'], test_image)

            return jsonify({
                'success': True,
                'image_file': os.path.basename(test_image),
                'total_texts': formatted_result['total_count'],
                'title_candidates': [{'text': t['text'], 'confidence': t['confidence']} for t in formatted_result['title_candidates']],
                'author_candidates': [{'text': t['text'], 'confidence': t['confidence']} for t in formatted_result['author_candidates']],
                'all_texts': [{'text': t['text'], 'confidence': t['confidence'], 'position': f"({t['center_x']:.0f},{t['center_y']:.0f})"} for t in formatted_result['all_texts']]
            })
        else:
            return jsonify({'success': False, 'error': result['error']})

    except Exception as e:
        current_app.logger.error(f"OCR测试失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@bp.route('/test-integration')
def test_integration():
    """测试完整的文字识别集成功能"""
    try:
        from app.core.audiveris_engine import AudiverisEngine

        # 找到测试图片
        upload_dir = current_app.config['UPLOAD_FOLDER']
        test_image = None

        for filename in os.listdir(upload_dir):
            if '20250724_212242_aa0df4b1' in filename and filename.endswith('.jpg'):
                test_image = os.path.join(upload_dir, filename)
                break

        if not test_image:
            return jsonify({'error': '未找到测试图片'})

        # 使用Audiveris引擎处理（包含文字集成）
        engine = AudiverisEngine()
        result = engine.process_image(test_image)

        if result['success']:
            response_data = {
                'success': True,
                'image_file': os.path.basename(test_image),
                'generated_files': list(result['files'].keys()),
                'text_integration': result.get('text_integration', {})
            }

            # 如果有文字集成结果，添加详细信息
            if result.get('text_integration') and result['text_integration'].get('success'):
                text_result = result['text_integration']
                response_data['text_details'] = {
                    'total_recognized': text_result.get('total_recognized', 0),
                    'added_texts_count': len(text_result.get('added_texts', [])),
                    'title_candidates': text_result.get('title_candidates', []),
                    'author_candidates': text_result.get('author_candidates', []),
                    'enhanced_file': text_result.get('enhanced_mxl_file', '')
                }

            return jsonify(response_data)
        else:
            return jsonify({'success': False, 'error': result['error']})

    except Exception as e:
        current_app.logger.error(f"集成测试失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@bp.route('/debug-xml/<task_id>')
def debug_xml(task_id):
    """调试XML文件内容"""
    try:
        from app.core.task_manager import task_manager
        task_status = task_manager.get_task_status(task_id)

        if task_status.get('state') != 'SUCCESS':
            return jsonify({'error': '任务未完成'}), 404

        files = task_status.get('result', {}).get('files', {})

        result = {
            'task_id': task_id,
            'available_files': list(files.keys()),
            'enhanced_xml_exists': 'enhanced_xml' in files,
            'extracted_xml_exists': 'extracted_xml' in files,
        }

        # 检查增强版XML文件内容
        if 'enhanced_xml' in files:
            enhanced_xml_path = files['enhanced_xml']
            if not os.path.isabs(enhanced_xml_path):
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
                enhanced_xml_path = os.path.join(project_root, enhanced_xml_path)

            if os.path.exists(enhanced_xml_path):
                with open(enhanced_xml_path, 'r', encoding='utf-8') as f:
                    enhanced_content = f.read()
                result['enhanced_xml_content_preview'] = enhanced_content[:500] + '...' if len(enhanced_content) > 500 else enhanced_content
                result['enhanced_xml_contains_chinese'] = '再次见到你' in enhanced_content
            else:
                result['enhanced_xml_file_missing'] = True

        # 检查原始XML文件内容
        if 'extracted_xml' in files:
            extracted_xml_path = files['extracted_xml']
            if not os.path.isabs(extracted_xml_path):
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
                extracted_xml_path = os.path.join(project_root, extracted_xml_path)

            if os.path.exists(extracted_xml_path):
                with open(extracted_xml_path, 'r', encoding='utf-8') as f:
                    extracted_content = f.read()
                result['extracted_xml_content_preview'] = extracted_content[:500] + '...' if len(extracted_content) > 500 else extracted_content
                result['extracted_xml_contains_chinese'] = '再次见到你' in extracted_content
            else:
                result['extracted_xml_file_missing'] = True

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"调试XML失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


