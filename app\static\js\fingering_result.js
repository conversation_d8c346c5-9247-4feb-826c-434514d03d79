/**
 * 指法生成结果页面JavaScript
 */

class FingeringResult {
    constructor() {
        this.taskId = null;
        this.taskData = null;
        this.init();
    }

    init() {
        // 从URL获取任务ID
        const urlParams = new URLSearchParams(window.location.search);
        this.taskId = urlParams.get('task_id');
        
        if (!this.taskId) {
            alert('缺少任务ID，请重新上传文件');
            window.location.href = '/upload';
            return;
        }

        this.bindEvents();
        this.loadResult();
    }

    bindEvents() {
        // 下载按钮
        document.getElementById('download-image').addEventListener('click', () => this.downloadImage());
        document.getElementById('download-xml').addEventListener('click', () => this.downloadXML());
        
        // 操作按钮
        document.getElementById('new-upload').addEventListener('click', () => this.newUpload());
        document.getElementById('re-edit').addEventListener('click', () => this.reEdit());
        document.getElementById('re-upload').addEventListener('click', () => this.reUpload());
        
        // 其他功能
        document.getElementById('copy-link').addEventListener('click', () => this.copyLink());
        document.getElementById('save-to-history').addEventListener('click', () => this.saveToHistory());
    }

    async loadResult() {
        const loadingDiv = document.getElementById('result-loading');
        const contentDiv = document.getElementById('result-content');
        const errorDiv = document.getElementById('result-error');
        
        // 显示加载状态
        loadingDiv.classList.remove('d-none');
        contentDiv.classList.add('d-none');
        errorDiv.classList.add('d-none');
        
        try {
            const response = await fetch(`/api/status/${this.taskId}`);
            const data = await response.json();
            
            if (data.state === 'SUCCESS' && data.result && data.result.files) {
                this.taskData = data;
                await this.displayResult();
            } else {
                throw new Error('任务未完成或数据不完整');
            }
        } catch (error) {
            console.error('加载结果失败:', error);
            document.getElementById('error-message').textContent = error.message;
            errorDiv.classList.remove('d-none');
        } finally {
            loadingDiv.classList.add('d-none');
        }
    }

    async displayResult() {
        const files = this.taskData.result.files;
        
        // 显示最终结果图片
        if (files.final_image) {
            const finalImg = document.getElementById('final-result');
            finalImg.src = `/api/file/${this.taskId}/final_image?t=${Date.now()}`;
            finalImg.onload = () => {
                // 获取图片信息
                document.getElementById('image-size').textContent = `${finalImg.naturalWidth} × ${finalImg.naturalHeight}`;
            };
        }
        
        // 显示对比图片
        if (files.preprocessed_image) {
            document.getElementById('original-comparison').src = `/api/file/${this.taskId}/preprocessed_image`;
        }
        
        if (files.final_image) {
            document.getElementById('fingered-comparison').src = `/api/file/${this.taskId}/final_image`;
        }
        
        // 显示处理信息
        if (this.taskData.result.total_time) {
            document.getElementById('processing-time').textContent = `${this.taskData.result.total_time.toFixed(1)}秒`;
        }
        
        // 获取文件大小
        if (files.final_image) {
            try {
                const response = await fetch(`/api/file/${this.taskId}/final_image`, { method: 'HEAD' });
                const contentLength = response.headers.get('content-length');
                if (contentLength) {
                    const sizeKB = Math.round(parseInt(contentLength) / 1024);
                    document.getElementById('file-size').textContent = `${sizeKB} KB`;
                }
            } catch (error) {
                console.error('获取文件大小失败:', error);
            }
        }
        
        // 显示内容
        document.getElementById('result-content').classList.remove('d-none');
    }

    downloadImage() {
        if (!this.taskData || !this.taskData.result.files.final_image) {
            alert('没有可下载的图片');
            return;
        }
        
        const link = document.createElement('a');
        link.href = `/api/file/${this.taskId}/final_image`;
        link.download = `fingered_score_${this.taskId}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        this.showMessage('图片下载已开始', 'success');
    }

    downloadXML() {
        if (!this.taskData || !this.taskData.result.files.fingered_xml) {
            alert('没有可下载的XML文件');
            return;
        }
        
        const link = document.createElement('a');
        link.href = `/api/file/${this.taskId}/fingered_xml`;
        link.download = `fingered_score_${this.taskId}.xml`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        this.showMessage('XML文件下载已开始', 'success');
    }

    newUpload() {
        window.location.href = '/upload';
    }

    reEdit() {
        if (confirm('确定要重新编辑XML吗？')) {
            window.location.href = `/xml-editor?task_id=${this.taskId}`;
        }
    }

    reUpload() {
        if (confirm('确定要重新上传文件吗？当前结果将丢失。')) {
            window.location.href = '/upload';
        }
    }

    copyLink() {
        const currentUrl = window.location.href;
        navigator.clipboard.writeText(currentUrl).then(() => {
            this.showMessage('链接已复制到剪贴板', 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            this.showMessage('复制失败', 'danger');
        });
    }

    async saveToHistory() {
        try {
            const response = await fetch('/api/save-to-history', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_id: this.taskId
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showMessage('已保存到历史记录', 'success');
            } else {
                throw new Error(data.error || '保存失败');
            }
        } catch (error) {
            console.error('保存到历史失败:', error);
            this.showMessage('保存失败: ' + error.message, 'danger');
        }
    }

    showMessage(message, type = 'info') {
        // 创建临时消息提示
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new FingeringResult();
});
