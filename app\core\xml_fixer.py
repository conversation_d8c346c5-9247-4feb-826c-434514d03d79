"""
MusicXML修复工具
用于修复OCR识别中的常见问题
"""
import xml.etree.ElementTree as ET
import logging
import re
from typing import Dict, List, Tuple

logger = logging.getLogger(__name__)

class MusicXMLFixer:
    """MusicXML修复器"""
    
    def __init__(self):
        self.common_durations = [1, 2, 4, 8, 16]  # 常见音符时值
        self.valid_pitches = ['C', 'D', 'E', 'F', 'G', 'A', 'B']
        
    def fix_missing_notes(self, xml_content: str) -> Dict:
        """
        修复缺失的音符
        通过分析小节的时值总和，推断可能缺失的音符
        """
        try:
            root = ET.fromstring(xml_content)
            fixes_made = 0
            
            # 获取拍号信息
            time_signature = self._get_time_signature(root)
            expected_duration = time_signature['beats'] * (4 / time_signature['beat_type']) * 4  # 转换为divisions
            
            # 检查每个小节
            for measure in root.findall('.//measure'):
                if measure.get('number') == '0':  # 跳过拍号小节
                    continue
                    
                # 计算当前小节的总时值
                current_duration = self._calculate_measure_duration(measure)
                
                if current_duration < expected_duration * 0.8:  # 如果少于期望时值的80%
                    # 添加休止符补齐
                    missing_duration = expected_duration - current_duration
                    self._add_rest(measure, missing_duration)
                    fixes_made += 1
                    logger.info(f"小节 {measure.get('number')} 添加了 {missing_duration} 时值的休止符")
            
            # 返回修复后的XML
            fixed_xml = ET.tostring(root, encoding='unicode')
            
            return {
                'success': True,
                'xml_content': fixed_xml,
                'fixes_made': fixes_made,
                'message': f'修复了 {fixes_made} 个小节的缺失音符问题'
            }
            
        except Exception as e:
            logger.error(f"修复缺失音符失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'xml_content': xml_content
            }
    
    def fix_rhythm_issues(self, xml_content: str) -> Dict:
        """
        修复节拍问题
        标准化音符时值，修复明显的节拍错误
        """
        try:
            root = ET.fromstring(xml_content)
            fixes_made = 0
            
            # 查找所有音符
            for note in root.findall('.//note'):
                duration_elem = note.find('duration')
                if duration_elem is not None:
                    current_duration = int(duration_elem.text)
                    
                    # 将异常时值标准化为最接近的常见时值
                    normalized_duration = self._normalize_duration(current_duration)
                    
                    if normalized_duration != current_duration:
                        duration_elem.text = str(normalized_duration)
                        fixes_made += 1
            
            fixed_xml = ET.tostring(root, encoding='unicode')
            
            return {
                'success': True,
                'xml_content': fixed_xml,
                'fixes_made': fixes_made,
                'message': f'标准化了 {fixes_made} 个音符的时值'
            }
            
        except Exception as e:
            logger.error(f"修复节拍问题失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'xml_content': xml_content
            }
    
    def fix_measure_numbers(self, xml_content: str) -> Dict:
        """
        修复小节编号
        将预备小节（小节0）改为小节1，其他小节依次+1
        """
        try:
            root = ET.fromstring(xml_content)
            fixes_made = 0

            # 查找所有小节
            measures = root.findall('.//measure')

            # 按照原始编号排序
            measures.sort(key=lambda m: int(m.get('number', '0')))

            # 重新编号：预备小节（0）变成1，其他小节+1
            for measure in measures:
                old_number = int(measure.get('number', '0'))
                new_number = old_number + 1
                measure.set('number', str(new_number))
                fixes_made += 1
                logger.info(f"小节编号修改: {old_number} → {new_number}")

            fixed_xml = ET.tostring(root, encoding='unicode')

            return {
                'success': True,
                'xml_content': fixed_xml,
                'fixes_made': fixes_made,
                'message': f'修改了 {fixes_made} 个小节的编号'
            }

        except Exception as e:
            logger.error(f"修复小节编号失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'xml_content': xml_content
            }

    def clean_artifacts(self, xml_content: str) -> Dict:
        """
        清理识别错误
        移除明显的OCR错误，如异常短的音符、无效的音高等
        """
        try:
            root = ET.fromstring(xml_content)
            fixes_made = 0
            
            # 查找并移除异常短的音符（可能是噪点）
            notes_to_remove = []
            for note in root.findall('.//note'):
                duration_elem = note.find('duration')
                if duration_elem is not None:
                    duration = int(duration_elem.text)
                    if duration < 1:  # 异常短的音符
                        notes_to_remove.append(note)
                        fixes_made += 1
            
            # 移除异常音符
            for note in notes_to_remove:
                parent = note.getparent()
                if parent is not None:
                    parent.remove(note)
            
            # 清理异常的音高
            for pitch in root.findall('.//pitch'):
                step_elem = pitch.find('step')
                if step_elem is not None:
                    step = step_elem.text
                    if step not in self.valid_pitches:
                        # 修正为最接近的有效音高
                        corrected_step = self._correct_pitch_step(step)
                        step_elem.text = corrected_step
                        fixes_made += 1
            
            fixed_xml = ET.tostring(root, encoding='unicode')
            
            return {
                'success': True,
                'xml_content': fixed_xml,
                'fixes_made': fixes_made,
                'message': f'清理了 {fixes_made} 个识别错误'
            }
            
        except Exception as e:
            logger.error(f"清理识别错误失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'xml_content': xml_content
            }
    
    def _get_time_signature(self, root) -> Dict:
        """获取拍号信息"""
        beats_elem = root.find('.//time/beats')
        beat_type_elem = root.find('.//time/beat-type')
        
        beats = int(beats_elem.text) if beats_elem is not None else 4
        beat_type = int(beat_type_elem.text) if beat_type_elem is not None else 4
        
        return {'beats': beats, 'beat_type': beat_type}
    
    def _calculate_measure_duration(self, measure) -> int:
        """计算小节的总时值"""
        total_duration = 0
        for note in measure.findall('.//note'):
            duration_elem = note.find('duration')
            if duration_elem is not None:
                total_duration += int(duration_elem.text)
        return total_duration
    
    def _add_rest(self, measure, duration: int):
        """在小节中添加休止符"""
        # 创建休止符元素
        rest_note = ET.Element('note')
        
        # 添加休止符标记
        rest_elem = ET.SubElement(rest_note, 'rest')
        
        # 添加时值
        duration_elem = ET.SubElement(rest_note, 'duration')
        duration_elem.text = str(int(duration))
        
        # 添加声部信息
        voice_elem = ET.SubElement(rest_note, 'voice')
        voice_elem.text = '1'
        
        # 添加到小节末尾
        measure.append(rest_note)
    
    def _normalize_duration(self, duration: int) -> int:
        """将时值标准化为最接近的常见时值"""
        # 基于4分音符为4个单位的标准
        standard_durations = [16, 8, 4, 2, 1]  # 全音符到16分音符
        
        # 找到最接近的标准时值
        closest_duration = min(standard_durations, key=lambda x: abs(x - duration))
        return closest_duration
    
    def _correct_pitch_step(self, step: str) -> str:
        """修正无效的音高"""
        if not step or len(step) == 0:
            return 'C'
        
        # 如果是数字或特殊字符，转换为最接近的音高
        step_upper = step.upper()
        if step_upper in self.valid_pitches:
            return step_upper
        
        # 简单的字符映射
        char_to_pitch = {
            '0': 'C', '1': 'D', '2': 'E', '3': 'F', '4': 'G', '5': 'A', '6': 'B',
            'H': 'B',  # 德语音名
            'I': 'A', 'J': 'A', 'K': 'B', 'L': 'C', 'M': 'D', 'N': 'E',
            'O': 'F', 'P': 'G', 'Q': 'A', 'R': 'B', 'S': 'C', 'T': 'D',
            'U': 'E', 'V': 'F', 'W': 'G', 'X': 'A', 'Y': 'B', 'Z': 'C'
        }
        
        return char_to_pitch.get(step_upper[0], 'C')
