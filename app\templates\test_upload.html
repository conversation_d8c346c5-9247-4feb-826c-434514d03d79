<!DOCTYPE html>
<html>
<head>
    <title>上传测试</title>
</head>
<body>
    <h1>简化上传测试</h1>
    
    <form id="uploadForm">
        <input type="file" id="fileInput" accept="image/*" required>
        <button type="submit">上传测试</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('result');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '❌ 请选择文件';
                return;
            }
            
            const file = fileInput.files[0];
            console.log('📁 选中文件:', file.name, file.size, 'bytes');
            
            const formData = new FormData();
            formData.append('file', file);
            formData.append('test', 'true');
            
            resultDiv.innerHTML = '⏳ 上传中...';
            
            try {
                console.log('🚀 开始上传...');
                
                const response = await fetch('/api/upload_simple', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('📊 响应状态:', response.status);
                console.log('📊 响应OK:', response.ok);
                
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('✅ 响应数据:', data);
                
                if (data.success) {
                    resultDiv.innerHTML = `✅ 上传成功！<br>任务ID: ${data.task_id}<br>消息: ${data.message}`;
                } else {
                    resultDiv.innerHTML = `❌ 上传失败: ${data.error}`;
                }
                
            } catch (error) {
                console.error('❌ 上传错误:', error);
                resultDiv.innerHTML = `❌ 上传失败: ${error.message}`;
            }
        });
    </script>
</body>
</html>
