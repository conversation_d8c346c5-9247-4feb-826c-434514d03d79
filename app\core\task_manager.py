"""
简单的任务状态管理器
避免Celery复杂的状态查询问题
"""
import json
import os
import time
from datetime import datetime, timedelta

class SimpleTaskManager:
    """简单任务状态管理器"""
    
    def __init__(self):
        self.status_file = os.path.join('temp', 'task_status.json')
        self.ensure_status_file()
    
    def ensure_status_file(self):
        """确保状态文件存在"""
        os.makedirs(os.path.dirname(self.status_file), exist_ok=True)
        if not os.path.exists(self.status_file):
            self.save_status({})
    
    def load_status(self):
        """加载任务状态"""
        try:
            with open(self.status_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {}
    
    def save_status(self, status_data):
        """保存任务状态"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存任务状态失败: {e}")
    
    def update_task_status(self, task_id, state, status, progress=0, **kwargs):
        """更新任务状态"""
        all_status = self.load_status()

        # 确保所有值都是可序列化的
        safe_kwargs = {}
        for key, value in kwargs.items():
            safe_kwargs[key] = self._make_serializable(value)

        all_status[task_id] = {
            'state': state,
            'status': status,
            'progress': progress,
            'updated_at': datetime.now().isoformat(),
            **safe_kwargs
        }

        self.save_status(all_status)

    def _make_serializable(self, obj):
        """递归地使对象可序列化"""
        if obj is None:
            return None
        elif isinstance(obj, (str, int, float, bool)):
            return obj
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        else:
            # 对于其他类型，尝试JSON序列化测试
            try:
                json.dumps(obj)
                return obj
            except (TypeError, ValueError):
                # 如果不能序列化，转换为字符串
                return str(obj)

    def get_task_status(self, task_id):
        """获取任务状态"""
        all_status = self.load_status()
        
        if task_id in all_status:
            task_status = all_status[task_id]
            
            # 检查任务是否超时（超过10分钟认为失败）
            try:
                updated_at = datetime.fromisoformat(task_status['updated_at'])
                if datetime.now() - updated_at > timedelta(minutes=10):
                    if task_status['state'] not in ['SUCCESS', 'FAILURE']:
                        task_status['state'] = 'FAILURE'
                        task_status['status'] = '任务超时'
                        task_status['error'] = '任务执行超时'
            except:
                pass
            
            return task_status
        
        return {
            'state': 'PENDING',
            'status': '等待处理...',
            'progress': 0
        }
    
    def cleanup_old_tasks(self, hours=24):
        """清理旧任务状态"""
        all_status = self.load_status()
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        cleaned_status = {}
        for task_id, task_data in all_status.items():
            try:
                updated_at = datetime.fromisoformat(task_data['updated_at'])
                if updated_at > cutoff_time:
                    cleaned_status[task_id] = task_data
            except:
                # 如果时间解析失败，保留任务
                cleaned_status[task_id] = task_data
        
        self.save_status(cleaned_status)
        return len(all_status) - len(cleaned_status)

# 全局任务管理器实例
task_manager = SimpleTaskManager()
