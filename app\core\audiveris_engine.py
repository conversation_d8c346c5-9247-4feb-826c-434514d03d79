"""
Audiveris乐谱识别引擎封装
"""
import os
import subprocess
import logging
from pathlib import Path
# 移除复杂的配置，使用Audiveris默认参数
from flask import current_app

logger = logging.getLogger(__name__)

class AudiverisEngine:
    """Audiveris乐谱识别引擎"""
    
    def __init__(self, config=None):
        if config:
            self.audiveris_path = config.get('AUDIVERIS_PATH')
            self.output_folder = config.get('OUTPUT_FOLDER', 'output')
        else:
            self.audiveris_path = current_app.config.get('AUDIVERIS_PATH')
            self.output_folder = current_app.config.get('OUTPUT_FOLDER')

        # 延迟初始化文字集成引擎
        self.text_integration = None
        
    def process_image(self, image_path, output_name=None):
        """
        处理图片，生成MusicXML文件（使用Audiveris默认参数）

        Args:
            image_path (str): 输入图片路径
            output_name (str): 输出文件名（不含扩展名）

        Returns:
            dict: 处理结果，包含状态和文件路径
        """
        try:
            # 转换为绝对路径
            abs_image_path = os.path.abspath(image_path)
            abs_output_path = os.path.abspath(self.output_folder)

            if not os.path.exists(abs_image_path):
                raise FileNotFoundError(f"输入文件不存在: {abs_image_path}")

            if not os.path.exists(self.audiveris_path):
                raise FileNotFoundError(f"Audiveris程序不存在: {self.audiveris_path}")

            # 确保输出目录存在
            os.makedirs(abs_output_path, exist_ok=True)

            # 生成输出文件名
            if output_name is None:
                output_name = Path(abs_image_path).stem

            # 构建命令行参数（启用文字识别，仅使用中文语言包）
            cmd = [
                self.audiveris_path,
                '-batch',
                '-transcribe',  # 完整转录，包括所有步骤
                '-export',
                '-constant', 'OCR_LANGUAGES=chi_sim',  # 仅使用中文简体进行文字识别
                '-constant', 'TEXT_RECOGNITION=true',  # 强制启用文字识别
                '-output', abs_output_path,
                abs_image_path
            ]

            logger.info("使用Audiveris完整转录模式（仅中文文字识别）")

            logger.info(f"执行Audiveris命令: {' '.join(cmd)}")
            logger.info(f"输入文件: {abs_image_path}")
            logger.info(f"输出目录: {abs_output_path}")

            # 执行Audiveris（使用项目根目录作为工作目录）
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5分钟超时
                cwd=os.getcwd()  # 使用当前工作目录
            )
            
            if result.returncode != 0:
                logger.error(f"Audiveris执行失败: {result.stderr}")
                return {
                    'success': False,
                    'error': f"Audiveris处理失败: {result.stderr}",
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
            
            # 查找生成的文件
            expected_files = {
                'mxl': os.path.join(self.output_folder, f"{output_name}.mxl"),
                'xml': os.path.join(self.output_folder, f"{output_name}.xml"),
                'omr': os.path.join(self.output_folder, f"{output_name}.omr")
            }
            
            generated_files = {}
            for file_type, file_path in expected_files.items():
                if os.path.exists(file_path):
                    generated_files[file_type] = file_path
                    
            if not generated_files:
                return {
                    'success': False,
                    'error': '未找到生成的输出文件',
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
            
            logger.info(f"Audiveris处理成功，生成文件: {list(generated_files.keys())}")

            # 集成文字识别结果
            text_integration_result = None
            if 'mxl' in generated_files:
                try:
                    # 延迟导入和初始化文字集成引擎
                    if self.text_integration is None:
                        from app.core.text_integration import TextIntegrationEngine
                        self.text_integration = TextIntegrationEngine()

                    logger.info("开始集成PaddleOCR文字识别结果")
                    text_integration_result = self.text_integration.process_with_text_integration(
                        image_path, abs_output_path
                    )
                    if text_integration_result['success']:
                        logger.info(f"文字集成成功，添加了 {len(text_integration_result['added_texts'])} 个文字信息")
                        # 添加增强版文件到结果中
                        if 'enhanced_mxl_file' in text_integration_result:
                            generated_files['enhanced_mxl'] = text_integration_result['enhanced_mxl_file']
                        if 'enhanced_xml_file' in text_integration_result:
                            generated_files['enhanced_xml'] = text_integration_result['enhanced_xml_file']
                    else:
                        logger.warning(f"文字集成失败: {text_integration_result['error']}")
                except Exception as e:
                    logger.error(f"文字集成异常: {e}")
                    text_integration_result = {'success': False, 'error': str(e)}

            return {
                'success': True,
                'files': generated_files,
                'text_integration': text_integration_result,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
        except subprocess.TimeoutExpired:
            logger.error("Audiveris处理超时")
            return {
                'success': False,
                'error': 'Audiveris处理超时（超过5分钟）'
            }
        except Exception as e:
            logger.error(f"Audiveris处理异常: {str(e)}")
            return {
                'success': False,
                'error': f'处理异常: {str(e)}'
            }
    
    def is_available(self):
        """检查Audiveris是否可用"""
        return os.path.exists(self.audiveris_path)
    
    def get_version(self):
        """获取Audiveris版本信息"""
        try:
            result = subprocess.run(
                [self.audiveris_path, '-version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.stdout.strip() if result.returncode == 0 else None
        except:
            return None
