"""
文字识别模块
使用PaddleOCR识别乐谱图片中的文字信息
"""

import os
import logging
from typing import Dict, List, Tuple, Optional, Any
import cv2
import numpy as np

logger = logging.getLogger(__name__)

class TextRecognitionEngine:
    """文字识别引擎"""
    
    def __init__(self):
        self.ocr = None
        self._initialize_ocr()
    
    def _initialize_ocr(self):
        """初始化PaddleOCR"""
        try:
            from paddleocr import PaddleOCR
            # 使用中文模型，启用文字方向分类
            self.ocr = PaddleOCR(
                use_textline_orientation=True,  # 启用文字方向分类
                lang='ch'                       # 中文模型
            )
            logger.info("PaddleOCR初始化成功")
        except Exception as e:
            logger.error(f"PaddleOCR初始化失败: {e}")
            self.ocr = None
    
    def recognize_text(self, image_path: str) -> Dict:
        """
        识别图片中的文字
        
        Args:
            image_path: 图片路径
            
        Returns:
            包含识别结果的字典
        """
        if not self.ocr:
            return {
                'success': False,
                'error': 'OCR引擎未初始化',
                'texts': []
            }
        
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                return {
                    'success': False,
                    'error': f'图片文件不存在: {image_path}',
                    'texts': []
                }
            
            logger.info(f"开始识别图片文字: {image_path}")
            
            # 使用PaddleOCR识别文字 - 正确的API调用方式
            result = self.ocr.ocr(image_path)

            logger.info(f"PaddleOCR原始结果类型: {type(result)}")
            logger.info(f"PaddleOCR原始结果长度: {len(result) if result else 0}")
            if result and len(result) > 0:
                logger.info(f"第一页结果类型: {type(result[0])}")
                logger.info(f"第一页结果长度: {len(result[0]) if result[0] else 0}")
                logger.info(f"第一页结果内容: {result[0]}")
                if result[0] is not None:
                    try:
                        if hasattr(result[0], '__len__') and len(result[0]) > 0:
                            logger.info(f"第一个元素: {result[0][0]}")
                            logger.info(f"第一个元素类型: {type(result[0][0])}")
                            if len(result[0]) > 1:
                                logger.info(f"第二个元素: {result[0][1]}")
                                logger.info(f"第二个元素类型: {type(result[0][1])}")
                        else:
                            logger.info("第一页结果为空")
                    except (KeyError, IndexError, TypeError) as e:
                        logger.info(f"访问第一页结果元素时出错: {e}")
                else:
                    logger.info("第一页结果为None")

            # 解析识别结果
            texts = []
            if result and len(result) > 0 and result[0]:
                ocr_result = result[0]

                # 检查是否是字典格式（新版PaddleOCR）
                if isinstance(ocr_result, dict):
                    logger.info("检测到字典格式的OCR结果")

                    rec_texts = ocr_result.get('rec_texts', [])
                    rec_scores = ocr_result.get('rec_scores', [])
                    rec_polys = ocr_result.get('rec_polys', [])

                    logger.info(f"找到 {len(rec_texts)} 个文字区域")

                    for i, (text_content, confidence) in enumerate(zip(rec_texts, rec_scores)):
                        if text_content and confidence > 0.1:  # 降低置信度阈值
                            logger.info(f"文字 {i+1}: '{text_content}', 置信度: {confidence}")

                            # 获取边界框
                            bbox = rec_polys[i] if i < len(rec_polys) else None

                            if bbox is not None and hasattr(bbox, '__len__') and len(bbox) >= 4:
                                # 计算文字区域的中心点和尺寸
                                if hasattr(bbox, 'tolist'):
                                    bbox_list = bbox.tolist()
                                else:
                                    bbox_list = list(bbox) if hasattr(bbox, '__iter__') else None

                                if bbox_list:
                                    # bbox格式: [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
                                    x_coords = [point[0] for point in bbox_list]
                                    y_coords = [point[1] for point in bbox_list]

                                    center_x = sum(x_coords) / len(x_coords)
                                    center_y = sum(y_coords) / len(y_coords)
                                    width = max(x_coords) - min(x_coords)
                                    height = max(y_coords) - min(y_coords)
                                else:
                                    center_x = center_y = width = height = 0
                                    bbox_list = None
                            else:
                                # 如果没有边界框信息，使用默认值
                                center_x = center_y = width = height = 0
                                bbox_list = None

                            texts.append({
                                'text': text_content,
                                'confidence': float(confidence),
                                'bbox': bbox_list,
                                'center_x': center_x,
                                'center_y': center_y,
                                'width': width,
                                'height': height
                            })

                # 兼容旧版格式（列表格式）
                elif isinstance(ocr_result, list):
                    logger.info("检测到列表格式的OCR结果")
                    logger.info(f"找到 {len(ocr_result)} 个原始文字区域")

                    for i, line in enumerate(ocr_result):
                        if line and len(line) >= 2:
                            try:
                                bbox = line[0]  # 边界框
                                text_info = line[1]  # 文字信息

                                # 处理文字信息
                                if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                                    text_content = text_info[0]
                                    confidence = text_info[1]
                                else:
                                    logger.warning(f"文字信息格式不正确: {text_info}")
                                    continue

                                logger.info(f"文字 {i+1}: '{text_content}', 置信度: {confidence}")

                                if text_content and confidence > 0.1:
                                    # 计算文字区域的中心点和尺寸
                                    if bbox and len(bbox) >= 4:
                                        x_coords = [point[0] for point in bbox]
                                        y_coords = [point[1] for point in bbox]

                                        center_x = sum(x_coords) / len(x_coords)
                                        center_y = sum(y_coords) / len(y_coords)
                                        width = max(x_coords) - min(x_coords)
                                        height = max(y_coords) - min(y_coords)
                                        bbox_list = bbox
                                    else:
                                        center_x = center_y = width = height = 0
                                        bbox_list = None

                                    texts.append({
                                        'text': text_content,
                                        'confidence': float(confidence),
                                        'bbox': bbox_list,
                                        'center_x': center_x,
                                        'center_y': center_y,
                                        'width': width,
                                        'height': height
                                    })
                            except Exception as e:
                                logger.warning(f"解析OCR结果行时出错: {e}, 行数据: {line}")
                                continue
            
            logger.info(f"识别完成，共找到 {len(texts)} 个文字区域")
            
            return {
                'success': True,
                'texts': texts,
                'total_count': len(texts)
            }
            
        except Exception as e:
            logger.error(f"文字识别失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return {
                'success': False,
                'error': str(e),
                'texts': []
            }
    
    def classify_texts(self, texts: List[Dict]) -> Dict:
        """
        对识别的文字进行分类

        Args:
            texts: 识别的文字列表

        Returns:
            分类后的文字字典
        """
        if not texts:
            return {
                'title': None,
                'subtitle': None,
                'composer': None,
                'lyricist': None,
                'other_texts': []
            }

        # 1. 首先合并相邻的文字片段
        merged_texts = self._merge_adjacent_texts(texts)
        logger.info(f"文字合并: 原始{len(texts)}个片段 -> 合并后{len(merged_texts)}个片段")

        # 按位置和大小对文字进行分类
        # 获取图片尺寸信息（通过文字位置推断）
        max_y = max(text['center_y'] for text in merged_texts) if merged_texts else 0
        min_y = min(text['center_y'] for text in merged_texts) if merged_texts else 0

        # 按y坐标排序（从上到下）
        sorted_texts = sorted(merged_texts, key=lambda x: x['center_y'])
        
        result = {
            'title': None,
            'subtitle': None,
            'composer': None,
            'arranger': None,  # 添加缺失的arranger字段
            'lyricist': None,
            'other_texts': []
        }
        
        for i, text_info in enumerate(sorted_texts):
            text = text_info['text'].strip()
            confidence = text_info['confidence']

            logger.debug(f"处理文字 {i}: '{text}' (置信度: {confidence:.3f})")

            # 跳过置信度太低的文字（降低阈值以捕获更多编曲信息）
            if confidence < 0.2:
                logger.debug(f"跳过低置信度文字: '{text}' (置信度: {confidence:.3f})")
                continue

            # 计算相对位置
            y_position = text_info['center_y']
            relative_y = (y_position - min_y) / (max_y - min_y) if max_y > min_y else 0

            # 根据位置和内容特征分类
            # 1. 标题识别（通常在顶部，字体较大）
            if relative_y < 0.25 and not result['title']:
                # 过滤掉明显不是标题的文字
                if len(text) > 1 and not any(keyword in text for keyword in ['作词', '作曲', '演唱', '原唱', '编曲']):
                    result['title'] = text
                    continue

            # 2. 副标题识别（在标题下方，但仍在上半部分）
            elif relative_y < 0.5 and result['title'] and not result['subtitle']:
                # 副标题通常包含中文描述或英文副标题
                if len(text) > 1 and not any(keyword in text for keyword in ['作词', '作曲', '演唱', '原唱', '编曲']):
                    result['subtitle'] = text
                    continue

            # 3. 作者信息识别（区分作曲、编曲、作词）
            if '作曲' in text and '编曲' not in text:
                # 纯作曲信息
                if not result['composer']:
                    result['composer'] = text
                    continue
            elif '编曲' in text:
                # 编曲信息（可能包含作曲）
                if not result['arranger']:
                    result['arranger'] = text
                    logger.info(f"识别到编曲信息: {text}")
                    continue
            elif '作词' in text:
                # 作词信息
                if not result['lyricist']:
                    result['lyricist'] = text
                    continue

            # 4. 原唱/演唱者信息识别（必须包含关键词）
            if any(keyword in text for keyword in ['原唱', '演唱', '歌手']):
                if not result['lyricist']:
                    result['lyricist'] = text
                    continue

            # 5. 其他文字
            result['other_texts'].append({
                'text': text,
                'confidence': confidence,
                'position': f"({text_info['center_x']:.0f}, {text_info['center_y']:.0f})",
                'relative_y': relative_y
            })

        # 6. 后处理：尝试从other_texts中提取遗漏的编曲信息
        if not result['arranger']:
            result['arranger'] = self._extract_arranger_from_other_texts(result['other_texts'])
            if result['arranger']:
                logger.info(f"从other_texts中提取到编曲信息: {result['arranger']}")

        return result

    def _merge_adjacent_texts(self, texts: List[Dict]) -> List[Dict]:
        """
        合并相邻的文字片段，特别是同一行被拆分的文字

        Args:
            texts: 原始文字列表

        Returns:
            合并后的文字列表
        """
        if not texts:
            return texts

        # 按y坐标排序
        sorted_texts = sorted(texts, key=lambda x: x['center_y'])
        merged_texts = []

        i = 0
        while i < len(sorted_texts):
            current_text = sorted_texts[i]
            merged_group = [current_text]

            # 查找同一行的其他文字片段
            j = i + 1
            while j < len(sorted_texts):
                next_text = sorted_texts[j]

                # 判断是否在同一行：y坐标差异小于阈值
                y_diff = abs(next_text['center_y'] - current_text['center_y'])
                if y_diff <= 20:  # 20像素的容差
                    merged_group.append(next_text)
                    j += 1
                else:
                    break

            # 如果找到多个片段，按x坐标排序后合并
            if len(merged_group) > 1:
                merged_group.sort(key=lambda x: x['center_x'])
                merged_text = self._merge_text_group(merged_group)
                merged_texts.append(merged_text)
                logger.info(f"合并文字行: {[t['text'] for t in merged_group]} -> '{merged_text['text']}'")
            else:
                merged_texts.append(current_text)

            i = j

        return merged_texts

    def _merge_text_group(self, text_group: List[Dict]) -> Dict:
        """
        将同一行的多个文字片段合并为一个

        Args:
            text_group: 同一行的文字片段列表（已按x坐标排序）

        Returns:
            合并后的文字字典
        """
        if not text_group:
            return {}

        if len(text_group) == 1:
            return text_group[0]

        # 合并文字内容
        merged_text = ""
        for i, text_info in enumerate(text_group):
            text = text_info['text'].strip()
            if i == 0:
                merged_text = text
            else:
                # 检查是否需要添加空格
                if (merged_text and text and
                    not merged_text.endswith(('：', ':', '/', '、', '，', '。')) and
                    not text.startswith(('：', ':', '/', '、', '，', '。'))):
                    # 如果前一个文字是中文/英文，后一个也是，可能需要连接
                    if self._should_connect_texts(merged_text, text):
                        merged_text += text  # 直接连接，不加空格
                    else:
                        merged_text += " " + text  # 添加空格
                else:
                    merged_text += text  # 直接连接

        # 计算合并后的位置和置信度
        avg_center_x = sum(t['center_x'] for t in text_group) / len(text_group)
        avg_center_y = sum(t['center_y'] for t in text_group) / len(text_group)
        avg_confidence = sum(t['confidence'] for t in text_group) / len(text_group)

        # 计算合并后的边界框
        min_x = min(t.get('bbox', [[0,0],[0,0],[0,0],[0,0]])[0][0] for t in text_group)
        max_x = max(t.get('bbox', [[0,0],[0,0],[0,0],[0,0]])[1][0] for t in text_group)
        min_y = min(t.get('bbox', [[0,0],[0,0],[0,0],[0,0]])[0][1] for t in text_group)
        max_y = max(t.get('bbox', [[0,0],[0,0],[0,0],[0,0]])[2][1] for t in text_group)

        return {
            'text': merged_text.strip(),
            'confidence': avg_confidence,
            'center_x': avg_center_x,
            'center_y': avg_center_y,
            'bbox': [[min_x, min_y], [max_x, min_y], [max_x, max_y], [min_x, max_y]],
            'merged_from': len(text_group)  # 记录合并的片段数量
        }

    def _should_connect_texts(self, text1: str, text2: str) -> bool:
        """
        判断两个文字片段是否应该直接连接（不加空格）

        Args:
            text1: 前一个文字
            text2: 后一个文字

        Returns:
            是否应该直接连接
        """
        # 中文字符直接连接
        if self._is_chinese_char(text1[-1]) and self._is_chinese_char(text2[0]):
            return True

        # 数字和单位连接
        if text1.isdigit() and text2 in ['年', '月', '日', '号', '版']:
            return True

        # 特殊符号连接
        if text1.endswith(('/', '：', ':')) or text2.startswith(('/', '：', ':')):
            return True

        # 英文单词之间需要空格，但某些情况除外
        if text1.isalpha() and text2.isalpha():
            # 检查是否是常见的连接词
            common_connections = [
                ('Once', 'Again'), ('Piano', 'Version'), ('Music', 'Box'),
                ('Theme', 'Song'), ('Original', 'Soundtrack')
            ]
            for conn in common_connections:
                if text1.endswith(conn[0]) and text2.startswith(conn[1]):
                    return False  # 需要空格
            return False  # 英文单词间需要空格

        return True  # 默认直接连接

    def _is_chinese_char(self, char: str) -> bool:
        """判断是否为中文字符"""
        return '\u4e00' <= char <= '\u9fff'

    def _extract_arranger_from_other_texts(self, other_texts: List[Dict]) -> str:
        """
        从other_texts中提取编曲信息
        专门处理被拆分的编曲信息

        Args:
            other_texts: 其他文字列表

        Returns:
            编曲信息字符串，如果没有找到返回空字符串
        """
        if not other_texts:
            return ""

        # 1. 查找包含编曲相关关键词的文字片段
        arranger_keywords = ['编曲', '钢琴编曲', '改编', '编配']
        platform_keywords = ['网易音乐人', '抖音', '小红书', 'B站', 'bilibili', '微博', '音乐平台']

        arranger_candidates = []

        # 收集可能的编曲信息片段
        for text_info in other_texts:
            text = text_info['text'].strip()

            # 直接包含编曲关键词的文字
            if any(keyword in text for keyword in arranger_keywords):
                arranger_candidates.append(text_info)
                logger.info(f"找到编曲关键词片段: '{text}'")

            # 包含平台关键词的文字（可能是编曲信息的一部分）
            elif any(keyword in text for keyword in platform_keywords):
                arranger_candidates.append(text_info)
                logger.info(f"找到平台关键词片段: '{text}'")

            # 包含冒号的文字（可能是编曲者名称）
            elif '：' in text or ':' in text:
                # 检查冒号前是否有编曲相关词汇
                colon_parts = text.replace(':', '：').split('：')
                if len(colon_parts) >= 2:
                    prefix = colon_parts[0].strip()
                    if any(keyword in prefix for keyword in arranger_keywords):
                        arranger_candidates.append(text_info)
                        logger.info(f"找到冒号分隔的编曲片段: '{text}'")

        if not arranger_candidates:
            return ""

        # 2. 如果只有一个候选，直接返回
        if len(arranger_candidates) == 1:
            return arranger_candidates[0]['text'].strip()

        # 3. 如果有多个候选，尝试合并相邻的片段
        # 按位置排序
        arranger_candidates.sort(key=lambda x: (x.get('center_y', 0), x.get('center_x', 0)))

        # 尝试合并相邻的片段
        merged_arranger_text = ""
        for i, candidate in enumerate(arranger_candidates):
            text = candidate['text'].strip()

            if i == 0:
                merged_arranger_text = text
            else:
                # 检查是否与前一个片段相邻
                prev_candidate = arranger_candidates[i-1]
                y_diff = abs(candidate.get('center_y', 0) - prev_candidate.get('center_y', 0))

                # 如果在同一行或相邻行
                if y_diff <= 30:
                    # 添加适当的连接符
                    if not merged_arranger_text.endswith(('：', ':', '/', ' ')):
                        if (merged_arranger_text and text and
                            self._is_chinese_char(merged_arranger_text[-1]) and self._is_chinese_char(text[0])):
                            merged_arranger_text += text  # 中文直接连接
                        else:
                            merged_arranger_text += " " + text  # 添加空格
                    else:
                        merged_arranger_text += text
                else:
                    # 不同行，用换行符连接
                    merged_arranger_text += "\n" + text

        # 4. 清理和验证结果
        merged_arranger_text = merged_arranger_text.strip()

        # 如果合并后的文字太短或不包含有效信息，返回最长的候选
        if len(merged_arranger_text) < 3:
            longest_candidate = max(arranger_candidates, key=lambda x: len(x['text']))
            return longest_candidate['text'].strip()

        logger.info(f"合并编曲信息: {[c['text'] for c in arranger_candidates]} -> '{merged_arranger_text}'")
        return merged_arranger_text

    def process_image(self, image_path: str) -> Dict:
        """
        处理图片，识别并分类文字
        
        Args:
            image_path: 图片路径
            
        Returns:
            处理结果
        """
        # 识别文字
        recognition_result = self.recognize_text(image_path)
        
        if not recognition_result['success']:
            return recognition_result
        
        # 分类文字
        classified_texts = self.classify_texts(recognition_result['texts'])
        
        return {
            'success': True,
            'raw_texts': recognition_result['texts'],
            'classified_texts': classified_texts,
            'total_count': recognition_result['total_count']
        }

    def format_recognition_result(self, texts: List[Dict], image_path: str) -> Dict[str, Any]:
        """
        格式化识别结果，便于后续处理

        Args:
            texts: 文字识别结果列表
            image_path: 图片路径

        Returns:
            格式化后的结果
        """
        # 使用新的分类方法
        classified_result = self.classify_texts(texts)

        # 从原始文字中提取候选项
        title_candidates = []
        author_candidates = []

        for text_info in texts:
            text = text_info['text'].strip()
            confidence = text_info.get('confidence', 0)

            # 跳过置信度太低的文字
            if confidence < 0.3:
                continue

            # 标题候选项：长度合适且不包含作者关键词
            if (len(text) > 1 and len(text) < 50 and
                not any(keyword in text for keyword in ['作词', '作曲', '演唱', '原唱', '编曲', '词', '曲'])):
                title_candidates.append(text_info)

            # 作者候选项：包含作者关键词或位置在底部
            if (any(keyword in text for keyword in ['作词', '作曲', '演唱', '原唱', '编曲', '词', '曲']) or
                len(text) > 1):
                author_candidates.append(text_info)

        # 转换为标准格式
        return {
            'title': classified_result.get('title', ''),
            'subtitle': classified_result.get('subtitle', ''),
            'composer': classified_result.get('composer', ''),
            'arranger': classified_result.get('arranger', ''),  # 添加编曲字段
            'lyricist': classified_result.get('lyricist', ''),
            'other_texts': classified_result.get('other_texts', []),
            'title_candidates': title_candidates,
            'author_candidates': author_candidates,
            'total_count': len(texts)
        }

    def filter_texts_by_confidence(self, texts: List[Dict], min_confidence: float = 0.5) -> List[Dict]:
        """
        根据置信度过滤文字识别结果

        Args:
            texts: 文字识别结果列表
            min_confidence: 最小置信度阈值

        Returns:
            过滤后的文字列表
        """
        filtered = []
        for text in texts:
            if text.get('confidence', 0) < min_confidence:
                continue

            text_content = text.get('text', '').strip()

            # 过滤乱码和无意义文本
            if self._is_garbage_text(text_content):
                continue

            filtered.append(text)

        return filtered

    def _is_garbage_text(self, text: str) -> bool:
        """判断是否为乱码或无意义文本"""
        if not text or len(text) < 1:
            return True

        # 过滤数学公式和LaTeX符号
        if ('\\frac' in text or
            '\\' in text or
            text.startswith('$') or
            text.endswith('$') or
            '{}' in text):
            return True

        # 过滤纯符号
        if all(c in '()[]{}.,;:!?-_=+*&^%$#@|/\\' for c in text):
            return True

        # 过滤过短的数字
        if text.isdigit() and len(text) == 1:
            return True

        return False

    def classify_texts_by_position(self, texts: List[Dict], image_height: int) -> Dict[str, List[Dict]]:
        """
        根据位置对文字进行分类

        Args:
            texts: 文字识别结果列表
            image_height: 图片高度

        Returns:
            分类后的文字字典 {'title': [], 'subtitle': [], 'footer': [], 'other': []}
        """
        classified = {
            'title': [],      # 标题（顶部区域）
            'subtitle': [],   # 副标题（上部区域）
            'footer': [],     # 页脚信息（底部区域）
            'other': []       # 其他位置
        }

        # 定义区域边界（相对于图片高度的比例）
        title_threshold = image_height * 0.15      # 顶部15%
        subtitle_threshold = image_height * 0.3    # 顶部30%
        footer_threshold = image_height * 0.85     # 底部15%

        for text in texts:
            center_y = text.get('center_y', 0)

            if center_y <= title_threshold:
                classified['title'].append(text)
            elif center_y <= subtitle_threshold:
                classified['subtitle'].append(text)
            elif center_y >= footer_threshold:
                classified['footer'].append(text)
            else:
                classified['other'].append(text)

        return classified

    def get_image_dimensions(self, image_path: str) -> tuple:
        """
        获取图片尺寸

        Args:
            image_path: 图片路径

        Returns:
            (width, height) 或 (0, 0) 如果失败
        """
        try:
            import cv2
            image = cv2.imread(image_path)
            if image is not None:
                height, width = image.shape[:2]
                return width, height
            return 0, 0
        except Exception as e:
            logger.error(f"获取图片尺寸失败: {e}")
            return 0, 0


def create_text_recognition_engine():
    """创建文字识别引擎实例"""
    return TextRecognitionEngine()
